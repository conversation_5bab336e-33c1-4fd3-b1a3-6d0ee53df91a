{"c": ["pages/_app", "webpack"], "r": ["pages/accounting/accounts", "pages/accounting/reports", "pages/accounting/cash-flow", "pages/accounting/expenses", "pages/accounting/customer-statements", "pages/accounting/installments"], "m": ["./node_modules/@radix-ui/react-dialog/dist/index.mjs", "./node_modules/@radix-ui/react-label/dist/index.mjs", "./node_modules/lucide-react/dist/esm/icons/building.js", "./node_modules/lucide-react/dist/esm/icons/more-horizontal.js", "./node_modules/lucide-react/dist/esm/icons/pie-chart.js", "./node_modules/lucide-react/dist/esm/icons/target.js", "./node_modules/lucide-react/dist/esm/icons/upload.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Caccounting%5Caccounts%5Cindex.tsx&page=%2Faccounting%2Faccounts!", "./src/components/accounting/AccountsStatistics.tsx", "./src/components/accounting/AdvancedAccountSearch.tsx", "./src/components/ui/dialog.tsx", "./src/components/ui/label.tsx", "./src/lib/services/accounts.ts", "./src/lib/utils/excel.ts", "./src/pages/accounting/accounts/index.tsx", "__barrel_optimize__?names=Activity,BarChart3,Building,CreditCard,DollarSign,PieChart,Target,TrendingDown,TrendingUp,Users!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=BarChart3,Building,CreditCard,Download,Edit,Eye,MoreHorizontal,PieChart,Plus,Trash2,TrendingDown,TrendingUp,Upload!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Filter,Search,X!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Caccounting%5Creports%5Cindex.tsx&page=%2Faccounting%2Freports!", "./src/pages/accounting/reports/index.tsx", "__barrel_optimize__?names=ArrowRight,BarChart3,Building,Calculator,Calendar,DollarSign,Download,FileText,PieChart,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Caccounting%5Ccash-flow.tsx&page=%2Faccounting%2Fcash-flow!", "./src/pages/accounting/cash-flow.tsx", "__barrel_optimize__?names=CreditCard,DollarSign,Plus,TrendingDown,TrendingUp!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/lucide-react/dist/esm/icons/car.js", "./node_modules/lucide-react/dist/esm/icons/zap.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Caccounting%5Cexpenses%5Cindex.tsx&page=%2Faccounting%2Fexpenses!", "./src/components/ui/textarea.tsx", "./src/pages/accounting/expenses/index.tsx", "__barrel_optimize__?names=Building,Calendar,Car,DollarSign,Download,Edit,Filter,Plus,Receipt,Search,ShoppingCart,Trash2,TrendingUp,Users,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/lucide-react/dist/esm/icons/arrow-down-circle.js", "./node_modules/lucide-react/dist/esm/icons/arrow-up-circle.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Caccounting%5Ccustomer-statements%5Cindex.tsx&page=%2Faccounting%2Fcustomer-statements!", "./src/lib/contacts.ts", "./src/pages/accounting/customer-statements/index.tsx", "__barrel_optimize__?names=ArrowDownCircle,ArrowUpCircle,DollarSign,Search,TrendingDown,TrendingUp,Truck,UserCheck,Users!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/lucide-react/dist/esm/icons/send.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Caccounting%5Cinstallments%5Cindex.tsx&page=%2Faccounting%2Finstallments!", "./src/pages/accounting/installments/index.tsx", "__barrel_optimize__?names=AlertTriangle,Bell,Calendar,CheckCircle,Clock,CreditCard,DollarSign,Download,FileText,Mail,Phone,Plus,Search,Send,Truck,User,UserCheck,Users!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}