{"c": ["webpack"], "r": ["pages/sales/invoices"], "m": ["./node_modules/lucide-react/dist/esm/icons/minus.js", "./node_modules/lucide-react/dist/esm/icons/send.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Csales%5Cinvoices%5Cindex.tsx&page=%2Fsales%2Finvoices!", "./src/components/sales/InvoiceForm.tsx", "./src/components/sales/InvoicePrint.tsx", "./src/components/sales/ProductSelector.tsx", "./src/hooks/useActivityLogger.ts", "./src/lib/documentUtils.ts", "./src/pages/sales/invoices/index.tsx", "__barrel_optimize__?names=<PERSON>ert<PERSON>riangle,ArrowRight,CheckCircle,Clock,Copy,CreditCard,DollarSign,Download,Edit,Eye,FileText,Filter,Mail,MessageCircle,Plus,Search,Send,Share2,ShoppingCart,Star,Trash2,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Calculator,Package,Plus,Trash2,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Minus,Package,Plus,Search,ShoppingCart!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}