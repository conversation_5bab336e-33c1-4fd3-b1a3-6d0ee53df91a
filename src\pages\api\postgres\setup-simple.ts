import { NextApiRequest, NextApiResponse } from 'next'
import { query } from '@/lib/postgres'
import fs from 'fs'
import path from 'path'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // قراءة ملف SQL المبسط
    const sqlFilePath = path.join(process.cwd(), 'simple_setup.sql')
    
    if (!fs.existsSync(sqlFilePath)) {
      return res.status(404).json({
        success: false,
        message: 'ملف simple_setup.sql غير موجود'
      })
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8')
    
    // تقسيم الاستعلامات بناءً على الفواصل المنقوطة
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && stmt !== '')

    console.log(`عدد الاستعلامات: ${statements.length}`)

    const results = []
    let successCount = 0
    let errorCount = 0

    // تنفيذ كل استعلام على حدة
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      
      if (statement.trim().length === 0) continue

      console.log(`تنفيذ الاستعلام ${i + 1}: ${statement.substring(0, 50)}...`)

      try {
        const result = await query(statement)
        
        if (result.success) {
          successCount++
          results.push({
            index: i + 1,
            statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
            success: true,
            message: 'تم التنفيذ بنجاح',
            rowCount: result.rowCount,
            data: result.data?.slice(0, 3) // أول 3 صفوف فقط
          })
          console.log(`✅ نجح الاستعلام ${i + 1}`)
        } else {
          errorCount++
          results.push({
            index: i + 1,
            statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
            success: false,
            error: result.error,
            message: 'فشل في التنفيذ'
          })
          console.log(`❌ فشل الاستعلام ${i + 1}:`, result.error)
        }
      } catch (error) {
        errorCount++
        const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
        results.push({
          index: i + 1,
          statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
          success: false,
          error: errorMessage,
          message: 'خطأ في التنفيذ'
        })
        console.log(`❌ خطأ في الاستعلام ${i + 1}:`, errorMessage)
      }
    }

    // فحص الجداول بعد التنفيذ
    const tablesCheck = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `)

    const tables = tablesCheck.success ? tablesCheck.data?.map(row => row.table_name) : []

    return res.status(200).json({
      success: successCount > 0,
      message: `تم تنفيذ ${successCount} استعلام بنجاح، ${errorCount} فشل`,
      summary: {
        totalStatements: statements.length,
        successful: successCount,
        failed: errorCount,
        tablesAfter: tables?.length || 0
      },
      tables: {
        created: tables,
        hasBranches: tables?.includes('branches'),
        hasUsers: tables?.includes('users'),
        hasProducts: tables?.includes('products'),
        hasWarehouses: tables?.includes('warehouses')
      },
      results,
      recommendations: successCount > 0 ? [
        '✅ تم إنشاء بعض الجداول بنجاح',
        '🎯 يمكنك الآن إنشاء مستخدم جديد',
        '🔄 تحقق من حالة الجداول'
      ] : [
        '❌ فشل في إنشاء الجداول',
        '🔍 تحقق من أخطاء قاعدة البيانات',
        '📞 تحقق من الاتصال بـ PostgreSQL'
      ]
    })

  } catch (error) {
    console.error('خطأ في إعداد قاعدة البيانات:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
