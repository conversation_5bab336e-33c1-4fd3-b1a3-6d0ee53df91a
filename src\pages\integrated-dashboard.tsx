import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  Package, 
  Users, 
  ShoppingCart,
  Truck,
  Wrench,
  TrendingUp,
  Database,
  RefreshCw,
  BarChart3
} from 'lucide-react'

interface DashboardData {
  products: any[]
  inventory: any[]
  customers: any[]
  suppliers: any[]
  salesOrders: any[]
  purchaseOrders: any[]
  stockMovements: any[]
  maintenanceRequests: any[]
}

interface Summary {
  productsCount: number
  inventoryCount: number
  customersCount: number
  suppliersCount: number
  salesOrdersCount: number
  purchaseOrdersCount: number
  stockMovementsCount: number
  maintenanceRequestsCount: number
}

export default function IntegratedDashboard() {
  const [data, setData] = useState<DashboardData | null>(null)
  const [summary, setSummary] = useState<Summary | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')

  const fetchData = async () => {
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch('/api/postgres/data?type=all')
      const result = await response.json()
      
      if (result.success) {
        setData(result.data)
        setSummary(result.summary)
      } else {
        setError(result.message || 'فشل في جلب البيانات')
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [])

  const cards = [
    {
      title: 'المنتجات',
      count: summary?.productsCount || 0,
      icon: <Package className="h-8 w-8 text-blue-600" />,
      color: 'bg-blue-50 border-blue-200',
      data: data?.products || []
    },
    {
      title: 'المخزون',
      count: summary?.inventoryCount || 0,
      icon: <Database className="h-8 w-8 text-green-600" />,
      color: 'bg-green-50 border-green-200',
      data: data?.inventory || []
    },
    {
      title: 'العملاء',
      count: summary?.customersCount || 0,
      icon: <Users className="h-8 w-8 text-purple-600" />,
      color: 'bg-purple-50 border-purple-200',
      data: data?.customers || []
    },
    {
      title: 'الموردين',
      count: summary?.suppliersCount || 0,
      icon: <Truck className="h-8 w-8 text-orange-600" />,
      color: 'bg-orange-50 border-orange-200',
      data: data?.suppliers || []
    },
    {
      title: 'أوامر المبيعات',
      count: summary?.salesOrdersCount || 0,
      icon: <ShoppingCart className="h-8 w-8 text-red-600" />,
      color: 'bg-red-50 border-red-200',
      data: data?.salesOrders || []
    },
    {
      title: 'أوامر الشراء',
      count: summary?.purchaseOrdersCount || 0,
      icon: <TrendingUp className="h-8 w-8 text-indigo-600" />,
      color: 'bg-indigo-50 border-indigo-200',
      data: data?.purchaseOrders || []
    },
    {
      title: 'حركات المخزون',
      count: summary?.stockMovementsCount || 0,
      icon: <BarChart3 className="h-8 w-8 text-teal-600" />,
      color: 'bg-teal-50 border-teal-200',
      data: data?.stockMovements || []
    },
    {
      title: 'طلبات الصيانة',
      count: summary?.maintenanceRequestsCount || 0,
      icon: <Wrench className="h-8 w-8 text-yellow-600" />,
      color: 'bg-yellow-50 border-yellow-200',
      data: data?.maintenanceRequests || []
    }
  ]

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
          <h2 className="text-xl font-semibold mb-2">جاري تحميل البيانات...</h2>
          <p className="text-gray-600">يتم جلب البيانات من قاعدة البيانات المتكاملة</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6">
      <div className="max-w-7xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            لوحة التحكم المتكاملة
          </h1>
          <p className="text-xl text-gray-600 mb-4">
            عرض شامل لجميع بيانات النظام المتكامل
          </p>
          <Button 
            onClick={fetchData}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            تحديث البيانات
          </Button>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Success Alert */}
        {data && !error && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4 text-green-600" />
            <AlertDescription className="text-green-800">
              تم تحميل البيانات بنجاح من قاعدة البيانات المتكاملة
            </AlertDescription>
          </Alert>
        )}

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {cards.map((card, index) => (
            <Card key={index} className={`${card.color} shadow-lg hover:shadow-xl transition-shadow`}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
                {card.icon}
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{card.count}</div>
                <p className="text-xs text-gray-600 mt-1">
                  {card.count > 0 ? 'عنصر متاح' : 'لا توجد عناصر'}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Data Preview */}
        {data && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Products */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Package className="h-5 w-5 mr-2" />
                  أحدث المنتجات
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.products.slice(0, 5).map((product, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b last:border-b-0">
                    <div>
                      <p className="font-medium">{product.name}</p>
                      <p className="text-sm text-gray-600">{product.sku}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{product.unit_price} ج.م</p>
                      <p className="text-sm text-gray-600">المخزون: {product.stock_quantity}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Recent Customers */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  أحدث العملاء
                </CardTitle>
              </CardHeader>
              <CardContent>
                {data.customers.slice(0, 5).map((customer, index) => (
                  <div key={index} className="flex justify-between items-center py-2 border-b last:border-b-0">
                    <div>
                      <p className="font-medium">{customer.name}</p>
                      <p className="text-sm text-gray-600">{customer.phone}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{customer.credit_limit} ج.م</p>
                      <p className="text-sm text-gray-600">الرصيد: {customer.current_balance}</p>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Integration Status */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardHeader>
            <CardTitle className="text-center text-blue-900">
              حالة التكامل
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-blue-900 mb-2">
              التكامل مكتمل بنجاح! 🎉
            </h3>
            <p className="text-blue-700">
              جميع الجداول والخدمات تعمل بتناغم كامل مع الكود
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
