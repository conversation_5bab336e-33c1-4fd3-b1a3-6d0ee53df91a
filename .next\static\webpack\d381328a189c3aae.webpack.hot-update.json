{"c": ["webpack"], "r": ["pages/sales/quotations", "pages/sales/orders"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Csales%5Cquotations%5Cindex.tsx&page=%2Fsales%2Fquotations!", "./src/components/sales/QuotationForm.tsx", "./src/components/sales/QuotationPrint.tsx", "./src/pages/sales/quotations/index.tsx", "__barrel_optimize__?names=ArrowRight,CheckCircle,Clock,Copy,Download,Edit,FileText,Filter,Mail,MessageCircle,Plus,Search,Send,Share2,ShoppingCart,Star,Trash2,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/lucide-react/dist/esm/icons/map-pin.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Csales%5Corders%5Cindex.tsx&page=%2Fsales%2Forders!", "./src/components/inventory/StockStatus.tsx", "./src/components/sales/OrderForm.tsx", "./src/components/sales/OrderPrint.tsx", "./src/pages/sales/orders/index.tsx", "__barrel_optimize__?names=<PERSON><PERSON><PERSON><PERSON>gle,ArrowRight,CheckCircle,Clock,Copy,Download,Edit,FileText,Filter,Mail,MessageCircle,Package,Plus,Search,Share2,Star,Trash2,Truck,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=AlertTriangle,CheckCircle,Package,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Calculator,MapPin,Package,Plus,Trash2,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}