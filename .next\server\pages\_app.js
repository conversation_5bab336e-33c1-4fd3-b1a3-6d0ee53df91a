/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/_app";
exports.ids = ["pages/_app"];
exports.modules = {

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// دالة للحصول على صلاحيات الدور\nconst getRolePermissions = (role)=>{\n    return _lib_auth__WEBPACK_IMPORTED_MODULE_2__.ROLE_PERMISSIONS[role] || [];\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تحميل بيانات المستخدم من localStorage\n    const loadUserFromStorage = ()=>{\n        try {\n            const savedUser = localStorage.getItem(\"user\");\n            if (savedUser) {\n                return JSON.parse(savedUser);\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error loading user from storage:\", error);\n            return null;\n        }\n    };\n    const refreshUser = async ()=>{\n        const savedUser = loadUserFromStorage();\n        if (savedUser && !savedUser.permissions) {\n            // إضافة الصلاحيات إذا لم تكن موجودة\n            const permissions = getRolePermissions(savedUser.role);\n            const userWithPermissions = {\n                ...savedUser,\n                permissions\n            };\n            localStorage.setItem(\"user\", JSON.stringify(userWithPermissions));\n            setUser(userWithPermissions);\n        } else {\n            setUser(savedUser);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/postgres/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                const userData = result.user;\n                // إضافة الصلاحيات بناءً على الدور\n                const permissions = getRolePermissions(userData.role);\n                const userWithPermissions = {\n                    ...userData,\n                    permissions\n                };\n                localStorage.setItem(\"user\", JSON.stringify(userWithPermissions));\n                setUser(userWithPermissions);\n            } else {\n                throw new Error(result.message || \"فشل في تسجيل الدخول\");\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            localStorage.removeItem(\"user\");\n            setUser(null);\n        } catch (error) {\n            console.error(\"Sign out error:\", error);\n            throw error;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل المستخدم من localStorage عند بدء التطبيق\n        const initializeAuth = async ()=>{\n            try {\n                await refreshUser();\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   PERMISSIONS_ORGANIZED: () => (/* binding */ PERMISSIONS_ORGANIZED),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n// Mock user type for demo\nconst PERMISSIONS = {\n    // User Management\n    USERS_VIEW: \"users:view\",\n    USERS_CREATE: \"users:create\",\n    USERS_EDIT: \"users:edit\",\n    USERS_DELETE: \"users:delete\",\n    // Branch Management\n    BRANCHES_VIEW: \"branches:view\",\n    BRANCHES_CREATE: \"branches:create\",\n    BRANCHES_EDIT: \"branches:edit\",\n    BRANCHES_DELETE: \"branches:delete\",\n    // Warehouse Management\n    WAREHOUSES_VIEW: \"warehouses:view\",\n    WAREHOUSES_CREATE: \"warehouses:create\",\n    WAREHOUSES_EDIT: \"warehouses:edit\",\n    WAREHOUSES_DELETE: \"warehouses:delete\",\n    // Product Management\n    PRODUCTS_VIEW: \"products:view\",\n    PRODUCTS_CREATE: \"products:create\",\n    PRODUCTS_EDIT: \"products:edit\",\n    PRODUCTS_DELETE: \"products:delete\",\n    // Sales\n    SALES_VIEW: \"sales:view\",\n    SALES_CREATE: \"sales:create\",\n    SALES_EDIT: \"sales:edit\",\n    SALES_DELETE: \"sales:delete\",\n    // Purchases\n    PURCHASES_VIEW: \"purchases:view\",\n    PURCHASES_CREATE: \"purchases:create\",\n    PURCHASES_EDIT: \"purchases:edit\",\n    PURCHASES_DELETE: \"purchases:delete\",\n    PURCHASES_APPROVE: \"purchases:approve\",\n    PURCHASES_RECEIVE: \"purchases:receive\",\n    PURCHASES_PAY: \"purchases:pay\",\n    PURCHASES_CANCEL: \"purchases:cancel\",\n    PURCHASES_PROCESS: \"purchases:process\",\n    // POS\n    POS_ACCESS: \"pos:access\",\n    POS_CLOSE_DAY: \"pos:close_day\",\n    // Cash Registers\n    CASH_REGISTERS_VIEW: \"cash_registers:view\",\n    CASH_REGISTERS_CREATE: \"cash_registers:create\",\n    CASH_REGISTERS_EDIT: \"cash_registers:edit\",\n    CASH_REGISTERS_DELETE: \"cash_registers:delete\",\n    // Accounting\n    ACCOUNTING_VIEW: \"accounting:view\",\n    ACCOUNTING_EDIT: \"accounting:edit\",\n    ACCOUNTING_MANAGE: \"accounting:manage\",\n    ACCOUNTING_CREATE: \"accounting:create\",\n    ACCOUNTING_DELETE: \"accounting:delete\",\n    // Reports\n    REPORTS_VIEW: \"reports:view\",\n    REPORTS_EXPORT: \"reports:export\"\n};\n// Organized permissions for easier use\nconst PERMISSIONS_ORGANIZED = {\n    SALES: {\n        VIEW: PERMISSIONS.SALES_VIEW,\n        CREATE: PERMISSIONS.SALES_CREATE,\n        EDIT: PERMISSIONS.SALES_EDIT,\n        DELETE: PERMISSIONS.SALES_DELETE\n    },\n    PURCHASES: {\n        VIEW: PERMISSIONS.PURCHASES_VIEW,\n        CREATE: PERMISSIONS.PURCHASES_CREATE,\n        EDIT: PERMISSIONS.PURCHASES_EDIT,\n        DELETE: PERMISSIONS.PURCHASES_DELETE,\n        APPROVE: PERMISSIONS.PURCHASES_APPROVE,\n        RECEIVE: PERMISSIONS.PURCHASES_RECEIVE,\n        PAY: PERMISSIONS.PURCHASES_PAY,\n        CANCEL: PERMISSIONS.PURCHASES_CANCEL,\n        PROCESS: PERMISSIONS.PURCHASES_PROCESS\n    }\n};\nconst ROLE_PERMISSIONS = {\n    admin: [\n        // جميع الصلاحيات للمدير\n        ...Object.values(PERMISSIONS)\n    ],\n    manager: [\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    employee: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.REPORTS_VIEW\n    ],\n    cashier: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.CASH_REGISTERS_VIEW\n    ]\n};\nasync function signIn(email, password) {\n    // Mock authentication for demo\n    await new Promise((resolve)=>setTimeout(resolve, 1000));\n    if (email && password) {\n        return {\n            user: {\n                id: \"1\",\n                email\n            }\n        };\n    }\n    throw new Error(\"Invalid credentials\");\n}\nasync function signOut() {\n    // Mock sign out\n    await new Promise((resolve)=>setTimeout(resolve, 500));\n}\nasync function getCurrentUser() {\n    // Mock user for demo - تأكد من أن جميع الصلاحيات متضمنة\n    const adminPermissions = [\n        // User Management\n        \"users:view\",\n        \"users:create\",\n        \"users:edit\",\n        \"users:delete\",\n        // Branch Management\n        \"branches:view\",\n        \"branches:create\",\n        \"branches:edit\",\n        \"branches:delete\",\n        // Warehouse Management\n        \"warehouses:view\",\n        \"warehouses:create\",\n        \"warehouses:edit\",\n        \"warehouses:delete\",\n        // Product Management\n        \"products:view\",\n        \"products:create\",\n        \"products:edit\",\n        \"products:delete\",\n        // Sales\n        \"sales:view\",\n        \"sales:create\",\n        \"sales:edit\",\n        \"sales:delete\",\n        // Purchases\n        \"purchases:view\",\n        \"purchases:create\",\n        \"purchases:edit\",\n        \"purchases:delete\",\n        \"purchases:approve\",\n        \"purchases:receive\",\n        \"purchases:pay\",\n        \"purchases:cancel\",\n        \"purchases:process\",\n        // POS\n        \"pos:access\",\n        \"pos:close_day\",\n        // Cash Registers\n        \"cash_registers:view\",\n        \"cash_registers:create\",\n        \"cash_registers:edit\",\n        \"cash_registers:delete\",\n        // Accounting\n        \"accounting:view\",\n        \"accounting:edit\",\n        \"accounting:manage\",\n        \"accounting:create\",\n        \"accounting:delete\",\n        // Reports\n        \"reports:view\",\n        \"reports:export\"\n    ];\n    return {\n        id: \"1\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام\",\n        role: \"admin\",\n        branch_id: \"1\",\n        warehouse_id: \"1\",\n        pos_id: \"1\",\n        is_active: true,\n        created_at: \"2024-01-01\",\n        updated_at: \"2024-01-01\",\n        permissions: adminPermissions\n    };\n}\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    return user.permissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    return permissions.some((permission)=>user.permissions.includes(permission));\n}\nfunction hasAllPermissions(user, permissions) {\n    if (!user) return false;\n    return permissions.every((permission)=>user.permissions.includes(permission));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/auth.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUM4QztBQUNqQjtBQUVkLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILHdEQUFZQTtrQkFDWCw0RUFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0g7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJ1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = (__webpack_exec__("./src/pages/_app.tsx"));
module.exports = __webpack_exports__;

})();