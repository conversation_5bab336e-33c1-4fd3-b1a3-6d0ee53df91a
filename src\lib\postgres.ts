import { Pool } from 'pg'

// إعداد الاتصال بقاعدة البيانات PostgreSQL
const pool = new Pool({
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '5432'),
  database: process.env.DB_NAME || 'Vero_ERP_ABA',
  user: process.env.DB_USER || 'openpg',
  password: process.env.DB_PASSWORD || 'V@admin010',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
})

// دالة للتحقق من الاتصال
export const testConnection = async () => {
  try {
    const client = await pool.connect()
    const result = await client.query('SELECT NOW()')
    client.release()
    return { success: true, time: result.rows[0].now }
  } catch (error) {
    console.error('خطأ في الاتصال بقاعدة البيانات:', error)
    return { success: false, error }
  }
}

// دالة لتنفيذ استعلام
export const query = async (text: string, params?: any[]) => {
  try {
    const result = await pool.query(text, params)
    return { success: true, data: result.rows, rowCount: result.rowCount }
  } catch (error) {
    console.error('خطأ في تنفيذ الاستعلام:', error)
    return { success: false, error }
  }
}

// دالة للتحقق من وجود الجداول
export const checkTables = async () => {
  try {
    const result = await query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name IN ('branches', 'users', 'products', 'warehouses')
    `)

    const tables = result.data?.map(row => row.table_name) || []

    return {
      success: true,
      tables,
      hasBranches: tables.includes('branches'),
      hasUsers: tables.includes('users'),
      hasProducts: tables.includes('products'),
      hasWarehouses: tables.includes('warehouses'),
      needsSetup: !tables.includes('branches')
    }
  } catch (error) {
    console.error('خطأ في فحص الجداول:', error)
    return { success: false, error, needsSetup: true }
  }
}

// دالة لإنشاء مستخدم جديد
export const createUser = async (userData: {
  email: string
  username: string
  full_name: string
  password_hash: string
  role?: string
}) => {
  try {
    const result = await query(`
      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)
      VALUES ($1, $2, $3, $4, $5, true, NOW())
      RETURNING id, email, username, full_name, role
    `, [
      userData.email,
      userData.username,
      userData.full_name,
      userData.password_hash,
      userData.role || 'admin'
    ])

    return { success: true, user: result.data?.[0] }
  } catch (error) {
    console.error('خطأ في إنشاء المستخدم:', error)
    return { success: false, error }
  }
}

// دالة للبحث عن مستخدم
export const findUser = async (email: string) => {
  try {
    const result = await query(`
      SELECT id, email, username, full_name, role, password_hash, is_active
      FROM users
      WHERE email = $1 AND is_active = true
    `, [email])

    return { success: true, user: result.data?.[0] || null }
  } catch (error) {
    console.error('خطأ في البحث عن المستخدم:', error)
    return { success: false, error }
  }
}

// خدمات المنتجات متوافقة مع الكود
export const getProducts = async () => {
  try {
    const result = await query(`
      SELECT
        p.*,
        i.total_stock,
        i.available_stock,
        i.reserved_stock
      FROM products p
      LEFT JOIN inventory i ON p.id = i.product_id
      WHERE p.is_active = true
      ORDER BY p.name
    `)

    return {
      success: true,
      data: result.data?.map(product => ({
        ...product,
        current_stock: product.total_stock || 0,
        price: product.unit_price,
        cost: product.cost_price
      })) || []
    }
  } catch (error) {
    console.error('خطأ في جلب المنتجات:', error)
    return { success: false, error }
  }
}

// خدمات المخزون متوافقة مع الكود
export const getInventory = async () => {
  try {
    const result = await query(`
      SELECT
        i.*,
        p.name as product_name,
        p.sku as product_sku,
        p.category as product_category,
        w.name as warehouse_name
      FROM inventory i
      JOIN products p ON i.product_id = p.id
      JOIN warehouses w ON i.warehouse_id = w.id
      ORDER BY i.updated_at DESC
    `)

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('خطأ في جلب المخزون:', error)
    return { success: false, error }
  }
}

// خدمات العملاء
export const getCustomers = async () => {
  try {
    const result = await query(`
      SELECT * FROM customers
      WHERE is_active = true
      ORDER BY name
    `)

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('خطأ في جلب العملاء:', error)
    return { success: false, error }
  }
}

// خدمات الموردين
export const getSuppliers = async () => {
  try {
    const result = await query(`
      SELECT * FROM suppliers
      WHERE is_active = true
      ORDER BY name
    `)

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('خطأ في جلب الموردين:', error)
    return { success: false, error }
  }
}

// خدمات أوامر المبيعات
export const getSalesOrders = async () => {
  try {
    const result = await query(`
      SELECT
        so.*,
        c.name as customer_name,
        b.name as branch_name,
        u.full_name as created_by_name
      FROM sales_orders so
      LEFT JOIN customers c ON so.customer_id = c.id
      LEFT JOIN branches b ON so.branch_id = b.id
      LEFT JOIN users u ON so.created_by = u.id
      ORDER BY so.created_at DESC
    `)

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('خطأ في جلب أوامر المبيعات:', error)
    return { success: false, error }
  }
}

// خدمات أوامر الشراء
export const getPurchaseOrders = async () => {
  try {
    const result = await query(`
      SELECT
        po.*,
        s.name as supplier_name,
        b.name as branch_name,
        w.name as warehouse_name,
        u.full_name as created_by_name
      FROM purchase_orders po
      LEFT JOIN suppliers s ON po.supplier_id = s.id
      LEFT JOIN branches b ON po.branch_id = b.id
      LEFT JOIN warehouses w ON po.warehouse_id = w.id
      LEFT JOIN users u ON po.created_by = u.id
      ORDER BY po.created_at DESC
    `)

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('خطأ في جلب أوامر الشراء:', error)
    return { success: false, error }
  }
}

// خدمات حركات المخزون
export const getStockMovements = async () => {
  try {
    const result = await query(`
      SELECT
        sm.*,
        p.name as product_name,
        p.sku as product_sku,
        w.name as warehouse_name,
        u.full_name as created_by_name
      FROM stock_movements sm
      JOIN products p ON sm.product_id = p.id
      JOIN warehouses w ON sm.warehouse_id = w.id
      LEFT JOIN users u ON sm.created_by = u.id
      ORDER BY sm.created_at DESC
      LIMIT 100
    `)

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('خطأ في جلب حركات المخزون:', error)
    return { success: false, error }
  }
}

// خدمات طلبات الصيانة
export const getMaintenanceRequests = async () => {
  try {
    const result = await query(`
      SELECT
        mr.*,
        t.full_name as technician_name,
        at.full_name as assigned_technician_name,
        b.name as branch_name,
        w.name as warehouse_name
      FROM maintenance_requests mr
      LEFT JOIN users t ON mr.technician_id = t.id
      LEFT JOIN users at ON mr.assigned_technician = at.id
      LEFT JOIN branches b ON mr.branch_id = b.id
      LEFT JOIN warehouses w ON mr.warehouse_id = w.id
      ORDER BY mr.created_at DESC
    `)

    return { success: true, data: result.data || [] }
  } catch (error) {
    console.error('خطأ في جلب طلبات الصيانة:', error)
    return { success: false, error }
  }
}

// خدمات المصادقة
export const findUser = async (email: string) => {
  try {
    const result = await query(`
      SELECT * FROM users
      WHERE email = $1 AND is_active = true
    `, [email])

    if (result.success && result.data && result.data.length > 0) {
      return { success: true, user: result.data[0] }
    } else {
      return { success: false, user: null }
    }
  } catch (error) {
    console.error('خطأ في البحث عن المستخدم:', error)
    return { success: false, error, user: null }
  }
}

// إنشاء مستخدم جديد
export const createUser = async (userData: {
  email: string
  username: string
  full_name: string
  password_hash: string
  role?: string
  branch_id?: number
}) => {
  try {
    const result = await query(`
      INSERT INTO users (email, username, full_name, password_hash, role, branch_id)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, email, username, full_name, role, is_active, branch_id, warehouse_id, pos_id
    `, [
      userData.email,
      userData.username,
      userData.full_name,
      userData.password_hash,
      userData.role || 'admin',
      userData.branch_id || null
    ])

    if (result.success && result.data && result.data.length > 0) {
      return { success: true, user: result.data[0] }
    } else {
      return { success: false, error: 'فشل في إنشاء المستخدم' }
    }
  } catch (error) {
    console.error('خطأ في إنشاء المستخدم:', error)
    return { success: false, error }
  }
}

export default pool
