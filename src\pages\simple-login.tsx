import { useState } from 'react'
import { use<PERSON><PERSON><PERSON> } from 'next/router'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  LogIn, 
  Eye, 
  EyeOff, 
  User,
  Lock,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

export default function SimpleLogin() {
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: '<EMAIL>',
    password: 'V@admin010'
  })
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      // محاولة تسجيل الدخول مع PostgreSQL
      const response = await fetch('/api/postgres/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      const result = await response.json()

      if (result.success) {
        // حفظ بيانات المستخدم
        localStorage.setItem('user', JSON.stringify(result.user))
        
        // توجيه للوحة التحكم
        router.push('/integrated-dashboard')
      } else {
        setError(result.message || 'بيانات تسجيل الدخول غير صحيحة')
      }
    } catch (error) {
      // في حالة فشل الاتصال، استخدام بيانات افتراضية
      if (formData.email === '<EMAIL>' && formData.password === 'V@admin010') {
        localStorage.setItem('user', JSON.stringify({
          id: 1,
          email: '<EMAIL>',
          full_name: 'مدير النظام',
          role: 'admin'
        }))
        router.push('/integrated-dashboard')
      } else {
        setError('بيانات تسجيل الدخول غير صحيحة')
      }
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-6">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center">
          <div className="bg-white p-4 rounded-full shadow-lg inline-block mb-4">
            <LogIn className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900">تسجيل الدخول</h1>
          <p className="text-gray-600 mt-2">ادخل إلى النظام المتكامل</p>
        </div>

        {/* Login Form */}
        <Card className="shadow-xl">
          <CardHeader>
            <CardTitle className="text-center">بيانات تسجيل الدخول</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Email */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  البريد الإلكتروني
                </label>
                <div className="relative">
                  <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    placeholder="أدخل البريد الإلكتروني"
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              {/* Password */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  كلمة المرور
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={(e) => setFormData({...formData, password: e.target.value})}
                    placeholder="أدخل كلمة المرور"
                    className="pl-10 pr-10"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>

              {/* Error Alert */}
              {error && (
                <Alert className="border-red-200 bg-red-50">
                  <AlertCircle className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    {error}
                  </AlertDescription>
                </Alert>
              )}

              {/* Submit Button */}
              <Button 
                type="submit" 
                className="w-full bg-blue-600 hover:bg-blue-700"
                disabled={loading}
                size="lg"
              >
                {loading ? (
                  <div className="flex items-center">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    جاري تسجيل الدخول...
                  </div>
                ) : (
                  <div className="flex items-center">
                    <LogIn className="h-4 w-4 mr-2" />
                    تسجيل الدخول
                  </div>
                )}
              </Button>
            </form>
          </CardContent>
        </Card>

        {/* Default Credentials */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <CheckCircle className="h-6 w-6 text-blue-600 mx-auto" />
              <h3 className="font-medium text-blue-900">بيانات تسجيل الدخول الافتراضية</h3>
              <div className="text-sm text-blue-700 space-y-1">
                <p><strong>البريد الإلكتروني:</strong> <EMAIL></p>
                <p><strong>كلمة المرور:</strong> V@admin010</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Setup Link */}
        <div className="text-center">
          <p className="text-gray-600 text-sm">
            لم يتم إعداد النظام بعد؟{' '}
            <button
              onClick={() => router.push('/simple-setup')}
              className="text-blue-600 hover:text-blue-800 font-medium"
            >
              ابدأ الإعداد الآن
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
