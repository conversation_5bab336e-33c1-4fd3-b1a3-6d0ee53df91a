{"c": ["webpack"], "r": ["pages/integration-setup", "pages/database-status", "pages/index"], "m": ["./node_modules/@radix-ui/react-progress/dist/index.mjs", "./node_modules/lucide-react/dist/esm/icons/code.js", "./node_modules/lucide-react/dist/esm/icons/database.js", "./node_modules/lucide-react/dist/esm/icons/layers.js", "./node_modules/lucide-react/dist/esm/icons/zap.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Cintegration-setup.tsx&page=%2Fintegration-setup!", "./src/components/ui/progress.tsx", "./src/pages/integration-setup.tsx", "__barrel_optimize__?names=CheckCircle,Code,Database,Layers,Package,Users,Warehouse,XCircle,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/lucide-react/dist/esm/icons/external-link.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Cdatabase-status.tsx&page=%2Fdatabase-status!", "./src/pages/database-status.tsx", "__barrel_optimize__?names=CheckCircle,Database,ExternalLink,RefreshCw,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "./src/pages/index.tsx"]}