"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/postgres/setup-database";
exports.ids = ["pages/api/postgres/setup-database"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fsetup-database&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Csetup-database.ts&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fsetup-database&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Csetup-database.ts&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_postgres_setup_database_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\postgres\\setup-database.ts */ \"(api)/./src/pages/api/postgres/setup-database.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_setup_database_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_setup_database_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/postgres/setup-database\",\n        pathname: \"/api/postgres/setup-database\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_postgres_setup_database_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fsetup-database&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Csetup-database.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/postgres.ts":
/*!*****************************!*\
  !*** ./src/lib/postgres.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTables: () => (/* binding */ checkTables),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   findUser: () => (/* binding */ findUser),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\n// إعداد الاتصال بقاعدة البيانات PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"5432\"),\n    database: process.env.DB_NAME || \"Vero_ERP_ABA\",\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    ssl:  false ? 0 : false,\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const client = await pool.connect();\n        const result = await client.query(\"SELECT NOW()\");\n        client.release();\n        return {\n            success: true,\n            time: result.rows[0].now\n        };\n    } catch (error) {\n        console.error(\"خطأ في الاتصال بقاعدة البيانات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    try {\n        const result = await pool.query(text, params);\n        return {\n            success: true,\n            data: result.rows,\n            rowCount: result.rowCount\n        };\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للتحقق من وجود الجداول\nconst checkTables = async ()=>{\n    try {\n        const result = await query(`\n      SELECT table_name \n      FROM information_schema.tables \n      WHERE table_schema = 'public' \n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = result.data?.map((row)=>row.table_name) || [];\n        return {\n            success: true,\n            tables,\n            hasBranches: tables.includes(\"branches\"),\n            hasUsers: tables.includes(\"users\"),\n            hasProducts: tables.includes(\"products\"),\n            hasWarehouses: tables.includes(\"warehouses\"),\n            needsSetup: !tables.includes(\"branches\")\n        };\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return {\n            success: false,\n            error,\n            needsSetup: true\n        };\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const result = await query(`\n      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)\n      VALUES ($1, $2, $3, $4, $5, true, NOW())\n      RETURNING id, email, username, full_name, role\n    `, [\n            userData.email,\n            userData.username,\n            userData.full_name,\n            userData.password_hash,\n            userData.role || \"admin\"\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0]\n        };\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للبحث عن مستخدم\nconst findUser = async (email)=>{\n    try {\n        const result = await query(`\n      SELECT id, email, username, full_name, role, password_hash, is_active\n      FROM users \n      WHERE email = $1 AND is_active = true\n    `, [\n            email\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0] || null\n        };\n    } catch (error) {\n        console.error(\"خطأ في البحث عن المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/postgres.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/postgres/setup-database.ts":
/*!**************************************************!*\
  !*** ./src/pages/api/postgres/setup-database.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/postgres */ \"(api)/./src/lib/postgres.ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // قراءة ملف SQL\n        const sqlFilePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"database_setup_postgres.sql\");\n        if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(sqlFilePath)) {\n            return res.status(404).json({\n                success: false,\n                message: \"ملف database_setup_postgres.sql غير موجود\"\n            });\n        }\n        const sqlContent = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(sqlFilePath, \"utf8\");\n        // تقسيم الاستعلامات\n        const statements = sqlContent.split(\";\").map((stmt)=>stmt.trim()).filter((stmt)=>stmt.length > 0 && !stmt.startsWith(\"--\"));\n        const results = [];\n        let successCount = 0;\n        let errorCount = 0;\n        // تنفيذ كل استعلام\n        for(let i = 0; i < statements.length; i++){\n            const statement = statements[i];\n            if (statement.trim().length === 0) continue;\n            try {\n                const result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(statement);\n                if (result.success) {\n                    successCount++;\n                    results.push({\n                        statement: statement.substring(0, 100) + \"...\",\n                        success: true,\n                        message: \"تم التنفيذ بنجاح\",\n                        rowCount: result.rowCount\n                    });\n                } else {\n                    errorCount++;\n                    results.push({\n                        statement: statement.substring(0, 100) + \"...\",\n                        success: false,\n                        error: result.error\n                    });\n                }\n            } catch (error) {\n                errorCount++;\n                results.push({\n                    statement: statement.substring(0, 100) + \"...\",\n                    success: false,\n                    error: error instanceof Error ? error.message : \"خطأ غير معروف\"\n                });\n            }\n        }\n        // فحص الجداول بعد التنفيذ\n        const tablesCheck = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT table_name\n      FROM information_schema.tables\n      WHERE table_schema = 'public'\n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = tablesCheck.success ? tablesCheck.data?.map((row)=>row.table_name) : [];\n        return res.status(200).json({\n            success: successCount > 0,\n            message: `تم تنفيذ ${successCount} استعلام بنجاح، ${errorCount} فشل`,\n            summary: {\n                totalStatements: statements.length,\n                successful: successCount,\n                failed: errorCount,\n                tablesCreated: tables?.length || 0\n            },\n            tables: {\n                created: tables,\n                hasBranches: tables?.includes(\"branches\"),\n                hasUsers: tables?.includes(\"users\"),\n                hasProducts: tables?.includes(\"products\"),\n                hasWarehouses: tables?.includes(\"warehouses\")\n            },\n            results: results,\n            recommendations: successCount > 0 ? [\n                \"✅ تم إنشاء الجداول بنجاح\",\n                \"\\uD83C\\uDFAF يمكنك الآن إنشاء مستخدم جديد\",\n                \"\\uD83D\\uDD04 تحقق من حالة الجداول\"\n            ] : [\n                \"❌ فشل في إنشاء الجداول\",\n                \"\\uD83D\\uDD0D تحقق من أخطاء قاعدة البيانات\",\n                \"\\uD83D\\uDCDD راجع ملف database_setup_postgres.sql\"\n            ]\n        });\n    } catch (error) {\n        console.error(\"خطأ في إعداد قاعدة البيانات:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/postgres/setup-database.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fsetup-database&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Csetup-database.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();