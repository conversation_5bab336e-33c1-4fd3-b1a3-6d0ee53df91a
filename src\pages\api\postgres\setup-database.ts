import { NextApiRequest, NextApiResponse } from 'next'
import { query } from '@/lib/postgres'
import fs from 'fs'
import path from 'path'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // قراءة ملف SQL
    const sqlFilePath = path.join(process.cwd(), 'database_setup_postgres.sql')

    if (!fs.existsSync(sqlFilePath)) {
      return res.status(404).json({
        success: false,
        message: 'ملف database_setup_postgres.sql غير موجود'
      })
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8')

    // تقسيم الاستعلامات
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))

    const results = []
    let successCount = 0
    let errorCount = 0

    // تنفيذ كل استعلام
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]

      if (statement.trim().length === 0) continue

      try {
        const result = await query(statement)

        if (result.success) {
          successCount++
          results.push({
            statement: statement.substring(0, 100) + '...',
            success: true,
            message: 'تم التنفيذ بنجاح',
            rowCount: result.rowCount
          })
        } else {
          errorCount++
          results.push({
            statement: statement.substring(0, 100) + '...',
            success: false,
            error: result.error
          })
        }
      } catch (error) {
        errorCount++
        results.push({
          statement: statement.substring(0, 100) + '...',
          success: false,
          error: error instanceof Error ? error.message : 'خطأ غير معروف'
        })
      }
    }

    // فحص الجداول بعد التنفيذ
    const tablesCheck = await query(`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      AND table_name IN ('branches', 'users', 'products', 'warehouses')
    `)

    const tables = tablesCheck.success ? tablesCheck.data?.map(row => row.table_name) : []

    return res.status(200).json({
      success: successCount > 0,
      message: `تم تنفيذ ${successCount} استعلام بنجاح، ${errorCount} فشل`,
      summary: {
        totalStatements: statements.length,
        successful: successCount,
        failed: errorCount,
        tablesCreated: tables?.length || 0
      },
      tables: {
        created: tables,
        hasBranches: tables?.includes('branches'),
        hasUsers: tables?.includes('users'),
        hasProducts: tables?.includes('products'),
        hasWarehouses: tables?.includes('warehouses')
      },
      results: results, // جميع النتائج لفهم الأخطاء
      recommendations: successCount > 0 ? [
        '✅ تم إنشاء الجداول بنجاح',
        '🎯 يمكنك الآن إنشاء مستخدم جديد',
        '🔄 تحقق من حالة الجداول'
      ] : [
        '❌ فشل في إنشاء الجداول',
        '🔍 تحقق من أخطاء قاعدة البيانات',
        '📝 راجع ملف database_setup_postgres.sql'
      ]
    })

  } catch (error) {
    console.error('خطأ في إعداد قاعدة البيانات:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
