import { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Database,
  RefreshCw,
  Copy,
  ExternalLink
} from 'lucide-react'

interface TableStatus {
  exists: boolean
  status: string
  count?: number
}

interface DatabaseStatus {
  success: boolean
  database?: {
    name: string
    host: string
    connected: boolean
  }
  summary?: {
    totalRequired: number
    existing: number
    missing: number
    setupComplete: boolean
    status: string
  }
  tables?: Record<string, TableStatus>
  enums?: Array<{
    name: string
    values: string[]
    status: string
  }>
  recommendations?: string[]
}

export default function DatabaseStatus() {
  const [status, setStatus] = useState<DatabaseStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [setupLoading, setSetupLoading] = useState(false)
  const [setupResult, setSetupResult] = useState<any>(null)

  const fetchStatus = async () => {
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/postgres/check-tables')
      const data = await response.json()

      if (data.success) {
        setStatus(data)
      } else {
        setError(data.message || 'فشل في فحص قاعدة البيانات')
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchStatus()
  }, [])

  const setupDatabase = async () => {
    setSetupLoading(true)
    setSetupResult(null)

    try {
      const response = await fetch('/api/postgres/setup-database', {
        method: 'POST'
      })
      const data = await response.json()
      setSetupResult(data)

      // إعادة فحص الجداول بعد الإعداد
      if (data.success) {
        setTimeout(() => {
          fetchStatus()
        }, 1000)
      }
    } catch (err) {
      setSetupResult({
        success: false,
        message: 'خطأ في تنفيذ الإعداد'
      })
    } finally {
      setSetupLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p>جاري فحص قاعدة البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            حالة قاعدة البيانات PostgreSQL
          </h1>
          <p className="text-gray-600">
            فحص الجداول والإعدادات المطلوبة للنظام
          </p>
        </div>

        {/* Error Alert */}
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <XCircle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Database Info */}
        {status?.database && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                معلومات قاعدة البيانات
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <p className="text-sm text-gray-600">اسم قاعدة البيانات</p>
                  <p className="font-medium">{status.database.name}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">المضيف</p>
                  <p className="font-medium">{status.database.host}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">حالة الاتصال</p>
                  <p className="font-medium text-green-600">
                    {status.database.connected ? '✅ متصل' : '❌ غير متصل'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Summary */}
        {status?.summary && (
          <Card>
            <CardHeader>
              <CardTitle>ملخص الحالة</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">
                    {status.summary.totalRequired}
                  </p>
                  <p className="text-sm text-gray-600">المطلوب</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">
                    {status.summary.existing}
                  </p>
                  <p className="text-sm text-gray-600">موجود</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-red-600">
                    {status.summary.missing}
                  </p>
                  <p className="text-sm text-gray-600">مفقود</p>
                </div>
                <div className="text-center">
                  <p className="text-lg font-bold">
                    {status.summary.setupComplete ? '✅' : '⚠️'}
                  </p>
                  <p className="text-sm text-gray-600">الحالة</p>
                </div>
              </div>
              <div className="text-center">
                <p className="text-lg font-medium">{status.summary.status}</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tables Status */}
        {status?.tables && (
          <Card>
            <CardHeader>
              <CardTitle>حالة الجداول</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(status.tables).map(([tableName, tableInfo]) => (
                  <div
                    key={tableName}
                    className={`p-4 rounded-lg border ${
                      tableInfo.exists
                        ? 'border-green-200 bg-green-50'
                        : 'border-red-200 bg-red-50'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="font-medium">{tableName}</h3>
                      {tableInfo.exists ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <p className="text-sm">{tableInfo.status}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Recommendations */}
        {status?.recommendations && (
          <Card>
            <CardHeader>
              <CardTitle>التوصيات</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {status.recommendations.map((rec, index) => (
                  <li key={index} className="flex items-start">
                    <span className="mr-2">{rec}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>
        )}

        {/* Setup Result */}
        {setupResult && (
          <Alert className={setupResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
            {setupResult.success ? (
              <CheckCircle className="h-4 w-4 text-green-600" />
            ) : (
              <XCircle className="h-4 w-4 text-red-600" />
            )}
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium">{setupResult.message}</p>
                {setupResult.summary && (
                  <div className="text-sm">
                    <p>الاستعلامات الناجحة: {setupResult.summary.successful}</p>
                    <p>الاستعلامات الفاشلة: {setupResult.summary.failed}</p>
                    <p>الجداول المُنشأة: {setupResult.summary.tablesCreated}</p>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Actions */}
        <div className="flex gap-4 justify-center flex-wrap">
          <Button onClick={fetchStatus} className="flex items-center">
            <RefreshCw className="h-4 w-4 mr-2" />
            إعادة فحص
          </Button>

          {status?.summary && !status.summary.setupComplete && (
            <Button
              onClick={setupDatabase}
              disabled={setupLoading}
              className="flex items-center bg-green-600 hover:bg-green-700"
            >
              {setupLoading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Database className="h-4 w-4 mr-2" />
              )}
              {setupLoading ? 'جاري الإعداد...' : 'إعداد قاعدة البيانات'}
            </Button>
          )}

          <Button
            variant="outline"
            onClick={() => window.open('/api/postgres/check-tables', '_blank')}
            className="flex items-center"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            عرض JSON
          </Button>
        </div>
      </div>
    </div>
  )
}
