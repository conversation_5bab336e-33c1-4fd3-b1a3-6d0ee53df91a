import { NextApiRequest, NextApiResponse } from 'next'
import { query } from '@/lib/postgres'
import fs from 'fs'
import path from 'path'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // قراءة ملف المخطط المتكامل
    const sqlFilePath = path.join(process.cwd(), 'integrated_database_schema.sql')
    
    if (!fs.existsSync(sqlFilePath)) {
      return res.status(404).json({
        success: false,
        message: 'ملف integrated_database_schema.sql غير موجود'
      })
    }

    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8')
    
    // تقسيم الاستعلامات بعناية
    const statements = []
    let currentStatement = ''
    let inDollarQuote = false
    let dollarTag = ''
    
    const lines = sqlContent.split('\n')
    
    for (const line of lines) {
      const trimmedLine = line.trim()
      
      // تجاهل التعليقات والأسطر الفارغة
      if (trimmedLine.startsWith('--') || trimmedLine === '') {
        continue
      }
      
      // التعامل مع DO blocks
      if (trimmedLine.includes('DO $$')) {
        inDollarQuote = true
        dollarTag = '$$'
        currentStatement += line + '\n'
        continue
      }
      
      if (inDollarQuote) {
        currentStatement += line + '\n'
        if (trimmedLine.includes(dollarTag) && !trimmedLine.startsWith('DO')) {
          inDollarQuote = false
          statements.push(currentStatement.trim())
          currentStatement = ''
        }
        continue
      }
      
      currentStatement += line + '\n'
      
      // إنهاء الاستعلام عند الفاصلة المنقوطة
      if (trimmedLine.endsWith(';')) {
        statements.push(currentStatement.trim())
        currentStatement = ''
      }
    }
    
    // إضافة آخر استعلام إذا لم ينته بفاصلة منقوطة
    if (currentStatement.trim()) {
      statements.push(currentStatement.trim())
    }

    console.log(`عدد الاستعلامات المعالجة: ${statements.length}`)

    const results = []
    let successCount = 0
    let errorCount = 0

    // تنفيذ كل استعلام
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i]
      
      if (statement.length === 0) continue

      console.log(`تنفيذ الاستعلام ${i + 1}: ${statement.substring(0, 50)}...`)

      try {
        const result = await query(statement)
        
        if (result.success) {
          successCount++
          results.push({
            index: i + 1,
            statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
            success: true,
            message: 'تم التنفيذ بنجاح',
            rowCount: result.rowCount
          })
          console.log(`✅ نجح الاستعلام ${i + 1}`)
        } else {
          errorCount++
          results.push({
            index: i + 1,
            statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
            success: false,
            error: result.error,
            message: 'فشل في التنفيذ'
          })
          console.log(`❌ فشل الاستعلام ${i + 1}:`, result.error)
        }
      } catch (error) {
        errorCount++
        const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
        results.push({
          index: i + 1,
          statement: statement.substring(0, 100) + (statement.length > 100 ? '...' : ''),
          success: false,
          error: errorMessage,
          message: 'خطأ في التنفيذ'
        })
        console.log(`❌ خطأ في الاستعلام ${i + 1}:`, errorMessage)
      }
    }

    // فحص الجداول والأنواع بعد التنفيذ
    const tablesCheck = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `)

    const enumsCheck = await query(`
      SELECT typname 
      FROM pg_type 
      WHERE typname IN ('user_role', 'transaction_type', 'payment_method', 'movement_type', 'reference_type')
    `)

    const tables = tablesCheck.success ? tablesCheck.data?.map(row => row.table_name) : []
    const enums = enumsCheck.success ? enumsCheck.data?.map(row => row.typname) : []

    // إضافة بيانات تجريبية للمنتجات إذا كانت الجداول موجودة
    if (tables?.includes('products') && tables?.includes('inventory')) {
      try {
        await query(`
          INSERT INTO products (name, sku, description, category, unit_price, cost_price, stock_quantity, min_stock_level)
          VALUES 
            ('لابتوب HP EliteBook', 'HP-EB-840-G8', 'لابتوب HP EliteBook 840 G8 - Intel Core i7', 'أجهزة كمبيوتر', 25000, 20000, 15, 5),
            ('ذاكرة RAM 16GB', 'RAM-DDR4-16GB', 'ذاكرة RAM DDR4 16GB 3200MHz', 'قطع غيار', 1500, 1200, 50, 10)
          ON CONFLICT (sku) DO NOTHING
        `)
        
        await query(`
          INSERT INTO inventory (product_id, warehouse_id, total_stock, available_stock, reserved_stock)
          SELECT p.id, 1, p.stock_quantity, p.stock_quantity, 0
          FROM products p
          WHERE NOT EXISTS (SELECT 1 FROM inventory i WHERE i.product_id = p.id)
        `)
      } catch (error) {
        console.log('تحذير: فشل في إضافة البيانات التجريبية:', error)
      }
    }

    return res.status(200).json({
      success: successCount > 0,
      message: `تم تنفيذ ${successCount} استعلام بنجاح، ${errorCount} فشل`,
      summary: {
        totalStatements: statements.length,
        successful: successCount,
        failed: errorCount,
        tablesCreated: tables?.length || 0,
        enumsCreated: enums?.length || 0
      },
      database: {
        tables: tables,
        enums: enums,
        coreTablesReady: ['branches', 'warehouses', 'users', 'products', 'inventory'].every(t => tables?.includes(t))
      },
      results: results.slice(0, 15), // أول 15 نتيجة
      recommendations: successCount > 0 ? [
        '✅ تم إنشاء المخطط المتكامل بنجاح',
        '🎯 يمكنك الآن إنشاء مستخدم جديد',
        '🔄 النظام جاهز للاستخدام الكامل',
        '📊 تم إضافة بيانات تجريبية'
      ] : [
        '❌ فشل في إنشاء المخطط',
        '🔍 تحقق من أخطاء قاعدة البيانات',
        '📞 تحقق من الاتصال بـ PostgreSQL'
      ]
    })

  } catch (error) {
    console.error('خطأ في إعداد المخطط المتكامل:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
