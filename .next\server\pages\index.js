/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/index";
exports.ids = ["pages/index"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\index.tsx */ \"./src/pages/index.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/index\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_index_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTJnBhZ2U9JTJGJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTVDcGFnZXMlNUNpbmRleC50c3gmYWJzb2x1dGVBcHBQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9hcHAmYWJzb2x1dGVEb2N1bWVudFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2RvY3VtZW50Jm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUErRjtBQUNoQztBQUNMO0FBQzFEO0FBQ29EO0FBQ1Y7QUFDMUM7QUFDb0Q7QUFDcEQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLGlEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLHVCQUF1Qix3RUFBSyxDQUFDLGlEQUFRO0FBQ3JDLHVCQUF1Qix3RUFBSyxDQUFDLGlEQUFRO0FBQ3JDLDJCQUEyQix3RUFBSyxDQUFDLGlEQUFRO0FBQ3pDLGVBQWUsd0VBQUssQ0FBQyxpREFBUTtBQUM3Qix3QkFBd0Isd0VBQUssQ0FBQyxpREFBUTtBQUM3QztBQUNPLGdDQUFnQyx3RUFBSyxDQUFDLGlEQUFRO0FBQzlDLGdDQUFnQyx3RUFBSyxDQUFDLGlEQUFRO0FBQzlDLGlDQUFpQyx3RUFBSyxDQUFDLGlEQUFRO0FBQy9DLGdDQUFnQyx3RUFBSyxDQUFDLGlEQUFRO0FBQzlDLG9DQUFvQyx3RUFBSyxDQUFDLGlEQUFRO0FBQ3pEO0FBQ08sd0JBQXdCLHlHQUFnQjtBQUMvQztBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBLFdBQVc7QUFDWCxnQkFBZ0I7QUFDaEIsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vPzk4YTciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2Z1dHVyZS9yb3V0ZS1tb2R1bGVzL3BhZ2VzL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvZnV0dXJlL3JvdXRlLWtpbmRcIjtcbmltcG9ydCB7IGhvaXN0IH0gZnJvbSBcIm5leHQvZGlzdC9idWlsZC90ZW1wbGF0ZXMvaGVscGVyc1wiO1xuLy8gSW1wb3J0IHRoZSBhcHAgYW5kIGRvY3VtZW50IG1vZHVsZXMuXG5pbXBvcnQgRG9jdW1lbnQgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fZG9jdW1lbnRcIjtcbmltcG9ydCBBcHAgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fYXBwXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9zcmNcXFxccGFnZXNcXFxcaW5kZXgudHN4XCI7XG4vLyBSZS1leHBvcnQgdGhlIGNvbXBvbmVudCAoc2hvdWxkIGJlIHRoZSBkZWZhdWx0IGV4cG9ydCkuXG5leHBvcnQgZGVmYXVsdCBob2lzdCh1c2VybGFuZCwgXCJkZWZhdWx0XCIpO1xuLy8gUmUtZXhwb3J0IG1ldGhvZHMuXG5leHBvcnQgY29uc3QgZ2V0U3RhdGljUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJnZXRTdGF0aWNQcm9wc1wiKTtcbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCBcImdldFN0YXRpY1BhdGhzXCIpO1xuZXhwb3J0IGNvbnN0IGdldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCBcImdldFNlcnZlclNpZGVQcm9wc1wiKTtcbmV4cG9ydCBjb25zdCBjb25maWcgPSBob2lzdCh1c2VybGFuZCwgXCJjb25maWdcIik7XG5leHBvcnQgY29uc3QgcmVwb3J0V2ViVml0YWxzID0gaG9pc3QodXNlcmxhbmQsIFwicmVwb3J0V2ViVml0YWxzXCIpO1xuLy8gUmUtZXhwb3J0IGxlZ2FjeSBtZXRob2RzLlxuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFN0YXRpY1Byb3BzID0gaG9pc3QodXNlcmxhbmQsIFwidW5zdGFibGVfZ2V0U3RhdGljUHJvcHNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U3RhdGljUGF0aHMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTdGF0aWNQYXRoc1wiKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXNcIik7XG5leHBvcnQgY29uc3QgdW5zdGFibGVfZ2V0U2VydmVyUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTZXJ2ZXJQcm9wc1wiKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgXCJ1bnN0YWJsZV9nZXRTZXJ2ZXJTaWRlUHJvcHNcIik7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc1JvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgcGFnZTogXCIvaW5kZXhcIixcbiAgICAgICAgcGF0aG5hbWU6IFwiL1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICBjb21wb25lbnRzOiB7XG4gICAgICAgIEFwcCxcbiAgICAgICAgRG9jdW1lbnRcbiAgICB9LFxuICAgIHVzZXJsYW5kXG59KTtcblxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cGFnZXMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// دالة للحصول على صلاحيات الدور\nconst getRolePermissions = (role)=>{\n    return _lib_auth__WEBPACK_IMPORTED_MODULE_2__.ROLE_PERMISSIONS[role] || [];\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تحميل بيانات المستخدم من localStorage\n    const loadUserFromStorage = ()=>{\n        try {\n            const savedUser = localStorage.getItem(\"user\");\n            if (savedUser) {\n                return JSON.parse(savedUser);\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error loading user from storage:\", error);\n            return null;\n        }\n    };\n    const refreshUser = async ()=>{\n        const savedUser = loadUserFromStorage();\n        if (savedUser && !savedUser.permissions) {\n            // إضافة الصلاحيات إذا لم تكن موجودة\n            const permissions = getRolePermissions(savedUser.role);\n            const userWithPermissions = {\n                ...savedUser,\n                permissions\n            };\n            localStorage.setItem(\"user\", JSON.stringify(userWithPermissions));\n            setUser(userWithPermissions);\n        } else {\n            setUser(savedUser);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/postgres/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                const userData = result.user;\n                // إضافة الصلاحيات بناءً على الدور\n                const permissions = getRolePermissions(userData.role);\n                const userWithPermissions = {\n                    ...userData,\n                    permissions\n                };\n                localStorage.setItem(\"user\", JSON.stringify(userWithPermissions));\n                setUser(userWithPermissions);\n            } else {\n                throw new Error(result.message || \"فشل في تسجيل الدخول\");\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            localStorage.removeItem(\"user\");\n            setUser(null);\n        } catch (error) {\n            console.error(\"Sign out error:\", error);\n            throw error;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل المستخدم من localStorage عند بدء التطبيق\n        const initializeAuth = async ()=>{\n            try {\n                await refreshUser();\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   PERMISSIONS_ORGANIZED: () => (/* binding */ PERMISSIONS_ORGANIZED),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n// Mock user type for demo\nconst PERMISSIONS = {\n    // User Management\n    USERS_VIEW: \"users:view\",\n    USERS_CREATE: \"users:create\",\n    USERS_EDIT: \"users:edit\",\n    USERS_DELETE: \"users:delete\",\n    // Branch Management\n    BRANCHES_VIEW: \"branches:view\",\n    BRANCHES_CREATE: \"branches:create\",\n    BRANCHES_EDIT: \"branches:edit\",\n    BRANCHES_DELETE: \"branches:delete\",\n    // Warehouse Management\n    WAREHOUSES_VIEW: \"warehouses:view\",\n    WAREHOUSES_CREATE: \"warehouses:create\",\n    WAREHOUSES_EDIT: \"warehouses:edit\",\n    WAREHOUSES_DELETE: \"warehouses:delete\",\n    // Product Management\n    PRODUCTS_VIEW: \"products:view\",\n    PRODUCTS_CREATE: \"products:create\",\n    PRODUCTS_EDIT: \"products:edit\",\n    PRODUCTS_DELETE: \"products:delete\",\n    // Sales\n    SALES_VIEW: \"sales:view\",\n    SALES_CREATE: \"sales:create\",\n    SALES_EDIT: \"sales:edit\",\n    SALES_DELETE: \"sales:delete\",\n    // Purchases\n    PURCHASES_VIEW: \"purchases:view\",\n    PURCHASES_CREATE: \"purchases:create\",\n    PURCHASES_EDIT: \"purchases:edit\",\n    PURCHASES_DELETE: \"purchases:delete\",\n    PURCHASES_APPROVE: \"purchases:approve\",\n    PURCHASES_RECEIVE: \"purchases:receive\",\n    PURCHASES_PAY: \"purchases:pay\",\n    PURCHASES_CANCEL: \"purchases:cancel\",\n    PURCHASES_PROCESS: \"purchases:process\",\n    // POS\n    POS_ACCESS: \"pos:access\",\n    POS_CLOSE_DAY: \"pos:close_day\",\n    // Cash Registers\n    CASH_REGISTERS_VIEW: \"cash_registers:view\",\n    CASH_REGISTERS_CREATE: \"cash_registers:create\",\n    CASH_REGISTERS_EDIT: \"cash_registers:edit\",\n    CASH_REGISTERS_DELETE: \"cash_registers:delete\",\n    // Accounting\n    ACCOUNTING_VIEW: \"accounting:view\",\n    ACCOUNTING_EDIT: \"accounting:edit\",\n    ACCOUNTING_MANAGE: \"accounting:manage\",\n    ACCOUNTING_CREATE: \"accounting:create\",\n    ACCOUNTING_DELETE: \"accounting:delete\",\n    // Reports\n    REPORTS_VIEW: \"reports:view\",\n    REPORTS_EXPORT: \"reports:export\"\n};\n// Organized permissions for easier use\nconst PERMISSIONS_ORGANIZED = {\n    SALES: {\n        VIEW: PERMISSIONS.SALES_VIEW,\n        CREATE: PERMISSIONS.SALES_CREATE,\n        EDIT: PERMISSIONS.SALES_EDIT,\n        DELETE: PERMISSIONS.SALES_DELETE\n    },\n    PURCHASES: {\n        VIEW: PERMISSIONS.PURCHASES_VIEW,\n        CREATE: PERMISSIONS.PURCHASES_CREATE,\n        EDIT: PERMISSIONS.PURCHASES_EDIT,\n        DELETE: PERMISSIONS.PURCHASES_DELETE,\n        APPROVE: PERMISSIONS.PURCHASES_APPROVE,\n        RECEIVE: PERMISSIONS.PURCHASES_RECEIVE,\n        PAY: PERMISSIONS.PURCHASES_PAY,\n        CANCEL: PERMISSIONS.PURCHASES_CANCEL,\n        PROCESS: PERMISSIONS.PURCHASES_PROCESS\n    }\n};\nconst ROLE_PERMISSIONS = {\n    admin: [\n        // User Management\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.USERS_CREATE,\n        PERMISSIONS.USERS_EDIT,\n        PERMISSIONS.USERS_DELETE,\n        // Branch Management\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.BRANCHES_DELETE,\n        // Warehouse Management\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.WAREHOUSES_DELETE,\n        // Product Management\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.PRODUCTS_DELETE,\n        // Sales\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.SALES_DELETE,\n        // Purchases\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_DELETE,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        // POS\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.POS_CLOSE_DAY,\n        // Cash Registers\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.CASH_REGISTERS_DELETE,\n        // Accounting\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        // Reports\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    manager: [\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    employee: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.REPORTS_VIEW\n    ],\n    cashier: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.CASH_REGISTERS_VIEW\n    ]\n};\nasync function signIn(email, password) {\n    // Mock authentication for demo\n    await new Promise((resolve)=>setTimeout(resolve, 1000));\n    if (email && password) {\n        return {\n            user: {\n                id: \"1\",\n                email\n            }\n        };\n    }\n    throw new Error(\"Invalid credentials\");\n}\nasync function signOut() {\n    // Mock sign out\n    await new Promise((resolve)=>setTimeout(resolve, 500));\n}\nasync function getCurrentUser() {\n    // Mock user for demo - تأكد من أن جميع الصلاحيات متضمنة\n    const adminPermissions = [\n        // User Management\n        \"users:view\",\n        \"users:create\",\n        \"users:edit\",\n        \"users:delete\",\n        // Branch Management\n        \"branches:view\",\n        \"branches:create\",\n        \"branches:edit\",\n        \"branches:delete\",\n        // Warehouse Management\n        \"warehouses:view\",\n        \"warehouses:create\",\n        \"warehouses:edit\",\n        \"warehouses:delete\",\n        // Product Management\n        \"products:view\",\n        \"products:create\",\n        \"products:edit\",\n        \"products:delete\",\n        // Sales\n        \"sales:view\",\n        \"sales:create\",\n        \"sales:edit\",\n        \"sales:delete\",\n        // Purchases\n        \"purchases:view\",\n        \"purchases:create\",\n        \"purchases:edit\",\n        \"purchases:delete\",\n        \"purchases:approve\",\n        \"purchases:receive\",\n        \"purchases:pay\",\n        \"purchases:cancel\",\n        \"purchases:process\",\n        // POS\n        \"pos:access\",\n        \"pos:close_day\",\n        // Cash Registers\n        \"cash_registers:view\",\n        \"cash_registers:create\",\n        \"cash_registers:edit\",\n        \"cash_registers:delete\",\n        // Accounting\n        \"accounting:view\",\n        \"accounting:edit\",\n        \"accounting:manage\",\n        \"accounting:create\",\n        \"accounting:delete\",\n        // Reports\n        \"reports:view\",\n        \"reports:export\"\n    ];\n    return {\n        id: \"1\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام\",\n        role: \"admin\",\n        branch_id: \"1\",\n        warehouse_id: \"1\",\n        pos_id: \"1\",\n        is_active: true,\n        created_at: \"2024-01-01\",\n        updated_at: \"2024-01-01\",\n        permissions: adminPermissions\n    };\n}\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    return user.permissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    return permissions.some((permission)=>user.permissions.includes(permission));\n}\nfunction hasAllPermissions(user, permissions) {\n    if (!user) return false;\n    return permissions.every((permission)=>user.permissions.includes(permission));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/auth.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUM4QztBQUNqQjtBQUVkLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILHdEQUFZQTtrQkFDWCw0RUFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0g7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJ1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/index.tsx":
/*!*****************************!*\
  !*** ./src/pages/index.tsx ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n\n\n\n\nfunction Home() {\n    const { user, loading } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!loading) {\n            if (user) {\n                router.push(\"/dashboard\");\n            } else {\n                router.push(\"/login\");\n            }\n        }\n    }, [\n        user,\n        loading,\n        router\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"\n        }, void 0, false, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\index.tsx\",\n            lineNumber: 21,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\index.tsx\",\n        lineNumber: 20,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFpQztBQUNNO0FBQ0U7QUFFMUIsU0FBU0c7SUFDdEIsTUFBTSxFQUFFQyxJQUFJLEVBQUVDLE9BQU8sRUFBRSxHQUFHSCx1REFBT0E7SUFDakMsTUFBTUksU0FBU0wsc0RBQVNBO0lBRXhCRCxnREFBU0EsQ0FBQztRQUNSLElBQUksQ0FBQ0ssU0FBUztZQUNaLElBQUlELE1BQU07Z0JBQ1JFLE9BQU9DLElBQUksQ0FBQztZQUNkLE9BQU87Z0JBQ0xELE9BQU9DLElBQUksQ0FBQztZQUNkO1FBQ0Y7SUFDRixHQUFHO1FBQUNIO1FBQU1DO1FBQVNDO0tBQU87SUFFMUIscUJBQ0UsOERBQUNFO1FBQUlDLFdBQVU7a0JBQ2IsNEVBQUNEO1lBQUlDLFdBQVU7Ozs7Ozs7Ozs7O0FBR3JCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vLi9zcmMvcGFnZXMvaW5kZXgudHN4PzE5YTAiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlRWZmZWN0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L3JvdXRlcidcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2hvb2tzL3VzZUF1dGgnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEhvbWUoKSB7XG4gIGNvbnN0IHsgdXNlciwgbG9hZGluZyB9ID0gdXNlQXV0aCgpXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWxvYWRpbmcpIHtcbiAgICAgIGlmICh1c2VyKSB7XG4gICAgICAgIHJvdXRlci5wdXNoKCcvZGFzaGJvYXJkJylcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHJvdXRlci5wdXNoKCcvbG9naW4nKVxuICAgICAgfVxuICAgIH1cbiAgfSwgW3VzZXIsIGxvYWRpbmcsIHJvdXRlcl0pXG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLXNjcmVlbiBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gcm91bmRlZC1mdWxsIGgtMzIgdy0zMiBib3JkZXItYi0yIGJvcmRlci1wcmltYXJ5XCI+PC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VFZmZlY3QiLCJ1c2VSb3V0ZXIiLCJ1c2VBdXRoIiwiSG9tZSIsInVzZXIiLCJsb2FkaW5nIiwicm91dGVyIiwicHVzaCIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/index.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2F&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cindex.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();