# 📚 الدليل الشامل لنظام إدارة الأعمال (ERP)

## 🎯 **مرحباً بك في نظام إدارة الأعمال المتكامل**

هذا الدليل الشامل يغطي جميع موديولات النظام بالتفصيل، مع شرح كامل لكل ميزة ووظيفة لضمان الاستخدام الأمثل للنظام.

---

## 📋 **فهرس الموديولات**

### 🏗️ **الموديولات الأساسية**
1. [⚙️ الإعدادات العامة](#setup) - الأساس لجميع الموديولات
2. [👥 جهات الاتصال](#contacts) - العملاء والموردين
3. [📦 إدارة المخازن](#inventory) - المنتجات والمخزون

### 💼 **الموديولات التجارية**
4. [💰 المبيعات](#sales) - من العروض للفواتير
5. [🛒 المشتريات](#purchases) - طلبات وفواتير الشراء
6. [💳 المحاسبة](#accounting) - النظام المالي

### 🔧 **الموديولات المتخصصة**
7. [🔧 الصيانة الفنية](#maintenance) - إدارة خدمات الصيانة

---

## ⚙️ **1. موديول الإعدادات العامة** {#setup}

> **📍 الوصول**: `/setup`  
> **🎯 الأهمية**: أساسي - يجب إعداده أولاً

### 🏢 **إدارة الفروع**
- إنشاء وإدارة فروع متعددة
- ربط كل فرع بمخازن وصناديق نقد
- تتبع الأنشطة لكل فرع منفصل

### 📦 **إدارة المخازن**
- مخازن متعددة لكل فرع
- تصنيف المخازن حسب النوع
- إدارة المواقع داخل المخزن

### 💰 **صناديق النقد**
- صناديق متعددة لكل فرع
- تتبع الحركات النقدية
- إدارة الورديات

### 👥 **المستخدمين والصلاحيات**
- إدارة حسابات المستخدمين
- تحديد الصلاحيات التفصيلية
- أدوار وظيفية محددة مسبقاً

**📖 [دليل مفصل للإعدادات](./USER_GUIDE_SETUP.md)**

---

## 👥 **2. موديول جهات الاتصال** {#contacts}

> **📍 الوصول**: `/contacts`  
> **🎯 الأهمية**: أساسي - قاعدة بيانات العملاء والموردين

### ✨ **الميزات الرئيسية**
- نظام موحد للعملاء والموردين
- تصنيف مرن (عميل، مورد، أو كليهما)
- كشوف حسابات شاملة
- تتبع تاريخ التعاملات

### 🔧 **الوظائف الأساسية**
- إضافة وتعديل جهات الاتصال
- عرض كشف الحساب التفصيلي
- تتبع المعاملات والأنشطة
- تقارير وتحليلات شاملة

### 🔄 **التكامل مع الموديولات**
- ربط تلقائي مع المبيعات والمشتريات
- تحديث كشوف الحسابات تلقائياً
- تكامل مع الصيانة والمحاسبة

**📖 [دليل مفصل لجهات الاتصال](./USER_GUIDE_CONTACTS.md)**

---

## 📦 **3. موديول إدارة المخازن** {#inventory}

> **📍 الوصول**: `/inventory`  
> **🎯 الأهمية**: أساسي - قلب النظام

### 🏷️ **إدارة المنتجات**
- قاعدة بيانات شاملة للمنتجات
- تصنيفات وفئات متعددة
- دعم المنتجات المركبة
- إدارة الأسعار والتكاليف

### 📊 **حركات المخزون**
- تتبع جميع حركات المخزون
- تسجيل تلقائي من المبيعات والمشتريات
- حركات يدوية للتعديلات
- تاريخ كامل لكل منتج

### 🔄 **التحويلات بين المخازن**
- نقل المخزون بين المخازن
- تتبع حالة التحويل
- تأكيد الاستلام

### 📈 **التقارير والتحليلات**
- تقارير المخزون الحالي
- تحليل حركات المخزون
- تقييم المخزون
- ABC Analysis

**📖 [دليل مفصل لإدارة المخازن](./USER_GUIDE_INVENTORY.md)**

---

## 💰 **4. موديول المبيعات** {#sales}

> **📍 الوصول**: `/sales`  
> **🎯 الأهمية**: تجاري - إدارة دورة المبيعات الكاملة

### 💼 **عروض الأسعار**
- إنشاء عروض احترافية
- تتبع حالة العروض
- تحويل العروض لأوامر بيع

### 📦 **أوامر البيع**
- تحويل العروض لأوامر
- حجز المخزون تلقائياً
- تتبع حالة التنفيذ

### 🧾 **فواتير المبيعات**
- إنشاء فواتير من الأوامر
- خصم المخزون تلقائياً
- تكامل مع المحاسبة

### 🔄 **مرتجعات المبيعات**
- ربط بفواتير المبيعات
- إرجاع جزئي أو كامل
- إعادة المخزون تلقائياً

**📖 [دليل مفصل للمبيعات](./USER_GUIDE_SALES.md)**

---

## 🛒 **5. موديول المشتريات** {#purchases}

> **📍 الوصول**: `/purchases`  
> **🎯 الأهمية**: تجاري - إدارة دورة المشتريات الكاملة

### 📝 **طلبات الشراء**
- إنشاء طلبات للموردين
- تتبع حالة الطلبات
- تحويل الطلبات لفواتير

### 🧾 **فواتير المشتريات**
- إنشاء فواتير من الطلبات
- إضافة المخزون تلقائياً
- تكامل مع المحاسبة

### 🔄 **مرتجعات المشتريات**
- ربط بفواتير المشتريات
- إرجاع للموردين
- خصم من المخزون

### 👥 **إدارة الموردين**
- قاعدة بيانات الموردين
- تقييم أداء الموردين
- كشوف حسابات الموردين

**📖 [دليل مفصل للمشتريات](./USER_GUIDE_PURCHASES.md)**

---

## 💳 **6. موديول المحاسبة** {#accounting}

> **📍 الوصول**: `/accounting`  
> **🎯 الأهمية**: مالي - النظام المحاسبي المبسط

### 🏦 **إدارة الحسابات**
- دليل حسابات مبسط
- تصنيف واضح للحسابات
- أرصدة محدثة تلقائياً

### 💳 **التدفق النقدي**
- تتبع المقبوضات والمدفوعات
- تصنيف تلقائي للمعاملات
- تقارير التدفق النقدي

### 💸 **المصروفات**
- تسجيل وتصنيف المصروفات
- ربط مع الموردين
- تقارير تحليلية

### 📊 **كشوف الحسابات**
- كشوف شاملة للعملاء والموردين
- تحليل أعمار الديون
- تقارير الذمم

### 💳 **الأقساط**
- خطط دفع مرنة
- تتبع المدفوعات
- حساب الفوائد (اختياري)

**📖 [دليل مفصل للمحاسبة](./USER_GUIDE_ACCOUNTING.md)**

---

## 🔧 **7. موديول الصيانة الفنية** {#maintenance}

> **📍 الوصول**: `/maintenance`  
> **🎯 الأهمية**: متخصص - إدارة خدمات الصيانة

### 📝 **إدارة طلبات الصيانة**
- سير عمل محدد من الاستلام للتسليم
- تكامل مع قاعدة العملاء
- تتبع دقيق لكل طلب

### 🔄 **سير العمل المتقدم**
- 7 مراحل محددة للصيانة
- تحويلات محمية بين المراحل
- رسائل واتساب تلقائية

### 🎫 **نظام التذاكر**
- تذكرة تلقائية لكل طلب
- عد تنازلي للمواعيد
- تنبيهات عاجلة

### 📦 **التكامل مع المخزون**
- استخدام قطع الغيار
- خصم تلقائي من المخزون
- تتبع التكاليف

### 💰 **التكامل مع المحاسبة**
- فواتير صيانة تلقائية
- تحديث كشوف العملاء
- تقارير الإيرادات

**📖 [دليل مفصل للصيانة الفنية](./USER_GUIDE_MAINTENANCE.md)**

---

## 🔄 **التكامل بين الموديولات**

### 🌐 **الشبكة المترابطة**
جميع موديولات النظام مترابطة ومتكاملة:

```
الإعدادات ←→ جهات الاتصال ←→ المخازن
     ↕              ↕              ↕
المحاسبة ←→ المبيعات/المشتريات ←→ الصيانة
```

### 🔗 **أمثلة التكامل**
- **فاتورة مبيعات** → خصم مخزون + تحديث كشف عميل + تدفق نقدي
- **طلب صيانة** → ربط عميل + استخدام قطع غيار + إنشاء فاتورة
- **فاتورة شراء** → زيادة مخزون + تحديث كشف مورد + تدفق نقدي

---

## 🎯 **دليل البدء السريع**

### 📋 **خطوات الإعداد الأولي**
1. **⚙️ الإعدادات**: إنشاء الفروع والمخازن وصناديق النقد
2. **👥 جهات الاتصال**: إضافة العملاء والموردين الأساسيين
3. **📦 المنتجات**: إنشاء قاعدة بيانات المنتجات
4. **💰 الحسابات**: مراجعة دليل الحسابات
5. **👤 المستخدمين**: إضافة المستخدمين وتحديد الصلاحيات

### 🚀 **البدء في العمل**
1. **📝 إنشاء أول فاتورة مبيعات**
2. **🛒 تسجيل أول فاتورة شراء**
3. **🔧 إنشاء أول طلب صيانة**
4. **📊 مراجعة التقارير الأولية**

---

## 📞 **الدعم والمساعدة**

### 🆘 **في حالة وجود مشاكل**
1. **📖 راجع الدليل المناسب** للموديول
2. **🔍 تحقق من البيانات** المدخلة
3. **🔐 تأكد من الصلاحيات** المطلوبة
4. **📞 تواصل مع الدعم الفني**

### 📚 **الأدلة التفصيلية**
- [⚙️ دليل الإعدادات العامة](./USER_GUIDE_SETUP.md)
- [👥 دليل جهات الاتصال](./USER_GUIDE_CONTACTS.md)
- [📦 دليل إدارة المخازن](./USER_GUIDE_INVENTORY.md)
- [💰 دليل المبيعات](./USER_GUIDE_SALES.md)
- [🛒 دليل المشتريات](./USER_GUIDE_PURCHASES.md)
- [💳 دليل المحاسبة](./USER_GUIDE_ACCOUNTING.md)
- [🔧 دليل الصيانة الفنية](./USER_GUIDE_MAINTENANCE.md)

---

## 🎉 **مرحباً بك في رحلة إدارة الأعمال الذكية!**

هذا النظام مصمم ليكون شريكك في النجاح، مع التركيز على البساطة والفعالية والتكامل الشامل بين جميع جوانب عملك.

**💡 نصيحة**: ابدأ بالموديولات الأساسية (الإعدادات، جهات الاتصال، المخازن) ثم انتقل تدريجياً للموديولات المتخصصة حسب احتياجاتك.
