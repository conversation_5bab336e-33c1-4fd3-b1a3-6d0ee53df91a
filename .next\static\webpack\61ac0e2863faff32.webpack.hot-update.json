{"c": ["pages/sales", "webpack"], "r": ["pages/products", "/_error"], "m": ["./node_modules/@radix-ui/react-dialog/dist/index.mjs", "./node_modules/lucide-react/dist/esm/icons/minus.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Cproducts%5Cindex.tsx&page=%2Fproducts!", "./src/components/products/ProductDetailsDialog.tsx", "./src/components/products/ProductForm.tsx", "./src/components/ui/dialog.tsx", "./src/pages/products/index.tsx", "__barrel_optimize__?names=<PERSON><PERSON><PERSON><PERSON>gle,BarChart3,Edit,Eye,Package,Plus,RefreshCw,Search,Trash2,TrendingDown,TrendingUp,Warehouse!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Minus,Package,Plus,ShoppingCart,TrendingDown,TrendingUp,Warehouse!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=X!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!"]}