import { useState } from 'react'
import { <PERSON>, Card<PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  XCircle, 
  Database, 
  User,
  ArrowRight,
  Settings
} from 'lucide-react'

export default function SimpleSetup() {
  const [step, setStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [adminData, setAdminData] = useState({
    email: '<EMAIL>',
    password: 'V@admin010',
    fullName: 'مدير النظام'
  })

  const setupDatabase = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/postgres/setup-integrated', {
        method: 'POST'
      })
      
      const data = await response.json()
      setResult(data)
      
      if (data.success) {
        setStep(2)
      }
    } catch (err) {
      setResult({
        success: false,
        message: 'خطأ في تنفيذ الإعداد'
      })
    } finally {
      setLoading(false)
    }
  }

  const createAdmin = async () => {
    setLoading(true)
    
    try {
      const response = await fetch('/api/postgres/create-admin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(adminData)
      })
      
      const data = await response.json()
      setResult(data)
      
      if (data.success) {
        setStep(3)
      }
    } catch (err) {
      setResult({
        success: false,
        message: 'خطأ في إنشاء المدير'
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            إعداد النظام البسيط
          </h1>
          <p className="text-xl text-gray-600">
            إعداد سريع وسهل للنظام المتكامل
          </p>
        </div>

        {/* Progress */}
        <div className="flex justify-center">
          <div className="flex items-center space-x-4">
            <div className={`flex items-center justify-center w-10 h-10 rounded-full ${step >= 1 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <div className={`w-16 h-1 ${step >= 2 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
            <div className={`flex items-center justify-center w-10 h-10 rounded-full ${step >= 2 ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
              2
            </div>
            <div className={`w-16 h-1 ${step >= 3 ? 'bg-blue-600' : 'bg-gray-200'}`}></div>
            <div className={`flex items-center justify-center w-10 h-10 rounded-full ${step >= 3 ? 'bg-green-600 text-white' : 'bg-gray-200'}`}>
              3
            </div>
          </div>
        </div>

        {/* Step 1: Database Setup */}
        {step === 1 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-6 w-6 mr-2" />
                الخطوة 1: إعداد قاعدة البيانات
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-600">
                سيتم إنشاء جميع الجداول والبيانات المطلوبة للنظام
              </p>
              
              <Button 
                onClick={setupDatabase}
                disabled={loading}
                className="w-full bg-blue-600 hover:bg-blue-700"
                size="lg"
              >
                {loading ? (
                  <Database className="h-5 w-5 mr-2 animate-pulse" />
                ) : (
                  <Database className="h-5 w-5 mr-2" />
                )}
                {loading ? 'جاري إعداد قاعدة البيانات...' : 'بدء إعداد قاعدة البيانات'}
              </Button>

              {result && (
                <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                  {result.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription>
                    <p className="font-medium">{result.message}</p>
                    {result.summary && (
                      <div className="mt-2 text-sm">
                        <p>الجداول المُنشأة: {result.summary.tablesCreated}</p>
                        <p>الاستعلامات الناجحة: {result.summary.successful}</p>
                      </div>
                    )}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        )}

        {/* Step 2: Create Admin */}
        {step === 2 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <User className="h-6 w-6 mr-2" />
                الخطوة 2: إنشاء المدير الأولي
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-1">الاسم الكامل</label>
                  <Input
                    value={adminData.fullName}
                    onChange={(e) => setAdminData({...adminData, fullName: e.target.value})}
                    placeholder="أدخل الاسم الكامل"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">البريد الإلكتروني</label>
                  <Input
                    type="email"
                    value={adminData.email}
                    onChange={(e) => setAdminData({...adminData, email: e.target.value})}
                    placeholder="أدخل البريد الإلكتروني"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium mb-1">كلمة المرور</label>
                  <Input
                    type="password"
                    value={adminData.password}
                    onChange={(e) => setAdminData({...adminData, password: e.target.value})}
                    placeholder="أدخل كلمة المرور"
                  />
                </div>
              </div>

              <Button 
                onClick={createAdmin}
                disabled={loading}
                className="w-full bg-green-600 hover:bg-green-700"
                size="lg"
              >
                {loading ? (
                  <User className="h-5 w-5 mr-2 animate-pulse" />
                ) : (
                  <User className="h-5 w-5 mr-2" />
                )}
                {loading ? 'جاري إنشاء المدير...' : 'إنشاء المدير الأولي'}
              </Button>

              {result && (
                <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                  {result.success ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <XCircle className="h-4 w-4 text-red-600" />
                  )}
                  <AlertDescription>
                    <p className="font-medium">{result.message}</p>
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        )}

        {/* Step 3: Complete */}
        {step === 3 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center text-green-700">
                <CheckCircle className="h-6 w-6 mr-2" />
                تم الإعداد بنجاح!
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center space-y-4">
                <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
                <h3 className="text-xl font-semibold">النظام جاهز للاستخدام!</h3>
                <p className="text-gray-600">
                  تم إعداد قاعدة البيانات وإنشاء المدير الأولي بنجاح
                </p>
                
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">بيانات تسجيل الدخول:</h4>
                  <p><strong>البريد الإلكتروني:</strong> {adminData.email}</p>
                  <p><strong>كلمة المرور:</strong> {adminData.password}</p>
                </div>

                <div className="flex gap-4 justify-center">
                  <Button 
                    onClick={() => window.location.href = '/login'}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    <ArrowRight className="h-4 w-4 mr-2" />
                    تسجيل الدخول
                  </Button>
                  
                  <Button 
                    onClick={() => window.location.href = '/integrated-dashboard'}
                    variant="outline"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    لوحة التحكم
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
