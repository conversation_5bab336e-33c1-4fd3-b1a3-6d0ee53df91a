"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/postgres/test-simple";
exports.ids = ["pages/api/postgres/test-simple"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Ftest-simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ctest-simple.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Ftest-simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ctest-simple.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_postgres_test_simple_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\postgres\\test-simple.ts */ \"(api)/./src/pages/api/postgres/test-simple.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_test_simple_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_test_simple_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/postgres/test-simple\",\n        pathname: \"/api/postgres/test-simple\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_postgres_test_simple_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Ftest-simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ctest-simple.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/postgres.ts":
/*!*****************************!*\
  !*** ./src/lib/postgres.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTables: () => (/* binding */ checkTables),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   findUser: () => (/* binding */ findUser),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\n// إعداد الاتصال بقاعدة البيانات PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"5432\"),\n    database: process.env.DB_NAME || \"Vero_ERP_ABA\",\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    ssl:  false ? 0 : false,\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const client = await pool.connect();\n        const result = await client.query(\"SELECT NOW()\");\n        client.release();\n        return {\n            success: true,\n            time: result.rows[0].now\n        };\n    } catch (error) {\n        console.error(\"خطأ في الاتصال بقاعدة البيانات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    try {\n        const result = await pool.query(text, params);\n        return {\n            success: true,\n            data: result.rows,\n            rowCount: result.rowCount\n        };\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للتحقق من وجود الجداول\nconst checkTables = async ()=>{\n    try {\n        const result = await query(`\n      SELECT table_name \n      FROM information_schema.tables \n      WHERE table_schema = 'public' \n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = result.data?.map((row)=>row.table_name) || [];\n        return {\n            success: true,\n            tables,\n            hasBranches: tables.includes(\"branches\"),\n            hasUsers: tables.includes(\"users\"),\n            hasProducts: tables.includes(\"products\"),\n            hasWarehouses: tables.includes(\"warehouses\"),\n            needsSetup: !tables.includes(\"branches\")\n        };\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return {\n            success: false,\n            error,\n            needsSetup: true\n        };\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const result = await query(`\n      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)\n      VALUES ($1, $2, $3, $4, $5, true, NOW())\n      RETURNING id, email, username, full_name, role\n    `, [\n            userData.email,\n            userData.username,\n            userData.full_name,\n            userData.password_hash,\n            userData.role || \"admin\"\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0]\n        };\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للبحث عن مستخدم\nconst findUser = async (email)=>{\n    try {\n        const result = await query(`\n      SELECT id, email, username, full_name, role, password_hash, is_active\n      FROM users \n      WHERE email = $1 AND is_active = true\n    `, [\n            email\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0] || null\n        };\n    } catch (error) {\n        console.error(\"خطأ في البحث عن المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/postgres.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/postgres/test-simple.ts":
/*!***********************************************!*\
  !*** ./src/pages/api/postgres/test-simple.ts ***!
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/postgres */ \"(api)/./src/lib/postgres.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const results = [];\n        // اختبار 1: استعلام بسيط\n        console.log(\"اختبار 1: استعلام بسيط\");\n        const test1 = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT NOW() as current_time, 'اختبار الاتصال' as message\");\n        results.push({\n            test: \"اختبار الاتصال\",\n            success: test1.success,\n            data: test1.data,\n            error: test1.error\n        });\n        // اختبار 2: فحص الجداول الموجودة\n        console.log(\"اختبار 2: فحص الجداول الموجودة\");\n        const test2 = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT table_name \n      FROM information_schema.tables \n      WHERE table_schema = 'public'\n      ORDER BY table_name\n    `);\n        results.push({\n            test: \"فحص الجداول الموجودة\",\n            success: test2.success,\n            data: test2.data,\n            error: test2.error\n        });\n        // اختبار 3: فحص الأنواع المخصصة\n        console.log(\"اختبار 3: فحص الأنواع المخصصة\");\n        const test3 = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT typname \n      FROM pg_type \n      WHERE typname IN ('user_role', 'transaction_type', 'payment_method')\n    `);\n        results.push({\n            test: \"فحص الأنواع المخصصة\",\n            success: test3.success,\n            data: test3.data,\n            error: test3.error\n        });\n        // اختبار 4: إنشاء جدول بسيط\n        console.log(\"اختبار 4: إنشاء جدول بسيط\");\n        const test4 = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      CREATE TABLE IF NOT EXISTS test_table (\n        id SERIAL PRIMARY KEY,\n        name VARCHAR(100),\n        created_at TIMESTAMP DEFAULT NOW()\n      )\n    `);\n        results.push({\n            test: \"إنشاء جدول بسيط\",\n            success: test4.success,\n            data: test4.data,\n            error: test4.error\n        });\n        // اختبار 5: إدراج بيانات في الجدول التجريبي\n        console.log(\"اختبار 5: إدراج بيانات\");\n        const test5 = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      INSERT INTO test_table (name) \n      VALUES ('اختبار البيانات') \n      ON CONFLICT DO NOTHING\n    `);\n        results.push({\n            test: \"إدراج بيانات\",\n            success: test5.success,\n            data: test5.data,\n            error: test5.error\n        });\n        // اختبار 6: قراءة البيانات\n        console.log(\"اختبار 6: قراءة البيانات\");\n        const test6 = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(\"SELECT * FROM test_table LIMIT 5\");\n        results.push({\n            test: \"قراءة البيانات\",\n            success: test6.success,\n            data: test6.data,\n            error: test6.error\n        });\n        // حذف الجدول التجريبي\n        await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(\"DROP TABLE IF EXISTS test_table\");\n        // حساب الإحصائيات\n        const successCount = results.filter((r)=>r.success).length;\n        const failCount = results.filter((r)=>!r.success).length;\n        return res.status(200).json({\n            success: successCount > 0,\n            message: `تم تنفيذ ${successCount} اختبار بنجاح، ${failCount} فشل`,\n            summary: {\n                totalTests: results.length,\n                successful: successCount,\n                failed: failCount\n            },\n            results,\n            database: {\n                host: process.env.DB_HOST,\n                port: process.env.DB_PORT,\n                name: process.env.DB_NAME,\n                user: process.env.DB_USER\n            }\n        });\n    } catch (error) {\n        console.error(\"خطأ في الاختبار:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/postgres/test-simple.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Ftest-simple&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ctest-simple.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();