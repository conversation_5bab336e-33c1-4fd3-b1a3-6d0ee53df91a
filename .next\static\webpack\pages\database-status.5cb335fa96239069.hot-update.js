"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/database-status",{

/***/ "./src/pages/database-status.tsx":
/*!***************************************!*\
  !*** ./src/pages/database-status.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DatabaseStatus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,ExternalLink,RefreshCw,XCircle!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Database,ExternalLink,RefreshCw,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DatabaseStatus() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [setupLoading, setSetupLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [setupResult, setSetupResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchStatus = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/postgres/check-tables\");\n            const data = await response.json();\n            if (data.success) {\n                setStatus(data);\n            } else {\n                setError(data.message || \"فشل في فحص قاعدة البيانات\");\n            }\n        } catch (err) {\n            setError(\"خطأ في الاتصال بالخادم\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStatus();\n    }, []);\n    const setupDatabase = async ()=>{\n        setSetupLoading(true);\n        setSetupResult(null);\n        try {\n            const response = await fetch(\"/api/postgres/setup-database\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            setSetupResult(data);\n            // إعادة فحص الجداول بعد الإعداد\n            if (data.success) {\n                setTimeout(()=>{\n                    fetchStatus();\n                }, 1000);\n            }\n        } catch (err) {\n            setSetupResult({\n                success: false,\n                message: \"خطأ في تنفيذ الإعداد\"\n            });\n        } finally{\n            setSetupLoading(false);\n        }\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.RefreshCw, {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"جاري فحص قاعدة البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"حالة قاعدة البيانات PostgreSQL\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"فحص الجداول والإعدادات المطلوبة للنظام\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.XCircle, {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 11\n                }, this),\n                (status === null || status === void 0 ? void 0 : status.database) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Database, {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"معلومات قاعدة البيانات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"اسم قاعدة البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: status.database.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"المضيف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: status.database.host\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"حالة الاتصال\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: status.database.connected ? \"✅ متصل\" : \"❌ غير متصل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this),\n                (status === null || status === void 0 ? void 0 : status.summary) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"ملخص الحالة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: status.summary.totalRequired\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"المطلوب\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: status.summary.existing\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"موجود\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-red-600\",\n                                                    children: status.summary.missing\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"مفقود\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: status.summary.setupComplete ? \"✅\" : \"⚠️\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium\",\n                                        children: status.summary.status\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this),\n                (status === null || status === void 0 ? void 0 : status.tables) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"حالة الجداول\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: Object.entries(status.tables).map((param)=>{\n                                    let [tableName, tableInfo] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 rounded-lg border \".concat(tableInfo.exists ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: tableName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    tableInfo.exists ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.CheckCircle, {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.XCircle, {\n                                                        className: \"h-5 w-5 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: tableInfo.status\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, tableName, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this),\n                (status === null || status === void 0 ? void 0 : status.recommendations) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"التوصيات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: status.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: rec\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this),\n                setupResult && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                    className: setupResult.success ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\",\n                    children: [\n                        setupResult.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.CheckCircle, {\n                            className: \"h-4 w-4 text-green-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 265,\n                            columnNumber: 15\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.XCircle, {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 267,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-medium\",\n                                        children: setupResult.message\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, this),\n                                    setupResult.summary && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"الاستعلامات الناجحة: \",\n                                                    setupResult.summary.successful\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"الاستعلامات الفاشلة: \",\n                                                    setupResult.summary.failed\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 275,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: [\n                                                    \"الجداول المُنشأة: \",\n                                                    setupResult.summary.tablesCreated\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 263,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4 justify-center flex-wrap\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: fetchStatus,\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.RefreshCw, {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 287,\n                                    columnNumber: 13\n                                }, this),\n                                \"إعادة فحص\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 286,\n                            columnNumber: 11\n                        }, this),\n                        (status === null || status === void 0 ? void 0 : status.summary) && !status.summary.setupComplete && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: setupDatabase,\n                            disabled: setupLoading,\n                            className: \"flex items-center bg-green-600 hover:bg-green-700\",\n                            children: [\n                                setupLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.RefreshCw, {\n                                    className: \"h-4 w-4 mr-2 animate-spin\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 298,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Database, {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 300,\n                                    columnNumber: 17\n                                }, this),\n                                setupLoading ? \"جاري الإعداد...\" : \"إعداد قاعدة البيانات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.open(\"/api/postgres/check-tables\", \"_blank\"),\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ExternalLink, {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                \"عرض JSON\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 285,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseStatus, \"cDnSqfkiAtcJG30LV1Hr3X4Nggs=\");\n_c = DatabaseStatus;\nvar _c;\n$RefreshReg$(_c, \"DatabaseStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/database-status.tsx\n"));

/***/ })

});