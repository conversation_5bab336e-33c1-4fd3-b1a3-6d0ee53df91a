import { NextApiRequest, NextApiResponse } from 'next'
import { 
  getProducts, 
  getInventory, 
  getCustomers, 
  getSuppliers,
  getSalesOrders,
  getPurchaseOrders,
  getStockMovements,
  getMaintenanceRequests
} from '@/lib/postgres'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  const { type } = req.query

  try {
    let result

    switch (type) {
      case 'products':
        result = await getProducts()
        break
      
      case 'inventory':
        result = await getInventory()
        break
      
      case 'customers':
        result = await getCustomers()
        break
      
      case 'suppliers':
        result = await getSuppliers()
        break
      
      case 'sales-orders':
        result = await getSalesOrders()
        break
      
      case 'purchase-orders':
        result = await getPurchaseOrders()
        break
      
      case 'stock-movements':
        result = await getStockMovements()
        break
      
      case 'maintenance-requests':
        result = await getMaintenanceRequests()
        break
      
      case 'all':
        // جلب جميع البيانات
        const [
          products,
          inventory,
          customers,
          suppliers,
          salesOrders,
          purchaseOrders,
          stockMovements,
          maintenanceRequests
        ] = await Promise.all([
          getProducts(),
          getInventory(),
          getCustomers(),
          getSuppliers(),
          getSalesOrders(),
          getPurchaseOrders(),
          getStockMovements(),
          getMaintenanceRequests()
        ])

        return res.status(200).json({
          success: true,
          data: {
            products: products.data || [],
            inventory: inventory.data || [],
            customers: customers.data || [],
            suppliers: suppliers.data || [],
            salesOrders: salesOrders.data || [],
            purchaseOrders: purchaseOrders.data || [],
            stockMovements: stockMovements.data || [],
            maintenanceRequests: maintenanceRequests.data || []
          },
          summary: {
            productsCount: products.data?.length || 0,
            inventoryCount: inventory.data?.length || 0,
            customersCount: customers.data?.length || 0,
            suppliersCount: suppliers.data?.length || 0,
            salesOrdersCount: salesOrders.data?.length || 0,
            purchaseOrdersCount: purchaseOrders.data?.length || 0,
            stockMovementsCount: stockMovements.data?.length || 0,
            maintenanceRequestsCount: maintenanceRequests.data?.length || 0
          }
        })
      
      default:
        return res.status(400).json({
          success: false,
          message: 'نوع البيانات غير صحيح',
          availableTypes: [
            'products', 'inventory', 'customers', 'suppliers',
            'sales-orders', 'purchase-orders', 'stock-movements',
            'maintenance-requests', 'all'
          ]
        })
    }

    if (result.success) {
      return res.status(200).json({
        success: true,
        data: result.data,
        count: result.data?.length || 0
      })
    } else {
      return res.status(500).json({
        success: false,
        message: 'فشل في جلب البيانات',
        error: result.error
      })
    }

  } catch (error) {
    console.error('خطأ في API البيانات:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
