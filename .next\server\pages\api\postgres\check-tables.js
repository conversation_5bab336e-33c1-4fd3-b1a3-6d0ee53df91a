"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/postgres/check-tables";
exports.ids = ["pages/api/postgres/check-tables"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fcheck-tables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ccheck-tables.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fcheck-tables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ccheck-tables.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_postgres_check_tables_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\postgres\\check-tables.ts */ \"(api)/./src/pages/api/postgres/check-tables.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_check_tables_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_check_tables_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/postgres/check-tables\",\n        pathname: \"/api/postgres/check-tables\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_postgres_check_tables_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTX0FQSSZwYWdlPSUyRmFwaSUyRnBvc3RncmVzJTJGY2hlY2stdGFibGVzJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTVDcGFnZXMlNUNhcGklNUNwb3N0Z3JlcyU1Q2NoZWNrLXRhYmxlcy50cyZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDTDtBQUMxRDtBQUN5RTtBQUN6RTtBQUNBLGlFQUFlLHdFQUFLLENBQUMsb0VBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sZUFBZSx3RUFBSyxDQUFDLG9FQUFRO0FBQ3BDO0FBQ08sd0JBQXdCLGdIQUFtQjtBQUNsRDtBQUNBLGNBQWMseUVBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLz9jNTRmIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzQVBJUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9wYWdlcy1hcGkvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9zcmNcXFxccGFnZXNcXFxcYXBpXFxcXHBvc3RncmVzXFxcXGNoZWNrLXRhYmxlcy50c1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBoYW5kbGVyIChzaG91bGQgYmUgdGhlIGRlZmF1bHQgZXhwb3J0KS5cbmV4cG9ydCBkZWZhdWx0IGhvaXN0KHVzZXJsYW5kLCBcImRlZmF1bHRcIik7XG4vLyBSZS1leHBvcnQgY29uZmlnLlxuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCBcImNvbmZpZ1wiKTtcbi8vIENyZWF0ZSBhbmQgZXhwb3J0IHRoZSByb3V0ZSBtb2R1bGUgdGhhdCB3aWxsIGJlIGNvbnN1bWVkLlxuZXhwb3J0IGNvbnN0IHJvdXRlTW9kdWxlID0gbmV3IFBhZ2VzQVBJUm91dGVNb2R1bGUoe1xuICAgIGRlZmluaXRpb246IHtcbiAgICAgICAga2luZDogUm91dGVLaW5kLlBBR0VTX0FQSSxcbiAgICAgICAgcGFnZTogXCIvYXBpL3Bvc3RncmVzL2NoZWNrLXRhYmxlc1wiLFxuICAgICAgICBwYXRobmFtZTogXCIvYXBpL3Bvc3RncmVzL2NoZWNrLXRhYmxlc1wiLFxuICAgICAgICAvLyBUaGUgZm9sbG93aW5nIGFyZW4ndCB1c2VkIGluIHByb2R1Y3Rpb24uXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcIlwiXG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLWFwaS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fcheck-tables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ccheck-tables.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/postgres.ts":
/*!*****************************!*\
  !*** ./src/lib/postgres.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTables: () => (/* binding */ checkTables),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   findUser: () => (/* binding */ findUser),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\n// إعداد الاتصال بقاعدة البيانات PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"5432\"),\n    database: process.env.DB_NAME || \"Vero_ERP_ABA\",\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    ssl:  false ? 0 : false,\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const client = await pool.connect();\n        const result = await client.query(\"SELECT NOW()\");\n        client.release();\n        return {\n            success: true,\n            time: result.rows[0].now\n        };\n    } catch (error) {\n        console.error(\"خطأ في الاتصال بقاعدة البيانات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    try {\n        const result = await pool.query(text, params);\n        return {\n            success: true,\n            data: result.rows,\n            rowCount: result.rowCount\n        };\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للتحقق من وجود الجداول\nconst checkTables = async ()=>{\n    try {\n        const result = await query(`\n      SELECT table_name \n      FROM information_schema.tables \n      WHERE table_schema = 'public' \n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = result.data?.map((row)=>row.table_name) || [];\n        return {\n            success: true,\n            tables,\n            hasBranches: tables.includes(\"branches\"),\n            hasUsers: tables.includes(\"users\"),\n            hasProducts: tables.includes(\"products\"),\n            hasWarehouses: tables.includes(\"warehouses\"),\n            needsSetup: !tables.includes(\"branches\")\n        };\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return {\n            success: false,\n            error,\n            needsSetup: true\n        };\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const result = await query(`\n      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)\n      VALUES ($1, $2, $3, $4, $5, true, NOW())\n      RETURNING id, email, username, full_name, role\n    `, [\n            userData.email,\n            userData.username,\n            userData.full_name,\n            userData.password_hash,\n            userData.role || \"admin\"\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0]\n        };\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للبحث عن مستخدم\nconst findUser = async (email)=>{\n    try {\n        const result = await query(`\n      SELECT id, email, username, full_name, role, password_hash, is_active\n      FROM users \n      WHERE email = $1 AND is_active = true\n    `, [\n            email\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0] || null\n        };\n    } catch (error) {\n        console.error(\"خطأ في البحث عن المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/postgres.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/postgres/check-tables.ts":
/*!************************************************!*\
  !*** ./src/pages/api/postgres/check-tables.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/postgres */ \"(api)/./src/lib/postgres.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // فحص الجداول الموجودة\n        const tablesResult = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT\n        table_name,\n        table_type\n      FROM information_schema.tables\n      WHERE table_schema = 'public'\n      ORDER BY table_name\n    `);\n        if (!tablesResult.success) {\n            return res.status(500).json({\n                success: false,\n                message: \"فشل في فحص الجداول\",\n                error: tablesResult.error\n            });\n        }\n        const allTables = tablesResult.data || [];\n        const tableNames = allTables.map((row)=>row.table_name);\n        // الجداول المطلوبة للنظام\n        const requiredTables = [\n            // الجداول الأساسية\n            \"branches\",\n            \"warehouses\",\n            \"cash_registers\",\n            \"users\",\n            \"customers\",\n            \"suppliers\",\n            \"products\",\n            \"inventory\",\n            // جداول المبيعات والمشتريات\n            \"sales_orders\",\n            \"sales_order_items\",\n            \"purchase_orders\",\n            \"purchase_order_items\",\n            // جداول المخزون والمالية\n            \"inventory_movements\",\n            \"cash_transactions\",\n            \"expenses\",\n            \"installments\",\n            \"installment_payments\",\n            // جداول الصيانة\n            \"maintenance_requests\",\n            \"maintenance_required_parts\",\n            \"maintenance_used_parts\"\n        ];\n        // فحص الجداول المطلوبة\n        const tableStatus = {};\n        for (const table of requiredTables){\n            const exists = tableNames.includes(table);\n            tableStatus[table] = {\n                exists,\n                status: exists ? \"✅ موجود\" : \"❌ غير موجود\"\n            };\n            // إذا كان الجدول موجود، فحص عدد السجلات\n            if (exists) {\n                try {\n                    const countResult = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`SELECT COUNT(*) as count FROM ${table}`);\n                    if (countResult.success) {\n                        tableStatus[table].count = parseInt(countResult.data[0].count);\n                        tableStatus[table].status += ` (${tableStatus[table].count} سجل)`;\n                    }\n                } catch (error) {\n                    tableStatus[table].status += \" (خطأ في العد)\";\n                }\n            }\n        }\n        // فحص الأنواع المخصصة (ENUMs)\n        const enumsResult = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT\n        t.typname as enum_name,\n        array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values\n      FROM pg_type t\n      JOIN pg_enum e ON t.oid = e.enumtypid\n      WHERE t.typname IN ('user_role', 'transaction_type', 'payment_method')\n      GROUP BY t.typname\n      ORDER BY t.typname\n    `);\n        const enums = enumsResult.success ? enumsResult.data : [];\n        // حساب الإحصائيات\n        const existingTables = Object.values(tableStatus).filter((t)=>t.exists).length;\n        const missingTables = requiredTables.length - existingTables;\n        const setupComplete = missingTables === 0;\n        return res.status(200).json({\n            success: true,\n            database: {\n                name: process.env.DB_NAME,\n                host: process.env.DB_HOST,\n                connected: true\n            },\n            summary: {\n                totalRequired: requiredTables.length,\n                existing: existingTables,\n                missing: missingTables,\n                setupComplete,\n                status: setupComplete ? \"✅ الإعداد مكتمل\" : `⚠️ يحتاج ${missingTables} جداول`\n            },\n            tables: tableStatus,\n            enums: enums.map((e)=>({\n                    name: e.enum_name,\n                    values: e.enum_values,\n                    status: \"✅ موجود\"\n                })),\n            allTables: tableNames,\n            recommendations: setupComplete ? [\n                \"✅ جميع الجداول موجودة\",\n                \"\\uD83C\\uDFAF يمكنك الآن إنشاء مستخدم جديد\",\n                \"\\uD83D\\uDE80 النظام جاهز للاستخدام\"\n            ] : [\n                \"⚠️ بعض الجداول مفقودة\",\n                \"\\uD83D\\uDCDD نفذ ملف database_setup_postgres.sql\",\n                \"\\uD83D\\uDD04 أعد فحص الجداول بعد التنفيذ\"\n            ]\n        });\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/postgres/check-tables.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fcheck-tables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ccheck-tables.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();