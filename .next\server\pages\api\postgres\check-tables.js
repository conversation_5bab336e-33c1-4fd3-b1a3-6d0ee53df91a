"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/postgres/check-tables";
exports.ids = ["pages/api/postgres/check-tables"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fcheck-tables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ccheck-tables.ts&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fcheck-tables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ccheck-tables.ts&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_postgres_check_tables_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\postgres\\check-tables.ts */ \"(api)/./src/pages/api/postgres/check-tables.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_check_tables_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_check_tables_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/postgres/check-tables\",\n        pathname: \"/api/postgres/check-tables\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_postgres_check_tables_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fcheck-tables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ccheck-tables.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/postgres.ts":
/*!*****************************!*\
  !*** ./src/lib/postgres.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTables: () => (/* binding */ checkTables),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   findUser: () => (/* binding */ findUser),\n/* harmony export */   getCustomers: () => (/* binding */ getCustomers),\n/* harmony export */   getInventory: () => (/* binding */ getInventory),\n/* harmony export */   getMaintenanceRequests: () => (/* binding */ getMaintenanceRequests),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getPurchaseOrders: () => (/* binding */ getPurchaseOrders),\n/* harmony export */   getSalesOrders: () => (/* binding */ getSalesOrders),\n/* harmony export */   getStockMovements: () => (/* binding */ getStockMovements),\n/* harmony export */   getSuppliers: () => (/* binding */ getSuppliers),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\n// إعداد الاتصال بقاعدة البيانات PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"5432\"),\n    database: process.env.DB_NAME || \"Vero_ERP_ABA\",\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    ssl:  false ? 0 : false,\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const client = await pool.connect();\n        const result = await client.query(\"SELECT NOW()\");\n        client.release();\n        return {\n            success: true,\n            time: result.rows[0].now\n        };\n    } catch (error) {\n        console.error(\"خطأ في الاتصال بقاعدة البيانات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    try {\n        const result = await pool.query(text, params);\n        return {\n            success: true,\n            data: result.rows,\n            rowCount: result.rowCount\n        };\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للتحقق من وجود الجداول\nconst checkTables = async ()=>{\n    try {\n        const result = await query(`\n      SELECT table_name\n      FROM information_schema.tables\n      WHERE table_schema = 'public'\n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = result.data?.map((row)=>row.table_name) || [];\n        return {\n            success: true,\n            tables,\n            hasBranches: tables.includes(\"branches\"),\n            hasUsers: tables.includes(\"users\"),\n            hasProducts: tables.includes(\"products\"),\n            hasWarehouses: tables.includes(\"warehouses\"),\n            needsSetup: !tables.includes(\"branches\")\n        };\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return {\n            success: false,\n            error,\n            needsSetup: true\n        };\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const result = await query(`\n      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)\n      VALUES ($1, $2, $3, $4, $5, true, NOW())\n      RETURNING id, email, username, full_name, role\n    `, [\n            userData.email,\n            userData.username,\n            userData.full_name,\n            userData.password_hash,\n            userData.role || \"admin\"\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0]\n        };\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للبحث عن مستخدم\nconst findUser = async (email)=>{\n    try {\n        const result = await query(`\n      SELECT id, email, username, full_name, role, password_hash, is_active\n      FROM users\n      WHERE email = $1 AND is_active = true\n    `, [\n            email\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0] || null\n        };\n    } catch (error) {\n        console.error(\"خطأ في البحث عن المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المنتجات متوافقة مع الكود\nconst getProducts = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        p.*,\n        i.total_stock,\n        i.available_stock,\n        i.reserved_stock\n      FROM products p\n      LEFT JOIN inventory i ON p.id = i.product_id\n      WHERE p.is_active = true\n      ORDER BY p.name\n    `);\n        return {\n            success: true,\n            data: result.data?.map((product)=>({\n                    ...product,\n                    current_stock: product.total_stock || 0,\n                    price: product.unit_price,\n                    cost: product.cost_price\n                })) || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المنتجات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المخزون متوافقة مع الكود\nconst getInventory = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        i.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        p.category as product_category,\n        w.name as warehouse_name\n      FROM inventory i\n      JOIN products p ON i.product_id = p.id\n      JOIN warehouses w ON i.warehouse_id = w.id\n      ORDER BY i.updated_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات العملاء\nconst getCustomers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM customers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب العملاء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات الموردين\nconst getSuppliers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM suppliers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب الموردين:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات أوامر المبيعات\nconst getSalesOrders = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        so.*,\n        c.name as customer_name,\n        b.name as branch_name,\n        u.full_name as created_by_name\n      FROM sales_orders so\n      LEFT JOIN customers c ON so.customer_id = c.id\n      LEFT JOIN branches b ON so.branch_id = b.id\n      LEFT JOIN users u ON so.created_by = u.id\n      ORDER BY so.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب أوامر المبيعات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات أوامر الشراء\nconst getPurchaseOrders = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        po.*,\n        s.name as supplier_name,\n        b.name as branch_name,\n        w.name as warehouse_name,\n        u.full_name as created_by_name\n      FROM purchase_orders po\n      LEFT JOIN suppliers s ON po.supplier_id = s.id\n      LEFT JOIN branches b ON po.branch_id = b.id\n      LEFT JOIN warehouses w ON po.warehouse_id = w.id\n      LEFT JOIN users u ON po.created_by = u.id\n      ORDER BY po.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب أوامر الشراء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات حركات المخزون\nconst getStockMovements = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        sm.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        w.name as warehouse_name,\n        u.full_name as created_by_name\n      FROM stock_movements sm\n      JOIN products p ON sm.product_id = p.id\n      JOIN warehouses w ON sm.warehouse_id = w.id\n      LEFT JOIN users u ON sm.created_by = u.id\n      ORDER BY sm.created_at DESC\n      LIMIT 100\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب حركات المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات طلبات الصيانة\nconst getMaintenanceRequests = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        mr.*,\n        t.full_name as technician_name,\n        at.full_name as assigned_technician_name,\n        b.name as branch_name,\n        w.name as warehouse_name\n      FROM maintenance_requests mr\n      LEFT JOIN users t ON mr.technician_id = t.id\n      LEFT JOIN users at ON mr.assigned_technician = at.id\n      LEFT JOIN branches b ON mr.branch_id = b.id\n      LEFT JOIN warehouses w ON mr.warehouse_id = w.id\n      ORDER BY mr.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب طلبات الصيانة:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/postgres.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/postgres/check-tables.ts":
/*!************************************************!*\
  !*** ./src/pages/api/postgres/check-tables.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/postgres */ \"(api)/./src/lib/postgres.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // فحص الجداول الموجودة\n        const tablesResult = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT\n        table_name,\n        table_type\n      FROM information_schema.tables\n      WHERE table_schema = 'public'\n      ORDER BY table_name\n    `);\n        if (!tablesResult.success) {\n            return res.status(500).json({\n                success: false,\n                message: \"فشل في فحص الجداول\",\n                error: tablesResult.error\n            });\n        }\n        const allTables = tablesResult.data || [];\n        const tableNames = allTables.map((row)=>row.table_name);\n        // الجداول المطلوبة للنظام المتكامل (20 جدول)\n        const requiredTables = [\n            // الجداول الأساسية (8 جداول)\n            \"branches\",\n            \"warehouses\",\n            \"cash_registers\",\n            \"users\",\n            \"customers\",\n            \"suppliers\",\n            \"products\",\n            \"inventory\",\n            // جداول المبيعات والمشتريات (4 جداول)\n            \"sales_orders\",\n            \"sales_order_items\",\n            \"purchase_orders\",\n            \"purchase_order_items\",\n            // جداول المخزون والمالية (5 جداول)\n            \"stock_movements\",\n            \"cash_transactions\",\n            \"expenses\",\n            \"installments\",\n            \"installment_payments\",\n            // جداول الصيانة (3 جداول)\n            \"maintenance_requests\",\n            \"maintenance_required_parts\",\n            \"maintenance_used_parts\"\n        ];\n        // فحص الجداول المطلوبة\n        const tableStatus = {};\n        for (const table of requiredTables){\n            const exists = tableNames.includes(table);\n            tableStatus[table] = {\n                exists,\n                status: exists ? \"✅ موجود\" : \"❌ غير موجود\"\n            };\n            // إذا كان الجدول موجود، فحص عدد السجلات\n            if (exists) {\n                try {\n                    const countResult = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`SELECT COUNT(*) as count FROM ${table}`);\n                    if (countResult.success) {\n                        tableStatus[table].count = parseInt(countResult.data[0].count);\n                        tableStatus[table].status += ` (${tableStatus[table].count} سجل)`;\n                    }\n                } catch (error) {\n                    tableStatus[table].status += \" (خطأ في العد)\";\n                }\n            }\n        }\n        // فحص الأنواع المخصصة (ENUMs)\n        const enumsResult = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT\n        t.typname as enum_name,\n        array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values\n      FROM pg_type t\n      JOIN pg_enum e ON t.oid = e.enumtypid\n      WHERE t.typname IN ('user_role', 'transaction_type', 'payment_method')\n      GROUP BY t.typname\n      ORDER BY t.typname\n    `);\n        const enums = enumsResult.success ? enumsResult.data : [];\n        // حساب الإحصائيات\n        const existingTables = Object.values(tableStatus).filter((t)=>t.exists).length;\n        const missingTables = requiredTables.length - existingTables;\n        const setupComplete = missingTables === 0;\n        return res.status(200).json({\n            success: true,\n            database: {\n                name: process.env.DB_NAME,\n                host: process.env.DB_HOST,\n                connected: true\n            },\n            summary: {\n                totalRequired: requiredTables.length,\n                existing: existingTables,\n                missing: missingTables,\n                setupComplete,\n                status: setupComplete ? \"✅ الإعداد مكتمل\" : `⚠️ يحتاج ${missingTables} جداول`\n            },\n            tables: tableStatus,\n            enums: enums.map((e)=>({\n                    name: e.enum_name,\n                    values: e.enum_values,\n                    status: \"✅ موجود\"\n                })),\n            allTables: tableNames,\n            recommendations: setupComplete ? [\n                \"✅ جميع الجداول موجودة\",\n                \"\\uD83C\\uDFAF يمكنك الآن إنشاء مستخدم جديد\",\n                \"\\uD83D\\uDE80 النظام جاهز للاستخدام\"\n            ] : [\n                \"⚠️ بعض الجداول مفقودة\",\n                \"\\uD83D\\uDCDD نفذ ملف database_setup_postgres.sql\",\n                \"\\uD83D\\uDD04 أعد فحص الجداول بعد التنفيذ\"\n            ]\n        });\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/postgres/check-tables.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fcheck-tables&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Ccheck-tables.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();