{"c": ["webpack"], "r": ["pages/login", "/_error", "pages/index", "pages/users/new", "node_modules_ws_browser_js"], "m": ["./node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js", "./node_modules/@supabase/auth-js/dist/module/AuthClient.js", "./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js", "./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js", "./node_modules/@supabase/auth-js/dist/module/index.js", "./node_modules/@supabase/auth-js/dist/module/lib/base64url.js", "./node_modules/@supabase/auth-js/dist/module/lib/constants.js", "./node_modules/@supabase/auth-js/dist/module/lib/errors.js", "./node_modules/@supabase/auth-js/dist/module/lib/fetch.js", "./node_modules/@supabase/auth-js/dist/module/lib/helpers.js", "./node_modules/@supabase/auth-js/dist/module/lib/local-storage.js", "./node_modules/@supabase/auth-js/dist/module/lib/locks.js", "./node_modules/@supabase/auth-js/dist/module/lib/polyfills.js", "./node_modules/@supabase/auth-js/dist/module/lib/types.js", "./node_modules/@supabase/auth-js/dist/module/lib/version.js", "./node_modules/@supabase/functions-js/dist/module/FunctionsClient.js", "./node_modules/@supabase/functions-js/dist/module/helper.js", "./node_modules/@supabase/functions-js/dist/module/index.js", "./node_modules/@supabase/functions-js/dist/module/types.js", "./node_modules/@supabase/node-fetch/browser.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js", "./node_modules/@supabase/postgrest-js/dist/cjs/constants.js", "./node_modules/@supabase/postgrest-js/dist/cjs/index.js", "./node_modules/@supabase/postgrest-js/dist/cjs/version.js", "./node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "./node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js", "./node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js", "./node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js", "./node_modules/@supabase/realtime-js/dist/module/index.js", "./node_modules/@supabase/realtime-js/dist/module/lib/constants.js", "./node_modules/@supabase/realtime-js/dist/module/lib/push.js", "./node_modules/@supabase/realtime-js/dist/module/lib/serializer.js", "./node_modules/@supabase/realtime-js/dist/module/lib/timer.js", "./node_modules/@supabase/realtime-js/dist/module/lib/transformers.js", "./node_modules/@supabase/realtime-js/dist/module/lib/version.js", "./node_modules/@supabase/storage-js/dist/module/StorageClient.js", "./node_modules/@supabase/storage-js/dist/module/index.js", "./node_modules/@supabase/storage-js/dist/module/lib/constants.js", "./node_modules/@supabase/storage-js/dist/module/lib/errors.js", "./node_modules/@supabase/storage-js/dist/module/lib/fetch.js", "./node_modules/@supabase/storage-js/dist/module/lib/helpers.js", "./node_modules/@supabase/storage-js/dist/module/lib/types.js", "./node_modules/@supabase/storage-js/dist/module/lib/version.js", "./node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js", "./node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js", "./node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js", "./node_modules/@supabase/supabase-js/dist/module/index.js", "./node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js", "./node_modules/@supabase/supabase-js/dist/module/lib/constants.js", "./node_modules/@supabase/supabase-js/dist/module/lib/fetch.js", "./node_modules/@supabase/supabase-js/dist/module/lib/helpers.js", "./node_modules/@supabase/supabase-js/dist/module/lib/version.js", "./node_modules/lucide-react/dist/esm/icons/eye-off.js", "./node_modules/lucide-react/dist/esm/icons/log-in.js", "./node_modules/next/dist/build/polyfills/process.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Clogin.tsx&page=%2Flogin!", "./node_modules/next/dist/compiled/buffer/index.js", "./node_modules/next/dist/compiled/process/browser.js", "./src/components/auth/LoginForm.tsx", "./src/lib/database-setup.ts", "./src/lib/supabase.ts", "./src/pages/login.tsx", "__barrel_optimize__?names=<PERSON>ertCircle,Eye,EyeOff,LogIn,Settings!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Cnode_modules%5Cnext%5Cdist%5Cpages%5C_error.js&page=%2F_error!", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "./src/pages/index.tsx", "./node_modules/@radix-ui/react-checkbox/dist/index.mjs", "./node_modules/@radix-ui/react-label/dist/index.mjs", "./node_modules/lucide-react/dist/esm/icons/arrow-left.js", "./node_modules/lucide-react/dist/esm/icons/key.js", "./node_modules/lucide-react/dist/esm/icons/save.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Cusers%5Cnew.tsx&page=%2Fusers%2Fnew!", "./src/components/ui/checkbox.tsx", "./src/components/ui/label.tsx", "./src/components/ui/textarea.tsx", "./src/hooks/useActivityLogger.ts", "./src/pages/users/new.tsx", "__barrel_optimize__?names=ArrowLeft,Eye,EyeOff,Key,Mail,Phone,Save,Shield,User!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "__barrel_optimize__?names=Check!=!./node_modules/lucide-react/dist/esm/lucide-react.js", "./node_modules/ws/browser.js"]}