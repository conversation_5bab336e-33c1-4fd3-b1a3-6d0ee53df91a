self.__BUILD_MANIFEST = {__rewrites:{afterFiles:[],beforeFiles:[],fallback:[]},"/_error":["static\u002Fchunks\u002Fpages\u002F_error.js"],"/accounting/accounts":["static\u002Fchunks\u002Fpages\u002Faccounting\u002Faccounts.js"],"/accounting/cash-flow":["static\u002Fchunks\u002Fpages\u002Faccounting\u002Fcash-flow.js"],"/accounting/customer-statements":["static\u002Fchunks\u002Fpages\u002Faccounting\u002Fcustomer-statements.js"],"/accounting/expenses":["static\u002Fchunks\u002Fpages\u002Faccounting\u002Fexpenses.js"],"/accounting/installments":["static\u002Fchunks\u002Fpages\u002Faccounting\u002Finstallments.js"],"/accounting/reports":["static\u002Fchunks\u002Fpages\u002Faccounting\u002Freports.js"],"/add-sample-data":["static\u002Fchunks\u002Fpages\u002Fadd-sample-data.js"],"/dashboard":["static\u002Fchunks\u002Fpages\u002Fdashboard.js"],"/maintenance":["static\u002Fchunks\u002Fpages\u002Fmaintenance.js"],"/sales":["static\u002Fchunks\u002Fpages\u002Fsales.js"],"/setup":["static\u002Fchunks\u002Fpages\u002Fsetup.js"],"/users":["static\u002Fchunks\u002Fpages\u002Fusers.js"],sortedPages:["\u002F_app","\u002F_error","\u002Faccounting\u002Faccounts","\u002Faccounting\u002Fcash-flow","\u002Faccounting\u002Fcustomer-statements","\u002Faccounting\u002Fexpenses","\u002Faccounting\u002Finstallments","\u002Faccounting\u002Freports","\u002Fadd-sample-data","\u002Fdashboard","\u002Fmaintenance","\u002Fsales","\u002Fsetup","\u002Fusers"]};self.__BUILD_MANIFEST_CB && self.__BUILD_MANIFEST_CB()