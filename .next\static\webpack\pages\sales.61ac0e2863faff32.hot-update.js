"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./src/pages/sales/index.tsx":
/*!***********************************!*\
  !*** ./src/pages/sales/index.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"./src/lib/auth.ts\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"./src/components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,FileText,RotateCcw,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"__barrel_optimize__?names=ArrowRight,Clock,DollarSign,FileText,RotateCcw,ShoppingCart,TrendingDown,TrendingUp,Users!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SalesOverview() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [salesData, setSalesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        orders: [],\n        invoices: [],\n        customers: [],\n        stats: {\n            totalQuotations: 0,\n            pendingQuotations: 0,\n            approvedQuotations: 0,\n            expiredQuotations: 0,\n            totalOrders: 0,\n            pendingOrders: 0,\n            confirmedOrders: 0,\n            shippedOrders: 0,\n            totalInvoices: 0,\n            pendingInvoices: 0,\n            paidInvoices: 0,\n            overdueInvoices: 0,\n            totalReturns: 0,\n            pendingReturns: 0,\n            processedReturns: 0,\n            totalValue: 0,\n            paidValue: 0,\n            pendingValue: 0,\n            returnValue: 0\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSalesData();\n    }, []);\n    const fetchSalesData = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/postgres/data?type=sales-orders\");\n            const result = await response.json();\n            if (result.success) {\n                // حساب الإحصائيات من البيانات الحقيقية\n                const orders = result.data || [];\n                const stats = {\n                    totalQuotations: 0,\n                    pendingQuotations: 0,\n                    approvedQuotations: 0,\n                    expiredQuotations: 0,\n                    totalOrders: orders.length,\n                    pendingOrders: orders.filter((o)=>o.order_status === \"pending\").length,\n                    confirmedOrders: orders.filter((o)=>o.order_status === \"confirmed\").length,\n                    shippedOrders: orders.filter((o)=>o.order_status === \"shipped\").length,\n                    totalInvoices: 0,\n                    pendingInvoices: 0,\n                    paidInvoices: 0,\n                    overdueInvoices: 0,\n                    totalReturns: 0,\n                    pendingReturns: 0,\n                    processedReturns: 0,\n                    totalValue: orders.reduce((sum, order)=>sum + (order.final_amount || 0), 0),\n                    paidValue: orders.filter((o)=>o.payment_status === \"paid\").reduce((sum, order)=>sum + (order.final_amount || 0), 0),\n                    pendingValue: orders.filter((o)=>o.payment_status === \"pending\").reduce((sum, order)=>sum + (order.final_amount || 0), 0),\n                    returnValue: 0\n                };\n                setSalesData({\n                    orders,\n                    invoices: [],\n                    customers: [],\n                    stats\n                });\n            }\n        } catch (error) {\n            console.error(\"خطأ في جلب بيانات المبيعات:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Recent activities\n    const recentQuotations = [\n        {\n            id: \"1\",\n            quotation_number: \"QT-2024-001\",\n            customer_name: \"شركة التقنية المتقدمة\",\n            status: \"pending\",\n            total_amount: 83000,\n            created_at: \"2024-01-25\"\n        },\n        {\n            id: \"2\",\n            quotation_number: \"QT-2024-002\",\n            customer_name: \"مؤسسة الإلكترونيات الحديثة\",\n            status: \"approved\",\n            total_amount: 50300,\n            created_at: \"2024-01-24\"\n        },\n        {\n            id: \"3\",\n            quotation_number: \"QT-2024-003\",\n            customer_name: \"شركة الأجهزة الذكية\",\n            status: \"expired\",\n            total_amount: 35980,\n            created_at: \"2024-01-23\"\n        }\n    ];\n    const recentInvoices = [\n        {\n            id: \"1\",\n            invoice_number: \"INV-2024-001\",\n            customer_name: \"شركة التقنية المتقدمة\",\n            status: \"pending\",\n            total_amount: 83000,\n            due_date: \"2024-02-20\"\n        },\n        {\n            id: \"2\",\n            invoice_number: \"INV-2024-002\",\n            customer_name: \"مؤسسة الإلكترونيات الحديثة\",\n            status: \"paid\",\n            total_amount: 50300,\n            due_date: \"2024-02-05\"\n        },\n        {\n            id: \"3\",\n            invoice_number: \"INV-2024-003\",\n            customer_name: \"شركة الأجهزة الذكية\",\n            status: \"overdue\",\n            total_amount: 35980,\n            due_date: \"2024-01-25\"\n        }\n    ];\n    const topCustomers = [\n        {\n            name: \"شركة التقنية المتقدمة\",\n            orders: 45,\n            value: 890000\n        },\n        {\n            name: \"مؤسسة الإلكترونيات الحديثة\",\n            orders: 38,\n            value: 720000\n        },\n        {\n            name: \"شركة الأجهزة الذكية\",\n            orders: 32,\n            value: 580000\n        },\n        {\n            name: \"متجر الكمبيوتر المتقدم\",\n            orders: 28,\n            value: 450000\n        },\n        {\n            name: \"شركة الحلول التقنية\",\n            orders: 25,\n            value: 380000\n        }\n    ];\n    // Helper functions\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"في الانتظار\";\n            case \"approved\":\n                return \"معتمد\";\n            case \"confirmed\":\n                return \"مؤكد\";\n            case \"shipped\":\n                return \"تم الشحن\";\n            case \"paid\":\n                return \"مدفوعة\";\n            case \"overdue\":\n                return \"متأخرة\";\n            case \"expired\":\n                return \"منتهية الصلاحية\";\n            default:\n                return \"غير محدد\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"approved\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"confirmed\":\n                return \"bg-green-100 text-green-800\";\n            case \"shipped\":\n                return \"bg-green-100 text-green-800\";\n            case \"paid\":\n                return \"bg-green-100 text-green-800\";\n            case \"overdue\":\n                return \"bg-red-100 text-red-800\";\n            case \"expired\":\n                return \"bg-gray-100 text-gray-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(user, _lib_auth__WEBPACK_IMPORTED_MODULE_3__.PERMISSIONS.SALES_VIEW)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"غير مصرح\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                        lineNumber: 207,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"ليس لديك صلاحية لعرض وحدة المبيعات\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                        lineNumber: 208,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                lineNumber: 206,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n            lineNumber: 205,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold tracking-tight\",\n                                    children: \"المبيعات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 220,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"لوحة تحكم شاملة لإدارة المبيعات والعملاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 219,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 space-x-reverse\",\n                            children: (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(user, _lib_auth__WEBPACK_IMPORTED_MODULE_3__.PERMISSIONS.SALES_CREATE) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                        href: \"/sales/quotations\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"عرض سعر جديد\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 229,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                        href: \"/sales/invoices\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"فاتورة مبيعات جديدة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 225,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 218,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/sales/quotations\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"عروض الأسعار\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 249,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: salesData.stats.totalQuotations\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    salesData.stats.pendingQuotations,\n                                                    \" في الانتظار\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 248,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/sales/orders\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"طلبات المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.ShoppingCart, {\n                                                className: \"h-4 w-4 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: salesData.stats.totalOrders\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 269,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    salesData.stats.pendingOrders,\n                                                    \" في الانتظار\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 270,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 268,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 263,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 262,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/sales/invoices\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"فواتير المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 280,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-4 w-4 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: salesData.stats.totalInvoices\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 284,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    salesData.stats.pendingInvoices,\n                                                    \" تحتاج دفع\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 277,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/sales/returns\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"مرتجعات المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.RotateCcw, {\n                                                className: \"h-4 w-4 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 296,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: salesData.stats.totalReturns\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    salesData.stats.pendingReturns,\n                                                    \" في الانتظار\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 300,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"إجمالي قيمة المبيعات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(salesData.stats.totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"إجمالي المبيعات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 315,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"المبلغ المحصل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 323,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 322,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(salesData.stats.paidValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 327,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                salesData.stats.totalValue > 0 ? Math.round(salesData.stats.paidValue / salesData.stats.totalValue * 100) : 0,\n                                                \"% من الإجمالي\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 328,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 321,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"المبلغ المستحق\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 336,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Clock, {\n                                            className: \"h-4 w-4 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-yellow-600\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(salesData.stats.pendingValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"يجب تحصيله\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"قيمة المرتجعات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 347,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingDown, {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 348,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 346,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(salesData.stats.returnValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                salesData.stats.totalValue > 0 ? Math.round(salesData.stats.returnValue / salesData.stats.totalValue * 100) : 0,\n                                                \"% من الإجمالي\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 352,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 345,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"أحدث عروض الأسعار\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                            href: \"/sales/quotations\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.ArrowRight, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 366,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentQuotations.map((quotation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: quotation.quotation_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 377,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: quotation.customer_name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 378,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatDate)(quotation.created_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(quotation.total_amount)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 382,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(quotation.status)),\n                                                                children: getStatusText(quotation.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 383,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, quotation.id, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 375,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"أحدث فواتير المبيعات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                            href: \"/sales/invoices\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.ArrowRight, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 400,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 398,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 395,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: invoice.invoice_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 409,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: invoice.customer_name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 410,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    \"استحقاق: \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatDate)(invoice.due_date)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(invoice.total_amount)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 414,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(invoice.status)),\n                                                                children: getStatusText(invoice.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 415,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, invoice.id, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 407,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 394,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 360,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Users, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أهم العملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-md border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"اسم العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"عدد الطلبات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 440,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"إجمالي القيمة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"متوسط قيمة الطلب\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 438,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                            children: topCustomers.map((customer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium text-sm\",\n                                                                            children: index + 1\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                            lineNumber: 451,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                        lineNumber: 450,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: customer.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                        lineNumber: 453,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 449,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: customer.orders\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"font-medium\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(customer.value)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                            lineNumber: 457,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(customer.value / customer.orders)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                    lineNumber: 447,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 445,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 434,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 427,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n            lineNumber: 216,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n        lineNumber: 215,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesOverview, \"24jh74yxpinBrtga+B56cbxIiZ8=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SalesOverview;\nvar _c;\n$RefreshReg$(_c, \"SalesOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvc2FsZXMvaW5kZXgudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQTJDO0FBQ0Y7QUFDYztBQUNSO0FBQ0E7QUFDZ0M7QUFRakQ7QUFnQlQ7QUFDbUM7QUFDNUI7QUFFYixTQUFTNkI7O0lBQ3RCLE1BQU0sRUFBRUMsSUFBSSxFQUFFLEdBQUc1Qix1REFBT0E7SUFDeEIsTUFBTSxDQUFDNkIsU0FBU0MsV0FBVyxHQUFHaEMsK0NBQVFBLENBQUM7SUFDdkMsTUFBTSxDQUFDaUMsV0FBV0MsYUFBYSxHQUFHbEMsK0NBQVFBLENBQUM7UUFDekNtQyxRQUFRLEVBQUU7UUFDVkMsVUFBVSxFQUFFO1FBQ1pDLFdBQVcsRUFBRTtRQUNiQyxPQUFPO1lBQ0xDLGlCQUFpQjtZQUNqQkMsbUJBQW1CO1lBQ25CQyxvQkFBb0I7WUFDcEJDLG1CQUFtQjtZQUNuQkMsYUFBYTtZQUNiQyxlQUFlO1lBQ2ZDLGlCQUFpQjtZQUNqQkMsZUFBZTtZQUNmQyxlQUFlO1lBQ2ZDLGlCQUFpQjtZQUNqQkMsY0FBYztZQUNkQyxpQkFBaUI7WUFDakJDLGNBQWM7WUFDZEMsZ0JBQWdCO1lBQ2hCQyxrQkFBa0I7WUFDbEJDLFlBQVk7WUFDWkMsV0FBVztZQUNYQyxjQUFjO1lBQ2RDLGFBQWE7UUFDZjtJQUNGO0lBRUF4RCxnREFBU0EsQ0FBQztRQUNSeUQ7SUFDRixHQUFHLEVBQUU7SUFFTCxNQUFNQSxpQkFBaUI7UUFDckIxQixXQUFXO1FBQ1gsSUFBSTtZQUNGLE1BQU0yQixXQUFXLE1BQU1DLE1BQU07WUFDN0IsTUFBTUMsU0FBUyxNQUFNRixTQUFTRyxJQUFJO1lBRWxDLElBQUlELE9BQU9FLE9BQU8sRUFBRTtnQkFDbEIsdUNBQXVDO2dCQUN2QyxNQUFNNUIsU0FBUzBCLE9BQU9HLElBQUksSUFBSSxFQUFFO2dCQUNoQyxNQUFNMUIsUUFBUTtvQkFDWkMsaUJBQWlCO29CQUNqQkMsbUJBQW1CO29CQUNuQkMsb0JBQW9CO29CQUNwQkMsbUJBQW1CO29CQUNuQkMsYUFBYVIsT0FBTzhCLE1BQU07b0JBQzFCckIsZUFBZVQsT0FBTytCLE1BQU0sQ0FBQyxDQUFDQyxJQUFXQSxFQUFFQyxZQUFZLEtBQUssV0FBV0gsTUFBTTtvQkFDN0VwQixpQkFBaUJWLE9BQU8rQixNQUFNLENBQUMsQ0FBQ0MsSUFBV0EsRUFBRUMsWUFBWSxLQUFLLGFBQWFILE1BQU07b0JBQ2pGbkIsZUFBZVgsT0FBTytCLE1BQU0sQ0FBQyxDQUFDQyxJQUFXQSxFQUFFQyxZQUFZLEtBQUssV0FBV0gsTUFBTTtvQkFDN0VsQixlQUFlO29CQUNmQyxpQkFBaUI7b0JBQ2pCQyxjQUFjO29CQUNkQyxpQkFBaUI7b0JBQ2pCQyxjQUFjO29CQUNkQyxnQkFBZ0I7b0JBQ2hCQyxrQkFBa0I7b0JBQ2xCQyxZQUFZbkIsT0FBT2tDLE1BQU0sQ0FBQyxDQUFDQyxLQUFhQyxRQUFlRCxNQUFPQyxDQUFBQSxNQUFNQyxZQUFZLElBQUksSUFBSTtvQkFDeEZqQixXQUFXcEIsT0FBTytCLE1BQU0sQ0FBQyxDQUFDQyxJQUFXQSxFQUFFTSxjQUFjLEtBQUssUUFBUUosTUFBTSxDQUFDLENBQUNDLEtBQWFDLFFBQWVELE1BQU9DLENBQUFBLE1BQU1DLFlBQVksSUFBSSxJQUFJO29CQUN2SWhCLGNBQWNyQixPQUFPK0IsTUFBTSxDQUFDLENBQUNDLElBQVdBLEVBQUVNLGNBQWMsS0FBSyxXQUFXSixNQUFNLENBQUMsQ0FBQ0MsS0FBYUMsUUFBZUQsTUFBT0MsQ0FBQUEsTUFBTUMsWUFBWSxJQUFJLElBQUk7b0JBQzdJZixhQUFhO2dCQUNmO2dCQUVBdkIsYUFBYTtvQkFDWEM7b0JBQ0FDLFVBQVUsRUFBRTtvQkFDWkMsV0FBVyxFQUFFO29CQUNiQztnQkFDRjtZQUNGO1FBQ0YsRUFBRSxPQUFPb0MsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsK0JBQStCQTtRQUMvQyxTQUFVO1lBQ1IxQyxXQUFXO1FBQ2I7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNNEMsbUJBQW1CO1FBQ3ZCO1lBQ0VDLElBQUk7WUFDSkMsa0JBQWtCO1lBQ2xCQyxlQUFlO1lBQ2ZDLFFBQVE7WUFDUkMsY0FBYztZQUNkQyxZQUFZO1FBQ2Q7UUFDQTtZQUNFTCxJQUFJO1lBQ0pDLGtCQUFrQjtZQUNsQkMsZUFBZTtZQUNmQyxRQUFRO1lBQ1JDLGNBQWM7WUFDZEMsWUFBWTtRQUNkO1FBQ0E7WUFDRUwsSUFBSTtZQUNKQyxrQkFBa0I7WUFDbEJDLGVBQWU7WUFDZkMsUUFBUTtZQUNSQyxjQUFjO1lBQ2RDLFlBQVk7UUFDZDtLQUNEO0lBRUQsTUFBTUMsaUJBQWlCO1FBQ3JCO1lBQ0VOLElBQUk7WUFDSk8sZ0JBQWdCO1lBQ2hCTCxlQUFlO1lBQ2ZDLFFBQVE7WUFDUkMsY0FBYztZQUNkSSxVQUFVO1FBQ1o7UUFDQTtZQUNFUixJQUFJO1lBQ0pPLGdCQUFnQjtZQUNoQkwsZUFBZTtZQUNmQyxRQUFRO1lBQ1JDLGNBQWM7WUFDZEksVUFBVTtRQUNaO1FBQ0E7WUFDRVIsSUFBSTtZQUNKTyxnQkFBZ0I7WUFDaEJMLGVBQWU7WUFDZkMsUUFBUTtZQUNSQyxjQUFjO1lBQ2RJLFVBQVU7UUFDWjtLQUNEO0lBRUQsTUFBTUMsZUFBZTtRQUNuQjtZQUFFQyxNQUFNO1lBQXlCcEQsUUFBUTtZQUFJcUQsT0FBTztRQUFPO1FBQzNEO1lBQUVELE1BQU07WUFBOEJwRCxRQUFRO1lBQUlxRCxPQUFPO1FBQU87UUFDaEU7WUFBRUQsTUFBTTtZQUF1QnBELFFBQVE7WUFBSXFELE9BQU87UUFBTztRQUN6RDtZQUFFRCxNQUFNO1lBQTBCcEQsUUFBUTtZQUFJcUQsT0FBTztRQUFPO1FBQzVEO1lBQUVELE1BQU07WUFBdUJwRCxRQUFRO1lBQUlxRCxPQUFPO1FBQU87S0FDMUQ7SUFFRCxtQkFBbUI7SUFDbkIsTUFBTUMsZ0JBQWdCLENBQUNUO1FBQ3JCLE9BQVFBO1lBQ04sS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQVksT0FBTztZQUN4QixLQUFLO2dCQUFhLE9BQU87WUFDekIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQVEsT0FBTztZQUNwQixLQUFLO2dCQUFXLE9BQU87WUFDdkIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCO2dCQUFTLE9BQU87UUFDbEI7SUFDRjtJQUVBLE1BQU1VLGlCQUFpQixDQUFDVjtRQUN0QixPQUFRQTtZQUNOLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFZLE9BQU87WUFDeEIsS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QixLQUFLO2dCQUFRLE9BQU87WUFDcEIsS0FBSztnQkFBVyxPQUFPO1lBQ3ZCLEtBQUs7Z0JBQVcsT0FBTztZQUN2QjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxJQUFJLENBQUM3RSx3REFBYUEsQ0FBQzJCLE1BQU0xQixrREFBV0EsQ0FBQ3VGLFVBQVUsR0FBRztRQUNoRCxxQkFDRSw4REFBQ3RGLGlFQUFNQTtzQkFDTCw0RUFBQ3VGO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQUdELFdBQVU7a0NBQW1DOzs7Ozs7a0NBQ2pELDhEQUFDRTt3QkFBRUYsV0FBVTtrQ0FBZ0I7Ozs7Ozs7Ozs7Ozs7Ozs7O0lBSXJDO0lBRUEscUJBQ0UsOERBQUN4RixpRUFBTUE7a0JBQ0wsNEVBQUN1RjtZQUFJQyxXQUFVOzs4QkFFYiw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDRDs7OENBQ0MsOERBQUNJO29DQUFHSCxXQUFVOzhDQUFvQzs7Ozs7OzhDQUNsRCw4REFBQ0U7b0NBQUVGLFdBQVU7OENBQXdCOzs7Ozs7Ozs7Ozs7c0NBSXZDLDhEQUFDRDs0QkFBSUMsV0FBVTtzQ0FDWjFGLHdEQUFhQSxDQUFDMkIsTUFBTTFCLGtEQUFXQSxDQUFDNkYsWUFBWSxtQkFDM0M7O2tEQUNFLDhEQUFDckUsa0RBQUlBO3dDQUFDc0UsTUFBSztrREFDVCw0RUFBQzVGLHlEQUFNQTs0Q0FBQzZGLFNBQVE7OzhEQUNkLDhEQUFDakYsb0tBQVFBO29EQUFDMkUsV0FBVTs7Ozs7O2dEQUFpQjs7Ozs7Ozs7Ozs7O2tEQUl6Qyw4REFBQ2pFLGtEQUFJQTt3Q0FBQ3NFLE1BQUs7a0RBQ1QsNEVBQUM1Rix5REFBTUE7OzhEQUNMLDhEQUFDWSxvS0FBUUE7b0RBQUMyRSxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQVVqRCw4REFBQ0Q7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDakUsa0RBQUlBOzRCQUFDc0UsTUFBSztzQ0FDVCw0RUFBQzNGLHFEQUFJQTtnQ0FBQ3NGLFdBQVU7O2tEQUNkLDhEQUFDcEYsMkRBQVVBO3dDQUFDb0YsV0FBVTs7MERBQ3BCLDhEQUFDbkYsMERBQVNBO2dEQUFDbUYsV0FBVTswREFBc0I7Ozs7OzswREFDM0MsOERBQUMzRSxvS0FBUUE7Z0RBQUMyRSxXQUFVOzs7Ozs7Ozs7Ozs7a0RBRXRCLDhEQUFDckYsNERBQVdBOzswREFDViw4REFBQ29GO2dEQUFJQyxXQUFVOzBEQUFzQjVELFVBQVVLLEtBQUssQ0FBQ0MsZUFBZTs7Ozs7OzBEQUNwRSw4REFBQ3dEO2dEQUFFRixXQUFVOztvREFDVjVELFVBQVVLLEtBQUssQ0FBQ0UsaUJBQWlCO29EQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTTNDLDhEQUFDWixrREFBSUE7NEJBQUNzRSxNQUFLO3NDQUNULDRFQUFDM0YscURBQUlBO2dDQUFDc0YsV0FBVTs7a0RBQ2QsOERBQUNwRiwyREFBVUE7d0NBQUNvRixXQUFVOzswREFDcEIsOERBQUNuRiwwREFBU0E7Z0RBQUNtRixXQUFVOzBEQUFzQjs7Ozs7OzBEQUMzQyw4REFBQzVFLHdLQUFZQTtnREFBQzRFLFdBQVU7Ozs7Ozs7Ozs7OztrREFFMUIsOERBQUNyRiw0REFBV0E7OzBEQUNWLDhEQUFDb0Y7Z0RBQUlDLFdBQVU7MERBQXNCNUQsVUFBVUssS0FBSyxDQUFDSyxXQUFXOzs7Ozs7MERBQ2hFLDhEQUFDb0Q7Z0RBQUVGLFdBQVU7O29EQUNWNUQsVUFBVUssS0FBSyxDQUFDTSxhQUFhO29EQUFDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBTXZDLDhEQUFDaEIsa0RBQUlBOzRCQUFDc0UsTUFBSztzQ0FDVCw0RUFBQzNGLHFEQUFJQTtnQ0FBQ3NGLFdBQVU7O2tEQUNkLDhEQUFDcEYsMkRBQVVBO3dDQUFDb0YsV0FBVTs7MERBQ3BCLDhEQUFDbkYsMERBQVNBO2dEQUFDbUYsV0FBVTswREFBc0I7Ozs7OzswREFDM0MsOERBQUMzRSxvS0FBUUE7Z0RBQUMyRSxXQUFVOzs7Ozs7Ozs7Ozs7a0RBRXRCLDhEQUFDckYsNERBQVdBOzswREFDViw4REFBQ29GO2dEQUFJQyxXQUFVOzBEQUFzQjVELFVBQVVLLEtBQUssQ0FBQ1MsYUFBYTs7Ozs7OzBEQUNsRSw4REFBQ2dEO2dEQUFFRixXQUFVOztvREFDVjVELFVBQVVLLEtBQUssQ0FBQ1UsZUFBZTtvREFBQzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O3NDQU16Qyw4REFBQ3BCLGtEQUFJQTs0QkFBQ3NFLE1BQUs7c0NBQ1QsNEVBQUMzRixxREFBSUE7Z0NBQUNzRixXQUFVOztrREFDZCw4REFBQ3BGLDJEQUFVQTt3Q0FBQ29GLFdBQVU7OzBEQUNwQiw4REFBQ25GLDBEQUFTQTtnREFBQ21GLFdBQVU7MERBQXNCOzs7Ozs7MERBQzNDLDhEQUFDMUUscUtBQVNBO2dEQUFDMEUsV0FBVTs7Ozs7Ozs7Ozs7O2tEQUV2Qiw4REFBQ3JGLDREQUFXQTs7MERBQ1YsOERBQUNvRjtnREFBSUMsV0FBVTswREFBc0I1RCxVQUFVSyxLQUFLLENBQUNhLFlBQVk7Ozs7OzswREFDakUsOERBQUM0QztnREFBRUYsV0FBVTs7b0RBQ1Y1RCxVQUFVSyxLQUFLLENBQUNjLGNBQWM7b0RBQUM7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFRMUMsOERBQUN3QztvQkFBSUMsV0FBVTs7c0NBQ2IsOERBQUN0RixxREFBSUE7OzhDQUNILDhEQUFDRSwyREFBVUE7b0NBQUNvRixXQUFVOztzREFDcEIsOERBQUNuRiwwREFBU0E7NENBQUNtRixXQUFVO3NEQUFzQjs7Ozs7O3NEQUMzQyw4REFBQ3pFLHNLQUFVQTs0Q0FBQ3lFLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFeEIsOERBQUNyRiw0REFBV0E7O3NEQUNWLDhEQUFDb0Y7NENBQUlDLFdBQVU7c0RBQXNCbkUsMERBQWNBLENBQUNPLFVBQVVLLEtBQUssQ0FBQ2dCLFVBQVU7Ozs7OztzREFDOUUsOERBQUN5Qzs0Q0FBRUYsV0FBVTtzREFBZ0M7Ozs7Ozs7Ozs7Ozs7Ozs7OztzQ0FJakQsOERBQUN0RixxREFBSUE7OzhDQUNILDhEQUFDRSwyREFBVUE7b0NBQUNvRixXQUFVOztzREFDcEIsOERBQUNuRiwwREFBU0E7NENBQUNtRixXQUFVO3NEQUFzQjs7Ozs7O3NEQUMzQyw4REFBQ3RFLHNLQUFVQTs0Q0FBQ3NFLFdBQVU7Ozs7Ozs7Ozs7Ozs4Q0FFeEIsOERBQUNyRiw0REFBV0E7O3NEQUNWLDhEQUFDb0Y7NENBQUlDLFdBQVU7c0RBQXFDbkUsMERBQWNBLENBQUNPLFVBQVVLLEtBQUssQ0FBQ2lCLFNBQVM7Ozs7OztzREFDNUYsOERBQUN3Qzs0Q0FBRUYsV0FBVTs7Z0RBQ1Y1RCxVQUFVSyxLQUFLLENBQUNnQixVQUFVLEdBQUcsSUFBSThDLEtBQUtDLEtBQUssQ0FBQyxVQUFXL0QsS0FBSyxDQUFDaUIsU0FBUyxHQUFHdEIsVUFBVUssS0FBSyxDQUFDZ0IsVUFBVSxHQUFJLE9BQU87Z0RBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBS3ZILDhEQUFDL0MscURBQUlBOzs4Q0FDSCw4REFBQ0UsMkRBQVVBO29DQUFDb0YsV0FBVTs7c0RBQ3BCLDhEQUFDbkYsMERBQVNBOzRDQUFDbUYsV0FBVTtzREFBc0I7Ozs7OztzREFDM0MsOERBQUN2RSxpS0FBS0E7NENBQUN1RSxXQUFVOzs7Ozs7Ozs7Ozs7OENBRW5CLDhEQUFDckYsNERBQVdBOztzREFDViw4REFBQ29GOzRDQUFJQyxXQUFVO3NEQUFzQ25FLDBEQUFjQSxDQUFDTyxVQUFVSyxLQUFLLENBQUNrQixZQUFZOzs7Ozs7c0RBQ2hHLDhEQUFDdUM7NENBQUVGLFdBQVU7c0RBQWdDOzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBSWpELDhEQUFDdEYscURBQUlBOzs4Q0FDSCw4REFBQ0UsMkRBQVVBO29DQUFDb0YsV0FBVTs7c0RBQ3BCLDhEQUFDbkYsMERBQVNBOzRDQUFDbUYsV0FBVTtzREFBc0I7Ozs7OztzREFDM0MsOERBQUN4RSx3S0FBWUE7NENBQUN3RSxXQUFVOzs7Ozs7Ozs7Ozs7OENBRTFCLDhEQUFDckYsNERBQVdBOztzREFDViw4REFBQ29GOzRDQUFJQyxXQUFVO3NEQUFtQ25FLDBEQUFjQSxDQUFDTyxVQUFVSyxLQUFLLENBQUNtQixXQUFXOzs7Ozs7c0RBQzVGLDhEQUFDc0M7NENBQUVGLFdBQVU7O2dEQUNWNUQsVUFBVUssS0FBSyxDQUFDZ0IsVUFBVSxHQUFHLElBQUk4QyxLQUFLQyxLQUFLLENBQUMsVUFBVy9ELEtBQUssQ0FBQ21CLFdBQVcsR0FBR3hCLFVBQVVLLEtBQUssQ0FBQ2dCLFVBQVUsR0FBSSxPQUFPO2dEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU8zSCw4REFBQ3NDO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ3RGLHFEQUFJQTs7OENBQ0gsOERBQUNFLDJEQUFVQTtvQ0FBQ29GLFdBQVU7O3NEQUNwQiw4REFBQ25GLDBEQUFTQTtzREFBQzs7Ozs7O3NEQUNYLDhEQUFDa0Isa0RBQUlBOzRDQUFDc0UsTUFBSztzREFDVCw0RUFBQzVGLHlEQUFNQTtnREFBQzZGLFNBQVE7Z0RBQVFHLE1BQUs7O29EQUFLO2tFQUVoQyw4REFBQzdFLHNLQUFVQTt3REFBQ29FLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUk1Qiw4REFBQ3JGLDREQUFXQTs4Q0FDViw0RUFBQ29GO3dDQUFJQyxXQUFVO2tEQUNaakIsaUJBQWlCMkIsR0FBRyxDQUFDLENBQUNDLDBCQUNyQiw4REFBQ1o7Z0RBQXVCQyxXQUFVOztrRUFDaEMsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQWVXLFVBQVUxQixnQkFBZ0I7Ozs7OzswRUFDeEQsOERBQUNjO2dFQUFJQyxXQUFVOzBFQUF5QlcsVUFBVXpCLGFBQWE7Ozs7OzswRUFDL0QsOERBQUNhO2dFQUFJQyxXQUFVOzBFQUF5QmxFLHNEQUFVQSxDQUFDNkUsVUFBVXRCLFVBQVU7Ozs7Ozs7Ozs7OztrRUFFekUsOERBQUNVO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQWVuRSwwREFBY0EsQ0FBQzhFLFVBQVV2QixZQUFZOzs7Ozs7MEVBQ25FLDhEQUFDd0I7Z0VBQUtaLFdBQVcsdUVBQXdHLE9BQWpDSCxlQUFlYyxVQUFVeEIsTUFBTTswRUFDcEhTLGNBQWNlLFVBQVV4QixNQUFNOzs7Ozs7Ozs7Ozs7OytDQVQzQndCLFVBQVUzQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBbUI5Qiw4REFBQ3RFLHFEQUFJQTs7OENBQ0gsOERBQUNFLDJEQUFVQTtvQ0FBQ29GLFdBQVU7O3NEQUNwQiw4REFBQ25GLDBEQUFTQTtzREFBQzs7Ozs7O3NEQUNYLDhEQUFDa0Isa0RBQUlBOzRDQUFDc0UsTUFBSztzREFDVCw0RUFBQzVGLHlEQUFNQTtnREFBQzZGLFNBQVE7Z0RBQVFHLE1BQUs7O29EQUFLO2tFQUVoQyw4REFBQzdFLHNLQUFVQTt3REFBQ29FLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhDQUk1Qiw4REFBQ3JGLDREQUFXQTs4Q0FDViw0RUFBQ29GO3dDQUFJQyxXQUFVO2tEQUNaVixlQUFlb0IsR0FBRyxDQUFDLENBQUNHLHdCQUNuQiw4REFBQ2Q7Z0RBQXFCQyxXQUFVOztrRUFDOUIsOERBQUNEO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQWVhLFFBQVF0QixjQUFjOzs7Ozs7MEVBQ3BELDhEQUFDUTtnRUFBSUMsV0FBVTswRUFBeUJhLFFBQVEzQixhQUFhOzs7Ozs7MEVBQzdELDhEQUFDYTtnRUFBSUMsV0FBVTs7b0VBQXdCO29FQUFVbEUsc0RBQVVBLENBQUMrRSxRQUFRckIsUUFBUTs7Ozs7Ozs7Ozs7OztrRUFFOUUsOERBQUNPO3dEQUFJQyxXQUFVOzswRUFDYiw4REFBQ0Q7Z0VBQUlDLFdBQVU7MEVBQWVuRSwwREFBY0EsQ0FBQ2dGLFFBQVF6QixZQUFZOzs7Ozs7MEVBQ2pFLDhEQUFDd0I7Z0VBQUtaLFdBQVcsdUVBQXNHLE9BQS9CSCxlQUFlZ0IsUUFBUTFCLE1BQU07MEVBQ2xIUyxjQUFjaUIsUUFBUTFCLE1BQU07Ozs7Ozs7Ozs7Ozs7K0NBVHpCMEIsUUFBUTdCLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFvQjlCLDhEQUFDdEUscURBQUlBOztzQ0FDSCw4REFBQ0UsMkRBQVVBO3NDQUNULDRFQUFDQywwREFBU0E7Z0NBQUNtRixXQUFVOztrREFDbkIsOERBQUNyRSxpS0FBS0E7d0NBQUNxRSxXQUFVOzs7Ozs7a0RBQ2pCLDhEQUFDWTtrREFBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7c0NBR1YsOERBQUNqRyw0REFBV0E7c0NBQ1YsNEVBQUNvRjtnQ0FBSUMsV0FBVTswQ0FDYiw0RUFBQ2xGLHVEQUFLQTs7c0RBQ0osOERBQUNJLDZEQUFXQTtzREFDViw0RUFBQ0MsMERBQVFBOztrRUFDUCw4REFBQ0YsMkRBQVNBO2tFQUFDOzs7Ozs7a0VBQ1gsOERBQUNBLDJEQUFTQTtrRUFBQzs7Ozs7O2tFQUNYLDhEQUFDQSwyREFBU0E7a0VBQUM7Ozs7OztrRUFDWCw4REFBQ0EsMkRBQVNBO2tFQUFDOzs7Ozs7Ozs7Ozs7Ozs7OztzREFHZiw4REFBQ0YsMkRBQVNBO3NEQUNQMEUsYUFBYWlCLEdBQUcsQ0FBQyxDQUFDSSxVQUFVQyxzQkFDM0IsOERBQUM1RiwwREFBUUE7O3NFQUNQLDhEQUFDSCwyREFBU0E7c0VBQ1IsNEVBQUMrRTtnRUFBSUMsV0FBVTs7a0ZBQ2IsOERBQUNEO3dFQUFJQyxXQUFVO2tGQUNiLDRFQUFDWTs0RUFBS1osV0FBVTtzRkFBcUNlLFFBQVE7Ozs7Ozs7Ozs7O2tGQUUvRCw4REFBQ0g7d0VBQUtaLFdBQVU7a0ZBQWVjLFNBQVNwQixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7OztzRUFHaEQsOERBQUMxRSwyREFBU0E7c0VBQUU4RixTQUFTeEUsTUFBTTs7Ozs7O3NFQUMzQiw4REFBQ3RCLDJEQUFTQTs0REFBQ2dGLFdBQVU7c0VBQWVuRSwwREFBY0EsQ0FBQ2lGLFNBQVNuQixLQUFLOzs7Ozs7c0VBQ2pFLDhEQUFDM0UsMkRBQVNBO3NFQUFFYSwwREFBY0EsQ0FBQ2lGLFNBQVNuQixLQUFLLEdBQUdtQixTQUFTeEUsTUFBTTs7Ozs7OzttREFYOUN5RTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQXNCbkM7R0FuYndCL0U7O1FBQ0wzQixtREFBT0E7OztLQURGMkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL3BhZ2VzL3NhbGVzL2luZGV4LnRzeD8zNTJjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IHVzZUF1dGggfSBmcm9tICdAL2hvb2tzL3VzZUF1dGgnXG5pbXBvcnQgeyBoYXNQZXJtaXNzaW9uLCBQRVJNSVNTSU9OUyB9IGZyb20gJ0AvbGliL2F1dGgnXG5pbXBvcnQgTGF5b3V0IGZyb20gJ0AvY29tcG9uZW50cy9sYXlvdXQvTGF5b3V0J1xuaW1wb3J0IHsgQnV0dG9uIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL2J1dHRvbidcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7XG4gIFRhYmxlLFxuICBUYWJsZUJvZHksXG4gIFRhYmxlQ2VsbCxcbiAgVGFibGVIZWFkLFxuICBUYWJsZUhlYWRlcixcbiAgVGFibGVSb3csXG59IGZyb20gJ0AvY29tcG9uZW50cy91aS90YWJsZSdcbmltcG9ydCB7XG4gIFBsdXMsXG4gIFNob3BwaW5nQ2FydCxcbiAgRmlsZVRleHQsXG4gIFJvdGF0ZUNjdyxcbiAgVHJlbmRpbmdVcCxcbiAgVHJlbmRpbmdEb3duLFxuICBDbG9jayxcbiAgQ2hlY2tDaXJjbGUsXG4gIEFsZXJ0VHJpYW5nbGUsXG4gIFBhY2thZ2UsXG4gIERvbGxhclNpZ24sXG4gIFVzZXJzLFxuICBDYWxlbmRhcixcbiAgQXJyb3dSaWdodFxufSBmcm9tICdsdWNpZGUtcmVhY3QnXG5pbXBvcnQgeyBmb3JtYXRDdXJyZW5jeSwgZm9ybWF0RGF0ZSB9IGZyb20gJ0AvbGliL3V0aWxzJ1xuaW1wb3J0IExpbmsgZnJvbSAnbmV4dC9saW5rJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBTYWxlc092ZXJ2aWV3KCkge1xuICBjb25zdCB7IHVzZXIgfSA9IHVzZUF1dGgoKVxuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKVxuICBjb25zdCBbc2FsZXNEYXRhLCBzZXRTYWxlc0RhdGFdID0gdXNlU3RhdGUoe1xuICAgIG9yZGVyczogW10sXG4gICAgaW52b2ljZXM6IFtdLFxuICAgIGN1c3RvbWVyczogW10sXG4gICAgc3RhdHM6IHtcbiAgICAgIHRvdGFsUXVvdGF0aW9uczogMCxcbiAgICAgIHBlbmRpbmdRdW90YXRpb25zOiAwLFxuICAgICAgYXBwcm92ZWRRdW90YXRpb25zOiAwLFxuICAgICAgZXhwaXJlZFF1b3RhdGlvbnM6IDAsXG4gICAgICB0b3RhbE9yZGVyczogMCxcbiAgICAgIHBlbmRpbmdPcmRlcnM6IDAsXG4gICAgICBjb25maXJtZWRPcmRlcnM6IDAsXG4gICAgICBzaGlwcGVkT3JkZXJzOiAwLFxuICAgICAgdG90YWxJbnZvaWNlczogMCxcbiAgICAgIHBlbmRpbmdJbnZvaWNlczogMCxcbiAgICAgIHBhaWRJbnZvaWNlczogMCxcbiAgICAgIG92ZXJkdWVJbnZvaWNlczogMCxcbiAgICAgIHRvdGFsUmV0dXJuczogMCxcbiAgICAgIHBlbmRpbmdSZXR1cm5zOiAwLFxuICAgICAgcHJvY2Vzc2VkUmV0dXJuczogMCxcbiAgICAgIHRvdGFsVmFsdWU6IDAsXG4gICAgICBwYWlkVmFsdWU6IDAsXG4gICAgICBwZW5kaW5nVmFsdWU6IDAsXG4gICAgICByZXR1cm5WYWx1ZTogMFxuICAgIH1cbiAgfSlcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGZldGNoU2FsZXNEYXRhKClcbiAgfSwgW10pXG5cbiAgY29uc3QgZmV0Y2hTYWxlc0RhdGEgPSBhc3luYyAoKSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKVxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3Bvc3RncmVzL2RhdGE/dHlwZT1zYWxlcy1vcmRlcnMnKVxuICAgICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG5cbiAgICAgIGlmIChyZXN1bHQuc3VjY2Vzcykge1xuICAgICAgICAvLyDYrdiz2KfYqCDYp9mE2KXYrdi12KfYptmK2KfYqiDZhdmGINin2YTYqNmK2KfZhtin2Kog2KfZhNit2YLZitmC2YrYqVxuICAgICAgICBjb25zdCBvcmRlcnMgPSByZXN1bHQuZGF0YSB8fCBbXVxuICAgICAgICBjb25zdCBzdGF0cyA9IHtcbiAgICAgICAgICB0b3RhbFF1b3RhdGlvbnM6IDAsXG4gICAgICAgICAgcGVuZGluZ1F1b3RhdGlvbnM6IDAsXG4gICAgICAgICAgYXBwcm92ZWRRdW90YXRpb25zOiAwLFxuICAgICAgICAgIGV4cGlyZWRRdW90YXRpb25zOiAwLFxuICAgICAgICAgIHRvdGFsT3JkZXJzOiBvcmRlcnMubGVuZ3RoLFxuICAgICAgICAgIHBlbmRpbmdPcmRlcnM6IG9yZGVycy5maWx0ZXIoKG86IGFueSkgPT4gby5vcmRlcl9zdGF0dXMgPT09ICdwZW5kaW5nJykubGVuZ3RoLFxuICAgICAgICAgIGNvbmZpcm1lZE9yZGVyczogb3JkZXJzLmZpbHRlcigobzogYW55KSA9PiBvLm9yZGVyX3N0YXR1cyA9PT0gJ2NvbmZpcm1lZCcpLmxlbmd0aCxcbiAgICAgICAgICBzaGlwcGVkT3JkZXJzOiBvcmRlcnMuZmlsdGVyKChvOiBhbnkpID0+IG8ub3JkZXJfc3RhdHVzID09PSAnc2hpcHBlZCcpLmxlbmd0aCxcbiAgICAgICAgICB0b3RhbEludm9pY2VzOiAwLFxuICAgICAgICAgIHBlbmRpbmdJbnZvaWNlczogMCxcbiAgICAgICAgICBwYWlkSW52b2ljZXM6IDAsXG4gICAgICAgICAgb3ZlcmR1ZUludm9pY2VzOiAwLFxuICAgICAgICAgIHRvdGFsUmV0dXJuczogMCxcbiAgICAgICAgICBwZW5kaW5nUmV0dXJuczogMCxcbiAgICAgICAgICBwcm9jZXNzZWRSZXR1cm5zOiAwLFxuICAgICAgICAgIHRvdGFsVmFsdWU6IG9yZGVycy5yZWR1Y2UoKHN1bTogbnVtYmVyLCBvcmRlcjogYW55KSA9PiBzdW0gKyAob3JkZXIuZmluYWxfYW1vdW50IHx8IDApLCAwKSxcbiAgICAgICAgICBwYWlkVmFsdWU6IG9yZGVycy5maWx0ZXIoKG86IGFueSkgPT4gby5wYXltZW50X3N0YXR1cyA9PT0gJ3BhaWQnKS5yZWR1Y2UoKHN1bTogbnVtYmVyLCBvcmRlcjogYW55KSA9PiBzdW0gKyAob3JkZXIuZmluYWxfYW1vdW50IHx8IDApLCAwKSxcbiAgICAgICAgICBwZW5kaW5nVmFsdWU6IG9yZGVycy5maWx0ZXIoKG86IGFueSkgPT4gby5wYXltZW50X3N0YXR1cyA9PT0gJ3BlbmRpbmcnKS5yZWR1Y2UoKHN1bTogbnVtYmVyLCBvcmRlcjogYW55KSA9PiBzdW0gKyAob3JkZXIuZmluYWxfYW1vdW50IHx8IDApLCAwKSxcbiAgICAgICAgICByZXR1cm5WYWx1ZTogMFxuICAgICAgICB9XG5cbiAgICAgICAgc2V0U2FsZXNEYXRhKHtcbiAgICAgICAgICBvcmRlcnMsXG4gICAgICAgICAgaW52b2ljZXM6IFtdLFxuICAgICAgICAgIGN1c3RvbWVyczogW10sXG4gICAgICAgICAgc3RhdHNcbiAgICAgICAgfSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KzZhNioINio2YrYp9mG2KfYqiDYp9mE2YXYqNmK2LnYp9iqOicsIGVycm9yKVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIC8vIFJlY2VudCBhY3Rpdml0aWVzXG4gIGNvbnN0IHJlY2VudFF1b3RhdGlvbnMgPSBbXG4gICAge1xuICAgICAgaWQ6ICcxJyxcbiAgICAgIHF1b3RhdGlvbl9udW1iZXI6ICdRVC0yMDI0LTAwMScsXG4gICAgICBjdXN0b21lcl9uYW1lOiAn2LTYsdmD2Kkg2KfZhNiq2YLZhtmK2Kkg2KfZhNmF2KrZgtiv2YXYqScsXG4gICAgICBzdGF0dXM6ICdwZW5kaW5nJyxcbiAgICAgIHRvdGFsX2Ftb3VudDogODMwMDAsXG4gICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0yNSdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnMicsXG4gICAgICBxdW90YXRpb25fbnVtYmVyOiAnUVQtMjAyNC0wMDInLFxuICAgICAgY3VzdG9tZXJfbmFtZTogJ9mF2KTYs9iz2Kkg2KfZhNil2YTZg9iq2LHZiNmG2YrYp9iqINin2YTYrdiv2YrYq9ipJyxcbiAgICAgIHN0YXR1czogJ2FwcHJvdmVkJyxcbiAgICAgIHRvdGFsX2Ftb3VudDogNTAzMDAsXG4gICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0yNCdcbiAgICB9LFxuICAgIHtcbiAgICAgIGlkOiAnMycsXG4gICAgICBxdW90YXRpb25fbnVtYmVyOiAnUVQtMjAyNC0wMDMnLFxuICAgICAgY3VzdG9tZXJfbmFtZTogJ9i02LHZg9ipINin2YTYo9is2YfYstipINin2YTYsNmD2YrYqScsXG4gICAgICBzdGF0dXM6ICdleHBpcmVkJyxcbiAgICAgIHRvdGFsX2Ftb3VudDogMzU5ODAsXG4gICAgICBjcmVhdGVkX2F0OiAnMjAyNC0wMS0yMydcbiAgICB9XG4gIF1cblxuICBjb25zdCByZWNlbnRJbnZvaWNlcyA9IFtcbiAgICB7XG4gICAgICBpZDogJzEnLFxuICAgICAgaW52b2ljZV9udW1iZXI6ICdJTlYtMjAyNC0wMDEnLFxuICAgICAgY3VzdG9tZXJfbmFtZTogJ9i02LHZg9ipINin2YTYqtmC2YbZitipINin2YTZhdiq2YLYr9mF2KknLFxuICAgICAgc3RhdHVzOiAncGVuZGluZycsXG4gICAgICB0b3RhbF9hbW91bnQ6IDgzMDAwLFxuICAgICAgZHVlX2RhdGU6ICcyMDI0LTAyLTIwJ1xuICAgIH0sXG4gICAge1xuICAgICAgaWQ6ICcyJyxcbiAgICAgIGludm9pY2VfbnVtYmVyOiAnSU5WLTIwMjQtMDAyJyxcbiAgICAgIGN1c3RvbWVyX25hbWU6ICfZhdik2LPYs9ipINin2YTYpdmE2YPYqtix2YjZhtmK2KfYqiDYp9mE2K3Yr9mK2KvYqScsXG4gICAgICBzdGF0dXM6ICdwYWlkJyxcbiAgICAgIHRvdGFsX2Ftb3VudDogNTAzMDAsXG4gICAgICBkdWVfZGF0ZTogJzIwMjQtMDItMDUnXG4gICAgfSxcbiAgICB7XG4gICAgICBpZDogJzMnLFxuICAgICAgaW52b2ljZV9udW1iZXI6ICdJTlYtMjAyNC0wMDMnLFxuICAgICAgY3VzdG9tZXJfbmFtZTogJ9i02LHZg9ipINin2YTYo9is2YfYstipINin2YTYsNmD2YrYqScsXG4gICAgICBzdGF0dXM6ICdvdmVyZHVlJyxcbiAgICAgIHRvdGFsX2Ftb3VudDogMzU5ODAsXG4gICAgICBkdWVfZGF0ZTogJzIwMjQtMDEtMjUnXG4gICAgfVxuICBdXG5cbiAgY29uc3QgdG9wQ3VzdG9tZXJzID0gW1xuICAgIHsgbmFtZTogJ9i02LHZg9ipINin2YTYqtmC2YbZitipINin2YTZhdiq2YLYr9mF2KknLCBvcmRlcnM6IDQ1LCB2YWx1ZTogODkwMDAwIH0sXG4gICAgeyBuYW1lOiAn2YXYpNiz2LPYqSDYp9mE2KXZhNmD2KrYsdmI2YbZitin2Kog2KfZhNit2K/Zitir2KknLCBvcmRlcnM6IDM4LCB2YWx1ZTogNzIwMDAwIH0sXG4gICAgeyBuYW1lOiAn2LTYsdmD2Kkg2KfZhNij2KzZh9iy2Kkg2KfZhNiw2YPZitipJywgb3JkZXJzOiAzMiwgdmFsdWU6IDU4MDAwMCB9LFxuICAgIHsgbmFtZTogJ9mF2KrYrNixINin2YTZg9mF2KjZitmI2KrYsSDYp9mE2YXYqtmC2K/ZhScsIG9yZGVyczogMjgsIHZhbHVlOiA0NTAwMDAgfSxcbiAgICB7IG5hbWU6ICfYtNix2YPYqSDYp9mE2K3ZhNmI2YQg2KfZhNiq2YLZhtmK2KknLCBvcmRlcnM6IDI1LCB2YWx1ZTogMzgwMDAwIH1cbiAgXVxuXG4gIC8vIEhlbHBlciBmdW5jdGlvbnNcbiAgY29uc3QgZ2V0U3RhdHVzVGV4dCA9IChzdGF0dXM6IHN0cmluZykgPT4ge1xuICAgIHN3aXRjaCAoc3RhdHVzKSB7XG4gICAgICBjYXNlICdwZW5kaW5nJzogcmV0dXJuICfZgdmKINin2YTYp9mG2KrYuNin2LEnXG4gICAgICBjYXNlICdhcHByb3ZlZCc6IHJldHVybiAn2YXYudiq2YXYrydcbiAgICAgIGNhc2UgJ2NvbmZpcm1lZCc6IHJldHVybiAn2YXYpNmD2K8nXG4gICAgICBjYXNlICdzaGlwcGVkJzogcmV0dXJuICfYqtmFINin2YTYtNit2YYnXG4gICAgICBjYXNlICdwYWlkJzogcmV0dXJuICfZhdiv2YHZiNi52KknXG4gICAgICBjYXNlICdvdmVyZHVlJzogcmV0dXJuICfZhdiq2KPYrtix2KknXG4gICAgICBjYXNlICdleHBpcmVkJzogcmV0dXJuICfZhdmG2KrZh9mK2Kkg2KfZhNi12YTYp9it2YrYqSdcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAn2LrZitixINmF2K3Yr9ivJ1xuICAgIH1cbiAgfVxuXG4gIGNvbnN0IGdldFN0YXR1c0NvbG9yID0gKHN0YXR1czogc3RyaW5nKSA9PiB7XG4gICAgc3dpdGNoIChzdGF0dXMpIHtcbiAgICAgIGNhc2UgJ3BlbmRpbmcnOiByZXR1cm4gJ2JnLXllbGxvdy0xMDAgdGV4dC15ZWxsb3ctODAwJ1xuICAgICAgY2FzZSAnYXBwcm92ZWQnOiByZXR1cm4gJ2JnLWJsdWUtMTAwIHRleHQtYmx1ZS04MDAnXG4gICAgICBjYXNlICdjb25maXJtZWQnOiByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgIGNhc2UgJ3NoaXBwZWQnOiByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgIGNhc2UgJ3BhaWQnOiByZXR1cm4gJ2JnLWdyZWVuLTEwMCB0ZXh0LWdyZWVuLTgwMCdcbiAgICAgIGNhc2UgJ292ZXJkdWUnOiByZXR1cm4gJ2JnLXJlZC0xMDAgdGV4dC1yZWQtODAwJ1xuICAgICAgY2FzZSAnZXhwaXJlZCc6IHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnYmctZ3JheS0xMDAgdGV4dC1ncmF5LTgwMCdcbiAgICB9XG4gIH1cblxuICBpZiAoIWhhc1Blcm1pc3Npb24odXNlciwgUEVSTUlTU0lPTlMuU0FMRVNfVklFVykpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPExheW91dD5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBweS0xMlwiPlxuICAgICAgICAgIDxoMiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMFwiPti62YrYsSDZhdi12LHYrTwvaDI+XG4gICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPtmE2YrYsyDZhNiv2YrZgyDYtdmE2KfYrdmK2Kkg2YTYudix2LYg2YjYrdiv2Kkg2KfZhNmF2KjZiti52KfYqjwvcD5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L0xheW91dD5cbiAgICApXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxMYXlvdXQ+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNlwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwidGV4dC0zeGwgZm9udC1ib2xkIHRyYWNraW5nLXRpZ2h0XCI+2KfZhNmF2KjZiti52KfYqjwvaDE+XG4gICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAg2YTZiNit2Kkg2KrYrdmD2YUg2LTYp9mF2YTYqSDZhNil2K/Yp9ix2Kkg2KfZhNmF2KjZiti52KfYqiDZiNin2YTYudmF2YTYp9ihXG4gICAgICAgICAgICA8L3A+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgIHtoYXNQZXJtaXNzaW9uKHVzZXIsIFBFUk1JU1NJT05TLlNBTEVTX0NSRUFURSkgJiYgKFxuICAgICAgICAgICAgICA8PlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2FsZXMvcXVvdGF0aW9uc1wiPlxuICAgICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwib3V0bGluZVwiPlxuICAgICAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICAgICAg2LnYsdi2INiz2LnYsSDYrNiv2YrYr1xuICAgICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2FsZXMvaW52b2ljZXNcIj5cbiAgICAgICAgICAgICAgICAgIDxCdXR0b24+XG4gICAgICAgICAgICAgICAgICAgIDxGaWxlVGV4dCBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICAgICDZgdin2KrZiNix2Kkg2YXYqNmK2LnYp9iqINis2K/Zitiv2KlcbiAgICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUXVpY2sgTmF2aWdhdGlvbiAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgPExpbmsgaHJlZj1cIi9zYWxlcy9xdW90YXRpb25zXCI+XG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3cgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2LnYsdmI2LYg2KfZhNij2LPYudin2LE8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWJsdWUtNjAwXCIgLz5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57c2FsZXNEYXRhLnN0YXRzLnRvdGFsUXVvdGF0aW9uc308L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAge3NhbGVzRGF0YS5zdGF0cy5wZW5kaW5nUXVvdGF0aW9uc30g2YHZiiDYp9mE2KfZhtiq2LjYp9ixXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvTGluaz5cblxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2FsZXMvb3JkZXJzXCI+XG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3cgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2LfZhNio2KfYqiDYp9mE2YXYqNmK2LnYp9iqPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgICAgPFNob3BwaW5nQ2FydCBjbGFzc05hbWU9XCJoLTQgdy00IHRleHQtZ3JlZW4tNjAwXCIgLz5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57c2FsZXNEYXRhLnN0YXRzLnRvdGFsT3JkZXJzfTwvZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAgICB7c2FsZXNEYXRhLnN0YXRzLnBlbmRpbmdPcmRlcnN9INmB2Yog2KfZhNin2YbYqti42KfYsVxuICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICAgIDwvQ2FyZD5cbiAgICAgICAgICA8L0xpbms+XG5cbiAgICAgICAgICA8TGluayBocmVmPVwiL3NhbGVzL2ludm9pY2VzXCI+XG4gICAgICAgICAgICA8Q2FyZCBjbGFzc05hbWU9XCJob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1zaGFkb3cgY3Vyc29yLXBvaW50ZXJcIj5cbiAgICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2YHZiNin2KrZitixINin2YTZhdio2YrYudin2Ko8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8RmlsZVRleHQgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LXB1cnBsZS02MDBcIiAvPlxuICAgICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntzYWxlc0RhdGEuc3RhdHMudG90YWxJbnZvaWNlc308L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAge3NhbGVzRGF0YS5zdGF0cy5wZW5kaW5nSW52b2ljZXN9INiq2K3Yqtin2Kwg2K/Zgdi5XG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvTGluaz5cblxuICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2FsZXMvcmV0dXJuc1wiPlxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwiaG92ZXI6c2hhZG93LW1kIHRyYW5zaXRpb24tc2hhZG93IGN1cnNvci1wb2ludGVyXCI+XG4gICAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBzcGFjZS15LTAgcGItMlwiPlxuICAgICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPtmF2LHYqtis2LnYp9iqINin2YTZhdio2YrYudin2Ko8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgICA8Um90YXRlQ2N3IGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNjAwXCIgLz5cbiAgICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGRcIj57c2FsZXNEYXRhLnN0YXRzLnRvdGFsUmV0dXJuc308L2Rpdj5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgICAge3NhbGVzRGF0YS5zdGF0cy5wZW5kaW5nUmV0dXJuc30g2YHZiiDYp9mE2KfZhtiq2LjYp9ixXG4gICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgICAgPC9DYXJkPlxuICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgey8qIEZpbmFuY2lhbCBPdmVydmlldyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0yIGxnOmdyaWQtY29scy00IGdhcC00XCI+XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gc3BhY2UteS0wIHBiLTJcIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJ0ZXh0LXNtIGZvbnQtbWVkaXVtXCI+2KXYrNmF2KfZhNmKINmC2YrZhdipINin2YTZhdio2YrYudin2Ko8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPFRyZW5kaW5nVXAgY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LW11dGVkLWZvcmVncm91bmRcIiAvPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtMnhsIGZvbnQtYm9sZFwiPntmb3JtYXRDdXJyZW5jeShzYWxlc0RhdGEuc3RhdHMudG90YWxWYWx1ZSl9PC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+2KXYrNmF2KfZhNmKINin2YTZhdio2YrYudin2Ko8L3A+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPtin2YTZhdio2YTYuiDYp9mE2YXYrdi12YQ8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPERvbGxhclNpZ24gY2xhc3NOYW1lPVwiaC00IHctNCB0ZXh0LWdyZWVuLTYwMFwiIC8+XG4gICAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JlZW4tNjAwXCI+e2Zvcm1hdEN1cnJlbmN5KHNhbGVzRGF0YS5zdGF0cy5wYWlkVmFsdWUpfTwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPlxuICAgICAgICAgICAgICAgIHtzYWxlc0RhdGEuc3RhdHMudG90YWxWYWx1ZSA+IDAgPyBNYXRoLnJvdW5kKChzYWxlc0RhdGEuc3RhdHMucGFpZFZhbHVlIC8gc2FsZXNEYXRhLnN0YXRzLnRvdGFsVmFsdWUpICogMTAwKSA6IDB9JSDZhdmGINin2YTYpdis2YXYp9mE2YpcbiAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgPC9DYXJkQ29udGVudD5cbiAgICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiBzcGFjZS15LTAgcGItMlwiPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW1cIj7Yp9mE2YXYqNmE2Log2KfZhNmF2LPYqtit2YI8L0NhcmRUaXRsZT5cbiAgICAgICAgICAgICAgPENsb2NrIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC15ZWxsb3ctNjAwXCIgLz5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC15ZWxsb3ctNjAwXCI+e2Zvcm1hdEN1cnJlbmN5KHNhbGVzRGF0YS5zdGF0cy5wZW5kaW5nVmFsdWUpfTwvZGl2PlxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtbXV0ZWQtZm9yZWdyb3VuZFwiPtmK2KzYqCDYqtit2LXZitmE2Yc8L3A+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIDxDYXJkPlxuICAgICAgICAgICAgPENhcmRIZWFkZXIgY2xhc3NOYW1lPVwiZmxleCBmbGV4LXJvdyBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHNwYWNlLXktMCBwYi0yXCI+XG4gICAgICAgICAgICAgIDxDYXJkVGl0bGUgY2xhc3NOYW1lPVwidGV4dC1zbSBmb250LW1lZGl1bVwiPtmC2YrZhdipINin2YTZhdix2KrYrNi52KfYqjwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8VHJlbmRpbmdEb3duIGNsYXNzTmFtZT1cImgtNCB3LTQgdGV4dC1yZWQtNjAwXCIgLz5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBmb250LWJvbGQgdGV4dC1yZWQtNjAwXCI+e2Zvcm1hdEN1cnJlbmN5KHNhbGVzRGF0YS5zdGF0cy5yZXR1cm5WYWx1ZSl9PC9kaXY+XG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1tdXRlZC1mb3JlZ3JvdW5kXCI+XG4gICAgICAgICAgICAgICAge3NhbGVzRGF0YS5zdGF0cy50b3RhbFZhbHVlID4gMCA/IE1hdGgucm91bmQoKHNhbGVzRGF0YS5zdGF0cy5yZXR1cm5WYWx1ZSAvIHNhbGVzRGF0YS5zdGF0cy50b3RhbFZhbHVlKSAqIDEwMCkgOiAwfSUg2YXZhiDYp9mE2KXYrNmF2KfZhNmKXG4gICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUmVjZW50IEFjdGl2aXRpZXMgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMSBsZzpncmlkLWNvbHMtMiBnYXAtNlwiPlxuICAgICAgICAgIHsvKiBSZWNlbnQgUXVvdGF0aW9ucyAqL31cbiAgICAgICAgICA8Q2FyZD5cbiAgICAgICAgICAgIDxDYXJkSGVhZGVyIGNsYXNzTmFtZT1cImZsZXggZmxleC1yb3cgaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlblwiPlxuICAgICAgICAgICAgICA8Q2FyZFRpdGxlPtij2K3Yr9irINi52LHZiNi2INin2YTYo9iz2LnYp9ixPC9DYXJkVGl0bGU+XG4gICAgICAgICAgICAgIDxMaW5rIGhyZWY9XCIvc2FsZXMvcXVvdGF0aW9uc1wiPlxuICAgICAgICAgICAgICAgIDxCdXR0b24gdmFyaWFudD1cImdob3N0XCIgc2l6ZT1cInNtXCI+XG4gICAgICAgICAgICAgICAgICDYudix2LYg2KfZhNmD2YRcbiAgICAgICAgICAgICAgICAgIDxBcnJvd1JpZ2h0IGNsYXNzTmFtZT1cImgtNCB3LTQgbXItMlwiIC8+XG4gICAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDwvTGluaz5cbiAgICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTRcIj5cbiAgICAgICAgICAgICAgICB7cmVjZW50UXVvdGF0aW9ucy5tYXAoKHF1b3RhdGlvbikgPT4gKFxuICAgICAgICAgICAgICAgICAgPGRpdiBrZXk9e3F1b3RhdGlvbi5pZH0gY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1iZXR3ZWVuIHAtMyBib3JkZXIgcm91bmRlZC1sZ1wiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57cXVvdGF0aW9uLnF1b3RhdGlvbl9udW1iZXJ9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXNtIHRleHQtZ3JheS01MDBcIj57cXVvdGF0aW9uLmN1c3RvbWVyX25hbWV9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj57Zm9ybWF0RGF0ZShxdW90YXRpb24uY3JlYXRlZF9hdCl9PC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtcmlnaHRcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2Zvcm1hdEN1cnJlbmN5KHF1b3RhdGlvbi50b3RhbF9hbW91bnQpfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtnZXRTdGF0dXNDb2xvcihxdW90YXRpb24uc3RhdHVzKX1gfT5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtnZXRTdGF0dXNUZXh0KHF1b3RhdGlvbi5zdGF0dXMpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICAgIDwvQ2FyZD5cblxuICAgICAgICAgIHsvKiBSZWNlbnQgSW52b2ljZXMgKi99XG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlciBjbGFzc05hbWU9XCJmbGV4IGZsZXgtcm93IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZT7Yo9it2K/YqyDZgdmI2KfYqtmK2LEg2KfZhNmF2KjZiti52KfYqjwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgICA8TGluayBocmVmPVwiL3NhbGVzL2ludm9pY2VzXCI+XG4gICAgICAgICAgICAgICAgPEJ1dHRvbiB2YXJpYW50PVwiZ2hvc3RcIiBzaXplPVwic21cIj5cbiAgICAgICAgICAgICAgICAgINi52LHYtiDYp9mE2YPZhFxuICAgICAgICAgICAgICAgICAgPEFycm93UmlnaHQgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgPC9MaW5rPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgICAgIHtyZWNlbnRJbnZvaWNlcy5tYXAoKGludm9pY2UpID0+IChcbiAgICAgICAgICAgICAgICAgIDxkaXYga2V5PXtpbnZvaWNlLmlkfSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW4gcC0zIGJvcmRlciByb3VuZGVkLWxnXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntpbnZvaWNlLmludm9pY2VfbnVtYmVyfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktNTAwXCI+e2ludm9pY2UuY3VzdG9tZXJfbmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPtin2LPYqtit2YLYp9mCOiB7Zm9ybWF0RGF0ZShpbnZvaWNlLmR1ZV9kYXRlKX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1yaWdodFwiPlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57Zm9ybWF0Q3VycmVuY3koaW52b2ljZS50b3RhbF9hbW91bnQpfTwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT17YGlubGluZS1mbGV4IGl0ZW1zLWNlbnRlciBweC0yIHB5LTEgcm91bmRlZC1mdWxsIHRleHQteHMgZm9udC1tZWRpdW0gJHtnZXRTdGF0dXNDb2xvcihpbnZvaWNlLnN0YXR1cyl9YH0+XG4gICAgICAgICAgICAgICAgICAgICAgICB7Z2V0U3RhdHVzVGV4dChpbnZvaWNlLnN0YXR1cyl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogVG9wIEN1c3RvbWVycyAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlIGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgICAgPFVzZXJzIGNsYXNzTmFtZT1cImgtNSB3LTVcIiAvPlxuICAgICAgICAgICAgICA8c3Bhbj7Yo9mH2YUg2KfZhNi52YXZhNin2KE8L3NwYW4+XG4gICAgICAgICAgICA8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyb3VuZGVkLW1kIGJvcmRlclwiPlxuICAgICAgICAgICAgICA8VGFibGU+XG4gICAgICAgICAgICAgICAgPFRhYmxlSGVhZGVyPlxuICAgICAgICAgICAgICAgICAgPFRhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgICA8VGFibGVIZWFkPtin2LPZhSDYp9mE2LnZhdmK2YQ8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD7Yudiv2K8g2KfZhNi32YTYqNin2Ko8L1RhYmxlSGVhZD5cbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlSGVhZD7Ypdis2YXYp9mE2Yog2KfZhNmC2YrZhdipPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICAgIDxUYWJsZUhlYWQ+2YXYqtmI2LPYtyDZgtmK2YXYqSDYp9mE2LfZhNioPC9UYWJsZUhlYWQ+XG4gICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgIDwvVGFibGVIZWFkZXI+XG4gICAgICAgICAgICAgICAgPFRhYmxlQm9keT5cbiAgICAgICAgICAgICAgICAgIHt0b3BDdXN0b21lcnMubWFwKChjdXN0b21lciwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICAgICAgPFRhYmxlUm93IGtleT17aW5kZXh9PlxuICAgICAgICAgICAgICAgICAgICAgIDxUYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBzcGFjZS14LXJldmVyc2VcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LTggaC04IGJnLWJsdWUtMTAwIHJvdW5kZWQtZnVsbCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtYmx1ZS02MDAgZm9udC1tZWRpdW0gdGV4dC1zbVwiPntpbmRleCArIDF9PC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57Y3VzdG9tZXIubmFtZX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICA8L1RhYmxlQ2VsbD5cbiAgICAgICAgICAgICAgICAgICAgICA8VGFibGVDZWxsPntjdXN0b21lci5vcmRlcnN9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntmb3JtYXRDdXJyZW5jeShjdXN0b21lci52YWx1ZSl9PC9UYWJsZUNlbGw+XG4gICAgICAgICAgICAgICAgICAgICAgPFRhYmxlQ2VsbD57Zm9ybWF0Q3VycmVuY3koY3VzdG9tZXIudmFsdWUgLyBjdXN0b21lci5vcmRlcnMpfTwvVGFibGVDZWxsPlxuICAgICAgICAgICAgICAgICAgICA8L1RhYmxlUm93PlxuICAgICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgICAgPC9UYWJsZUJvZHk+XG4gICAgICAgICAgICAgIDwvVGFibGU+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG4gICAgICA8L2Rpdj5cbiAgICA8L0xheW91dD5cbiAgKVxufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlQXV0aCIsImhhc1Blcm1pc3Npb24iLCJQRVJNSVNTSU9OUyIsIkxheW91dCIsIkJ1dHRvbiIsIkNhcmQiLCJDYXJkQ29udGVudCIsIkNhcmRIZWFkZXIiLCJDYXJkVGl0bGUiLCJUYWJsZSIsIlRhYmxlQm9keSIsIlRhYmxlQ2VsbCIsIlRhYmxlSGVhZCIsIlRhYmxlSGVhZGVyIiwiVGFibGVSb3ciLCJTaG9wcGluZ0NhcnQiLCJGaWxlVGV4dCIsIlJvdGF0ZUNjdyIsIlRyZW5kaW5nVXAiLCJUcmVuZGluZ0Rvd24iLCJDbG9jayIsIkRvbGxhclNpZ24iLCJVc2VycyIsIkFycm93UmlnaHQiLCJmb3JtYXRDdXJyZW5jeSIsImZvcm1hdERhdGUiLCJMaW5rIiwiU2FsZXNPdmVydmlldyIsInVzZXIiLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsInNhbGVzRGF0YSIsInNldFNhbGVzRGF0YSIsIm9yZGVycyIsImludm9pY2VzIiwiY3VzdG9tZXJzIiwic3RhdHMiLCJ0b3RhbFF1b3RhdGlvbnMiLCJwZW5kaW5nUXVvdGF0aW9ucyIsImFwcHJvdmVkUXVvdGF0aW9ucyIsImV4cGlyZWRRdW90YXRpb25zIiwidG90YWxPcmRlcnMiLCJwZW5kaW5nT3JkZXJzIiwiY29uZmlybWVkT3JkZXJzIiwic2hpcHBlZE9yZGVycyIsInRvdGFsSW52b2ljZXMiLCJwZW5kaW5nSW52b2ljZXMiLCJwYWlkSW52b2ljZXMiLCJvdmVyZHVlSW52b2ljZXMiLCJ0b3RhbFJldHVybnMiLCJwZW5kaW5nUmV0dXJucyIsInByb2Nlc3NlZFJldHVybnMiLCJ0b3RhbFZhbHVlIiwicGFpZFZhbHVlIiwicGVuZGluZ1ZhbHVlIiwicmV0dXJuVmFsdWUiLCJmZXRjaFNhbGVzRGF0YSIsInJlc3BvbnNlIiwiZmV0Y2giLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsImRhdGEiLCJsZW5ndGgiLCJmaWx0ZXIiLCJvIiwib3JkZXJfc3RhdHVzIiwicmVkdWNlIiwic3VtIiwib3JkZXIiLCJmaW5hbF9hbW91bnQiLCJwYXltZW50X3N0YXR1cyIsImVycm9yIiwiY29uc29sZSIsInJlY2VudFF1b3RhdGlvbnMiLCJpZCIsInF1b3RhdGlvbl9udW1iZXIiLCJjdXN0b21lcl9uYW1lIiwic3RhdHVzIiwidG90YWxfYW1vdW50IiwiY3JlYXRlZF9hdCIsInJlY2VudEludm9pY2VzIiwiaW52b2ljZV9udW1iZXIiLCJkdWVfZGF0ZSIsInRvcEN1c3RvbWVycyIsIm5hbWUiLCJ2YWx1ZSIsImdldFN0YXR1c1RleHQiLCJnZXRTdGF0dXNDb2xvciIsIlNBTEVTX1ZJRVciLCJkaXYiLCJjbGFzc05hbWUiLCJoMiIsInAiLCJoMSIsIlNBTEVTX0NSRUFURSIsImhyZWYiLCJ2YXJpYW50IiwiTWF0aCIsInJvdW5kIiwic2l6ZSIsIm1hcCIsInF1b3RhdGlvbiIsInNwYW4iLCJpbnZvaWNlIiwiY3VzdG9tZXIiLCJpbmRleCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/sales/index.tsx\n"));

/***/ })

});