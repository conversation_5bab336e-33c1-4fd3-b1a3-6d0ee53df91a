import { useState } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { hasPermission, PERMISSIONS } from '@/lib/auth'
import Layout from '@/components/layout/Layout'
import OrderForm from '@/components/sales/OrderForm'
import OrderPrint from '@/components/sales/OrderPrint'
import ExportData from '@/components/sales/ExportData'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  Package,
  Truck,
  CheckCircle,
  Clock,
  XCircle,
  Alert<PERSON>riangle,
  Refresh<PERSON><PERSON>,
  Filter,
  Download,
  FileText,
  ArrowRight,
  ShoppingCart,
  Mail,
  MessageCircle,
  Share2,
  Star,
  Copy,
  Send
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

// Mock data for orders
const mockOrders = [
  {
    id: '1',
    order_number: 'ORD-2024-001',
    customer_name: 'أحمد محمد علي',
    customer_phone: '01234567890',
    customer_email: '<EMAIL>',
    status: 'confirmed',
    payment_status: 'paid',
    subtotal: 86500,
    tax_amount: 12110,
    discount_amount: 5000,
    total_amount: 93610,
    delivery_date: '2024-02-01',
    delivery_address: 'القاهرة - مدينة نصر - شارع مصطفى النحاس',
    items: [
      {
        id: '1',
        product_name: 'HP ZBook Studio G9',
        product_type: 'composite',
        quantity: 1,
        unit_price: 86500,
        total_price: 86500
      }
    ],
    notes: 'طلب عاجل - يرجى التحضير بأسرع وقت',
    created_at: '2024-01-15',
    created_by: 'محمد أحمد',
    branch_name: 'الفرع الرئيسي',
    warehouse_name: 'مخزن الإلكترونيات الرئيسي'
  },
  {
    id: '2',
    order_number: 'ORD-2024-002',
    customer_name: 'فاطمة حسن',
    customer_phone: '01098765432',
    customer_email: '<EMAIL>',
    status: 'processing',
    payment_status: 'partial',
    subtotal: 76000,
    tax_amount: 10640,
    discount_amount: 3000,
    total_amount: 83640,
    delivery_date: '2024-02-05',
    delivery_address: 'الإسكندرية - سموحة - شارع الحرية',
    items: [
      {
        id: '1',
        product_name: 'iPhone 15 Pro',
        product_type: 'simple',
        quantity: 2,
        unit_price: 38000,
        total_price: 76000
      }
    ],
    notes: 'دفع جزئي - باقي المبلغ عند الاستلام',
    created_at: '2024-01-18',
    created_by: 'سارة محمود',
    branch_name: 'فرع الإسكندرية',
    warehouse_name: 'مخزن الهواتف'
  },
  {
    id: '3',
    order_number: 'ORD-2024-003',
    customer_name: 'محمد عبدالله',
    customer_phone: '01555666777',
    customer_email: '<EMAIL>',
    status: 'shipped',
    payment_status: 'paid',
    subtotal: 56000,
    tax_amount: 7840,
    discount_amount: 2000,
    total_amount: 61840,
    delivery_date: '2024-01-25',
    delivery_address: 'الجيزة - المهندسين - شارع جامعة الدول العربية',
    items: [
      {
        id: '1',
        product_name: 'Samsung Galaxy S24',
        product_type: 'simple',
        quantity: 2,
        unit_price: 28000,
        total_price: 56000
      }
    ],
    notes: 'تم الشحن - رقم الشحنة: SH-001',
    created_at: '2024-01-20',
    created_by: 'أحمد علي',
    branch_name: 'الفرع الرئيسي',
    warehouse_name: 'مخزن الهواتف'
  },
  {
    id: '4',
    order_number: 'ORD-2024-004',
    customer_name: 'نورا سالم',
    customer_phone: '01777888999',
    customer_email: '<EMAIL>',
    status: 'delivered',
    payment_status: 'paid',
    subtotal: 17000,
    tax_amount: 2380,
    discount_amount: 1000,
    total_amount: 18380,
    delivery_date: '2024-01-22',
    delivery_address: 'القاهرة - مصر الجديدة - شارع الحجاز',
    items: [
      {
        id: '1',
        product_name: 'AirPods Pro',
        product_type: 'simple',
        quantity: 2,
        unit_price: 8500,
        total_price: 17000
      }
    ],
    notes: 'تم التسليم بنجاح',
    created_at: '2024-01-19',
    created_by: 'ليلى أحمد',
    branch_name: 'الفرع الرئيسي',
    warehouse_name: 'مخزن الإكسسوارات'
  }
]

export default function OrdersPage() {
  const { user } = useAuth()
  const [orders, setOrders] = useState(mockOrders)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [paymentFilter, setPaymentFilter] = useState('all')
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingOrder, setEditingOrder] = useState<any>(null)
  const [viewingOrder, setViewingOrder] = useState<any>(null)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)

  // تصفية الطلبات
  const filteredOrders = orders.filter(order => {
    const matchesSearch = order.order_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         order.customer_name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || order.status === statusFilter
    const matchesPayment = paymentFilter === 'all' || order.payment_status === paymentFilter
    return matchesSearch && matchesStatus && matchesPayment
  })

  const canCreate = hasPermission(user, PERMISSIONS.SALES_CREATE)
  const canEdit = hasPermission(user, PERMISSIONS.SALES_EDIT)
  const canDelete = hasPermission(user, PERMISSIONS.SALES_DELETE)

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'draft':
        return { color: 'bg-gray-100 text-gray-800', icon: Clock, text: 'مسودة' }
      case 'confirmed':
        return { color: 'bg-blue-100 text-blue-800', icon: CheckCircle, text: 'مؤكد' }
      case 'processing':
        return { color: 'bg-yellow-100 text-yellow-800', icon: Package, text: 'قيد التحضير' }
      case 'shipped':
        return { color: 'bg-purple-100 text-purple-800', icon: Truck, text: 'تم الشحن' }
      case 'delivered':
        return { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'تم التسليم' }
      case 'cancelled':
        return { color: 'bg-red-100 text-red-800', icon: XCircle, text: 'ملغي' }
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: Clock, text: 'غير محدد' }
    }
  }

  const getPaymentStatusInfo = (status: string) => {
    switch (status) {
      case 'pending':
        return { color: 'bg-orange-100 text-orange-800', text: 'معلق' }
      case 'partial':
        return { color: 'bg-yellow-100 text-yellow-800', text: 'جزئي' }
      case 'paid':
        return { color: 'bg-green-100 text-green-800', text: 'مدفوع' }
      default:
        return { color: 'bg-gray-100 text-gray-800', text: 'غير محدد' }
    }
  }

  const handleCreateOrder = () => {
    setEditingOrder(null)
    setIsFormOpen(true)
  }

  const handleViewOrder = (order: any) => {
    setViewingOrder(order)
    setIsViewModalOpen(true)
  }

  const handleEditOrder = (order: any) => {
    setEditingOrder(order)
    setIsFormOpen(true)
  }

  const handleSaveOrder = (orderData: any) => {
    if (editingOrder) {
      // تحديث طلب موجود
      setOrders(orders.map(o =>
        o.id === editingOrder.id ? orderData : o
      ))
      alert('تم تحديث الطلب بنجاح')
    } else {
      // إضافة طلب جديد
      setOrders([orderData, ...orders])
      alert('تم إنشاء الطلب بنجاح')
    }
    setIsFormOpen(false)
    setEditingOrder(null)
  }

  const handleDeleteOrder = (orderId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
      setOrders(orders.filter(o => o.id !== orderId))
    }
  }

  const handleUpdateStatus = (orderId: string, newStatus: string) => {
    setOrders(orders.map(o =>
      o.id === orderId ? { ...o, status: newStatus } : o
    ))
    alert(`تم تحديث حالة الطلب إلى: ${getStatusInfo(newStatus).text}`)
  }

  const handleConvertToInvoice = (order: any) => {
    console.log('تحويل الطلب إلى فاتورة:', order.id)

    // إنشاء فاتورة جديدة من بيانات طلب المبيعات
    const newInvoice = {
      ...order,
      id: Date.now().toString(),
      invoice_number: `INV-2024-${String(Date.now()).slice(-3)}`,
      type: 'invoice',
      status: 'draft',
      payment_method: 'cash',
      paid_amount: 0,
      remaining_amount: order.total_amount,
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 يوم من الآن
      created_at: new Date().toISOString().split('T')[0]
    }

    // في التطبيق الحقيقي، سيتم إرسال البيانات إلى API
    alert(`تم تحويل طلب المبيعات ${order.order_number} إلى فاتورة مبيعات ${newInvoice.invoice_number}`)
  }

  const handleConvertToQuotation = (order: any) => {
    console.log('تحويل الطلب إلى عرض سعر:', order.id)

    // إنشاء عرض سعر جديد من بيانات طلب المبيعات
    const newQuotation = {
      ...order,
      id: Date.now().toString(),
      quotation_number: `QUO-2024-${String(Date.now()).slice(-3)}`,
      type: 'quotation',
      status: 'draft',
      valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 يوم من الآن
      created_at: new Date().toISOString().split('T')[0]
    }

    // في التطبيق الحقيقي، سيتم إرسال البيانات إلى API
    alert(`تم تحويل طلب المبيعات ${order.order_number} إلى عرض سعر ${newQuotation.quotation_number}`)
  }

  const handleDownloadOrder = (orderId: string) => {
    console.log('تحميل الطلب:', orderId)
    alert('سيتم تحميل الطلب كملف PDF')
  }

  // حساب الإحصائيات
  const stats = {
    total: orders.length,
    confirmed: orders.filter(o => o.status === 'confirmed').length,
    processing: orders.filter(o => o.status === 'processing').length,
    shipped: orders.filter(o => o.status === 'shipped').length,
    delivered: orders.filter(o => o.status === 'delivered').length,
    totalValue: orders.reduce((sum, o) => sum + (o.total_amount || 0), 0),
    pendingPayment: orders.filter(o => o.payment_status === 'pending').length
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">طلبات المبيعات</h1>
            <p className="text-gray-600">إدارة طلبات العملاء ومتابعة التسليم</p>
          </div>
          {canCreate && (
            <Button onClick={handleCreateOrder}>
              <Plus className="h-4 w-4 mr-2" />
              طلب جديد
            </Button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-7 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الطلبات</CardTitle>
              <Package className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">مؤكدة</CardTitle>
              <CheckCircle className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.confirmed}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">قيد التحضير</CardTitle>
              <Package className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.processing}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">تم الشحن</CardTitle>
              <Truck className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.shipped}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">تم التسليم</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.delivered}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي القيمة</CardTitle>
              <Package className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">{formatCurrency(stats.totalValue)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">دفع معلق</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.pendingPayment}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في الطلبات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="حالة الطلب" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="draft">مسودة</SelectItem>
                    <SelectItem value="confirmed">مؤكد</SelectItem>
                    <SelectItem value="processing">قيد التحضير</SelectItem>
                    <SelectItem value="shipped">تم الشحن</SelectItem>
                    <SelectItem value="delivered">تم التسليم</SelectItem>
                    <SelectItem value="cancelled">ملغي</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="حالة الدفع" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع حالات الدفع</SelectItem>
                    <SelectItem value="pending">معلق</SelectItem>
                    <SelectItem value="partial">جزئي</SelectItem>
                    <SelectItem value="paid">مدفوع</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex space-x-2 space-x-reverse">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  مرشحات متقدمة
                </Button>
                <ExportData
                  data={filteredOrders}
                  filename="orders"
                  type="orders"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Orders Table */}
        <Card>
          <CardHeader>
            <CardTitle>قائمة الطلبات</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>رقم الطلب</TableHead>
                  <TableHead>العميل</TableHead>
                  <TableHead>التاريخ</TableHead>
                  <TableHead>تاريخ التسليم</TableHead>
                  <TableHead>المبلغ</TableHead>
                  <TableHead>حالة الطلب</TableHead>
                  <TableHead>حالة الدفع</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredOrders.map((order) => {
                  const statusInfo = getStatusInfo(order.status)
                  const paymentInfo = getPaymentStatusInfo(order.payment_status)
                  const StatusIcon = statusInfo.icon

                  return (
                    <TableRow key={order.id}>
                      <TableCell className="font-medium">
                        <button
                          onClick={() => handleViewOrder(order)}
                          className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer transition-colors duration-200"
                          title="انقر لعرض تفاصيل الطلب"
                        >
                          {order.order_number}
                        </button>
                      </TableCell>

                      <TableCell>
                        <div>
                          <div className="font-medium">{order.customer_name}</div>
                          <div className="text-xs text-gray-500">{order.customer_phone}</div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div>
                          <div>{formatDate(order.created_at)}</div>
                          <div className="text-xs text-gray-500">{order.created_by}</div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className={`text-sm ${
                          new Date(order.delivery_date) < new Date() && order.status !== 'delivered'
                            ? 'text-red-600' : 'text-gray-900'
                        }`}>
                          {formatDate(order.delivery_date)}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="text-right">
                          <div className="font-bold">{formatCurrency(order.total_amount)}</div>
                          <div className="text-xs text-gray-500">
                            {order.items.length} منتج
                          </div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusInfo.text}
                        </span>
                      </TableCell>

                      <TableCell>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${paymentInfo.color}`}>
                          {paymentInfo.text}
                        </span>
                      </TableCell>

                      <TableCell className="text-center">
                        <span className="text-sm text-gray-500">انقر على رقم الطلب للإجراءات</span>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Order Form */}
        <OrderForm
          isOpen={isFormOpen}
          onClose={() => {
            setIsFormOpen(false)
            setEditingOrder(null)
          }}
          order={editingOrder}
          onSave={handleSaveOrder}
        />

        {/* Order View Modal */}
        <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>تفاصيل طلب المبيعات {viewingOrder?.order_number}</span>
                <div className="flex flex-wrap gap-2">
                  {viewingOrder && (
                    <>
                      {/* طباعة */}
                      <OrderPrint order={viewingOrder} />

                      {/* تحميل PDF */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadOrder(viewingOrder.id)}
                        className="text-purple-600 hover:text-purple-700"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        تحميل PDF
                      </Button>

                      {/* إرسال بالبريد الإلكتروني */}
                      {viewingOrder.customer_email && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const subject = `طلب مبيعات رقم ${viewingOrder.order_number}`
                            const body = `السيد/ة ${viewingOrder.customer_name} المحترم/ة،%0A%0A` +
                                       `نؤكد استلام طلبكم التالي:%0A%0A` +
                                       `رقم الطلب: ${viewingOrder.order_number}%0A` +
                                       `تاريخ الطلب: ${formatDate(viewingOrder.created_at)}%0A` +
                                       `تاريخ التسليم المتوقع: ${formatDate(viewingOrder.delivery_date)}%0A` +
                                       `المبلغ الإجمالي: ${formatCurrency(viewingOrder.total_amount)}%0A%0A` +
                                       `سيتم التواصل معكم قريباً.%0A%0A` +
                                       `مع أطيب التحيات`

                            const mailtoUrl = `mailto:${viewingOrder.customer_email}?subject=${subject}&body=${body}`
                            window.open(mailtoUrl, '_blank')
                          }}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Mail className="h-4 w-4 mr-2" />
                          إيميل
                        </Button>
                      )}

                      {/* إرسال بالواتساب */}
                      {viewingOrder.customer_phone && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const message = `طلب مبيعات رقم ${viewingOrder.order_number}%0A` +
                                          `العميل: ${viewingOrder.customer_name}%0A` +
                                          `المبلغ الإجمالي: ${formatCurrency(viewingOrder.total_amount)}%0A` +
                                          `تاريخ التسليم: ${formatDate(viewingOrder.delivery_date)}`

                            const whatsappUrl = `https://wa.me/${viewingOrder.customer_phone?.replace(/[^0-9]/g, '')}?text=${message}`
                            window.open(whatsappUrl, '_blank')
                          }}
                          className="text-green-600 hover:text-green-700"
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          واتساب
                        </Button>
                      )}

                      {/* تعديل */}
                      {canEdit && viewingOrder.status !== 'delivered' && viewingOrder.status !== 'cancelled' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setEditingOrder(viewingOrder)
                            setIsViewModalOpen(false)
                            setIsFormOpen(true)
                          }}
                          className="text-orange-600 hover:text-orange-700"
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          تعديل
                        </Button>
                      )}

                      {/* حذف */}
                      {canDelete && viewingOrder.status === 'draft' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
                              handleDeleteOrder(viewingOrder.id)
                              setIsViewModalOpen(false)
                            }
                          }}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          حذف
                        </Button>
                      )}

                      {/* نسخ الطلب */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setEditingOrder({ ...viewingOrder, id: null, order_number: '', status: 'draft' })
                          setIsViewModalOpen(false)
                          setIsFormOpen(true)
                        }}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        نسخ الطلب
                      </Button>

                      {/* مشاركة الطلب */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          `,
                            text: `طلب مبيعات رقم ${viewingOrder.order_number} - العميل: ${viewingOrder.customer_name} - المبلغ: ${formatCurrency(viewingOrder.total_amount)}`,
                            url: window.location.href
                          }

                          if (navigator.share) {
                            navigator.share(shareData)
                          } else {
                            navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`)
                            alert('تم نسخ تفاصيل الطلب للحافظة')
                          }
                        }}
                        className="text-cyan-600 hover:text-cyan-700"
                      >
                        <Share2 className="h-4 w-4 mr-2" />
                        مشاركة
                      </Button>

                      {/* إضافة للمفضلة */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          alert('تم إضافة الطلب للمفضلة')
                        }}
                        className="text-yellow-600 hover:text-yellow-700"
                      >
                        <Star className="h-4 w-4 mr-2" />
                        مفضلة
                      </Button>

                      {/* إجراءات تحديث الحالة */}
                      <div className="w-full border-t my-2"></div>

                      {/* بدء التحضير */}
                      {viewingOrder.status === 'confirmed' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            handleUpdateStatus(viewingOrder.id, 'processing')
                            setViewingOrder({...viewingOrder, status: 'processing'})
                          }}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Package className="h-4 w-4 mr-2" />
                          بدء التحضير
                        </Button>
                      )}

                      {/* شحن */}
                      {viewingOrder.status === 'processing' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            handleUpdateStatus(viewingOrder.id, 'shipped')
                            setViewingOrder({...viewingOrder, status: 'shipped'})
                          }}
                          className="text-indigo-600 hover:text-indigo-700"
                        >
                          <Truck className="h-4 w-4 mr-2" />
                          شحن
                        </Button>
                      )}

                      {/* تأكيد التسليم */}
                      {viewingOrder.status === 'shipped' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            handleUpdateStatus(viewingOrder.id, 'delivered')
                            setViewingOrder({...viewingOrder, status: 'delivered'})
                          }}
                          className="text-green-600 hover:text-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          تأكيد التسليم
                        </Button>
                      )}

                      {/* فاصل للتحويلات */}
                      <div className="w-full border-t my-2"></div>

                      {/* تحويل إلى عرض سعر */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleConvertToQuotation(viewingOrder)
                          setIsViewModalOpen(false)
                        }}
                        className="text-indigo-600 hover:text-indigo-700"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        تحويل لعرض سعر
                      </Button>

                      {/* تحويل إلى فاتورة */}
                      {(viewingOrder.status === 'confirmed' || viewingOrder.status === 'processing') && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            handleConvertToInvoice(viewingOrder)
                            setIsViewModalOpen(false)
                          }}
                          className="text-emerald-600 hover:text-emerald-700"
                        >
                          <ArrowRight className="h-4 w-4 mr-2" />
                          تحويل لفاتورة
                        </Button>
                      )}
                    </>
                  )}
                </div>
              </DialogTitle>
            </DialogHeader>

            {viewingOrder && (
              <div className="space-y-6">
                {/* معلومات الطلب الأساسية */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">معلومات الطلب</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">رقم الطلب:</span>
                        <span className="font-medium">{viewingOrder.order_number}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">تاريخ الطلب:</span>
                        <span>{formatDate(viewingOrder.created_at)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">تاريخ التسليم:</span>
                        <span>{formatDate(viewingOrder.delivery_date)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">حالة الطلب:</span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusInfo(viewingOrder.status).color}`}>
                          {getStatusInfo(viewingOrder.status).text}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">حالة الدفع:</span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPaymentStatusInfo(viewingOrder.payment_status).color}`}>
                          {getPaymentStatusInfo(viewingOrder.payment_status).text}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">المسؤول:</span>
                        <span>{viewingOrder.created_by || 'غير محدد'}</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">معلومات العميل</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">اسم العميل:</span>
                        <span className="font-medium">{viewingOrder.customer_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">رقم الهاتف:</span>
                        <span>{viewingOrder.customer_phone}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">البريد الإلكتروني:</span>
                        <span>{viewingOrder.customer_email || 'غير محدد'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">عنوان التسليم:</span>
                        <span>{viewingOrder.delivery_address || 'غير محدد'}</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* المنتجات */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">المنتجات</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>المنتج</TableHead>
                          <TableHead>الكمية</TableHead>
                          <TableHead>السعر</TableHead>
                          <TableHead>الإجمالي</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {viewingOrder.items?.map((item: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              <div>
                                <div>{item.product_name}</div>
                                {item.selected_components && item.selected_components.length > 0 && (
                                  <div className="text-xs text-gray-500 mt-1">
                                    {item.selected_components.map((comp: any, i: number) => (
                                      <div key={i}>+ {comp.name}</div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>{item.quantity}</TableCell>
                            <TableCell>{formatCurrency(item.unit_price)}</TableCell>
                            <TableCell>{formatCurrency(item.total_price)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                {/* ملخص المبالغ */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">ملخص المبالغ</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">المجموع الفرعي:</span>
                        <span>{formatCurrency(viewingOrder.subtotal)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">الضريبة:</span>
                        <span>{formatCurrency(viewingOrder.tax_amount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">الخصم:</span>
                        <span>{formatCurrency(viewingOrder.discount_amount)}</span>
                      </div>
                      <div className="border-t pt-3">
                        <div className="flex justify-between text-lg font-bold">
                          <span>المجموع الكلي:</span>
                          <span>{formatCurrency(viewingOrder.total_amount)}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* ملاحظات */}
                {viewingOrder.notes && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">ملاحظات</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700">{viewingOrder.notes}</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  )
}
