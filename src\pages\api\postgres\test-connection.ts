import { NextApiRequest, NextApiResponse } from 'next'
import { testConnection, checkTables } from '@/lib/postgres'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // اختبار الاتصال
    const connectionTest = await testConnection()
    
    if (!connectionTest.success) {
      return res.status(500).json({
        success: false,
        message: 'فشل في الاتصال بقاعدة البيانات',
        error: connectionTest.error
      })
    }

    // فحص الجداول
    const tablesCheck = await checkTables()

    return res.status(200).json({
      success: true,
      message: 'تم الاتصال بقاعدة البيانات بنجاح',
      connection: {
        connected: true,
        time: connectionTest.time
      },
      database: {
        name: process.env.DB_NAME,
        host: process.env.DB_HOST,
        port: process.env.DB_PORT
      },
      tables: tablesCheck.success ? {
        available: tablesCheck.tables,
        hasBranches: tablesCheck.hasBranches,
        hasUsers: tablesCheck.hasUsers,
        hasProducts: tablesCheck.hasProducts,
        hasWarehouses: tablesCheck.hasWarehouses,
        needsSetup: tablesCheck.needsSetup
      } : {
        error: tablesCheck.error,
        needsSetup: true
      }
    })

  } catch (error) {
    console.error('خطأ في اختبار الاتصال:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
