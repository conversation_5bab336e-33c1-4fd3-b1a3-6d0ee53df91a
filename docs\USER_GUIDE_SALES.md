# 📊 دليل استخدام موديول المبيعات

## 🎯 **نظرة عامة**
موديول المبيعات هو نظام متكامل لإدارة جميع عمليات البيع من عروض الأسعار حتى الفواتير والمرتجعات، مع تكامل كامل مع المخزون والمحاسبة.

---

## 📋 **المكونات الرئيسية**

### 1. **عروض الأسعار (Quotations)**
### 2. **أوامر البيع (Sales Orders)**  
### 3. **فواتير المبيعات (Sales Invoices)**
### 4. **مرتجعات المبيعات (Sales Returns)**

---

## 💰 **1. عروض الأسعار (Quotations)**

### 📍 **الوصول**: `/sales/quotations`

### ✨ **الميزات الرئيسية**
- إنشاء عروض أسعار احترافية
- تحويل العروض إلى أوامر بيع
- تتبع حالة العروض (مسودة، مرسل، مقبول، مرفوض)
- طباعة وتصدير العروض

### 🔧 **كيفية الاستخدام**

#### **إنشاء عرض سعر جديد**
1. انقر على "عرض سعر جديد"
2. **بيانات العميل**:
   - اختر العميل من القائمة أو أدخل بيانات جديدة
   - سيتم ملء البيانات تلقائياً (الاسم، الهاتف، العنوان)
3. **بيانات العرض**:
   - تاريخ العرض (تلقائي)
   - تاريخ انتهاء الصلاحية
   - الفرع والمخزن
4. **إضافة المنتجات**:
   - انقر "إضافة منتج"
   - اختر المنتج من القائمة
   - حدد الكمية (سيتم حساب السعر تلقائياً)
   - يمكن تعديل السعر حسب الحاجة
5. **الخصومات والضرائب**:
   - خصم على كل منتج منفرد
   - خصم إجمالي على الفاتورة
   - ضريبة لكل منتج
6. **حفظ العرض**

#### **إدارة عروض الأسعار**
- **عرض القائمة**: جميع العروض مع الفلترة والبحث
- **تعديل العرض**: تحديث البيانات والمنتجات
- **تغيير الحالة**: مسودة → مرسل → مقبول/مرفوض
- **تحويل لأمر بيع**: عند قبول العرض
- **طباعة/تصدير**: PDF أو Excel

#### **حالات عروض الأسعار**
- 📝 **مسودة**: قيد التحضير
- 📤 **مرسل**: تم إرساله للعميل
- ✅ **مقبول**: وافق العميل
- ❌ **مرفوض**: رفض العميل
- ⏰ **منتهي الصلاحية**: تجاوز تاريخ الانتهاء

---

## 📦 **2. أوامر البيع (Sales Orders)**

### 📍 **الوصول**: `/sales/orders`

### ✨ **الميزات الرئيسية**
- تحويل عروض الأسعار إلى أوامر
- حجز المخزون تلقائياً
- تتبع حالة التنفيذ
- تحويل الأوامر إلى فواتير

### 🔧 **كيفية الاستخدام**

#### **إنشاء أمر بيع جديد**
1. **من عرض سعر**: انقر "تحويل لأمر بيع"
2. **إنشاء مباشر**: انقر "أمر بيع جديد"
3. **بيانات الأمر**:
   - بيانات العميل
   - تاريخ الأمر
   - تاريخ التسليم المتوقع
   - الفرع والمخزن
4. **المنتجات والكميات**
5. **تأكيد الأمر**

#### **حجز المخزون**
- ✅ يتم حجز الكميات تلقائياً عند تأكيد الأمر
- 📊 تظهر الكميات المحجوزة في تقارير المخزون
- 🔄 يمكن إلغاء الحجز عند إلغاء الأمر

#### **حالات أوامر البيع**
- 📝 **مسودة**: قيد التحضير
- ✅ **مؤكد**: تم تأكيد الأمر وحجز المخزون
- 🚚 **قيد التنفيذ**: جارٍ تحضير الطلب
- 📋 **جاهز للفوترة**: تم التحضير
- ✅ **مكتمل**: تم إنشاء الفاتورة

---

## 🧾 **3. فواتير المبيعات (Sales Invoices)**

### 📍 **الوصول**: `/sales/invoices`

### ✨ **الميزات الرئيسية**
- إنشاء فواتير من أوامر البيع
- خصم المخزون تلقائياً
- تكامل مع المحاسبة
- طرق دفع متعددة

### 🔧 **كيفية الاستخدام**

#### **إنشاء فاتورة جديدة**
1. **من أمر بيع**: انقر "إنشاء فاتورة"
2. **إنشاء مباشر**: انقر "فاتورة جديدة"
3. **بيانات الفاتورة**:
   - بيانات العميل
   - تاريخ الفاتورة
   - طريقة الدفع (نقدي، آجل، بنكي، إلكتروني)
   - الفرع والمخزن
4. **المنتجات والأسعار**
5. **الخصومات والضرائب**
6. **إجمالي الفاتورة**
7. **حفظ وطباعة**

#### **التأثير على الأنظمة الأخرى**
- 📦 **المخزون**: خصم الكميات تلقائياً
- 💰 **المحاسبة**: إضافة معاملة في كشف العميل
- 💳 **التدفق النقدي**: تسجيل الإيراد
- 📊 **التقارير**: تحديث إحصائيات المبيعات

#### **طرق الدفع**
- 💵 **نقدي**: دفع فوري
- 📅 **آجل**: إضافة للذمم المدينة
- 🏦 **بنكي**: تحويل بنكي
- 💳 **إلكتروني**: دفع إلكتروني

---

## 🔄 **4. مرتجعات المبيعات (Sales Returns)**

### 📍 **الوصول**: `/sales/returns`

### ✨ **الميزات الرئيسية**
- ربط بفواتير المبيعات
- إرجاع جزئي أو كامل
- إعادة المخزون تلقائياً
- تعديل كشف حساب العميل

### 🔧 **كيفية الاستخدام**

#### **إنشاء مرتجع جديد**
1. انقر "مرتجع جديد"
2. **اختيار الفاتورة**:
   - ابحث برقم الفاتورة أو اسم العميل
   - اختر الفاتورة المراد الإرجاع منها
3. **عرض بيانات الفاتورة**:
   - تظهر جميع المنتجات والكميات
   - أسعار البيع الأصلية
4. **تحديد المنتجات المرتجعة**:
   - اختر المنتجات المراد إرجاعها
   - حدد الكمية المرتجعة (لا تتجاوز المباعة)
   - سبب الإرجاع
5. **تأكيد المرتجع**

#### **التأثير على الأنظمة**
- 📦 **المخزون**: إعادة الكميات المرتجعة
- 💰 **المحاسبة**: تعديل كشف حساب العميل
- 💳 **التدفق النقدي**: تسجيل المرتجع
- 📊 **التقارير**: تحديث إحصائيات المرتجعات

---

## 📊 **التقارير والإحصائيات**

### 📈 **تقارير المبيعات**
- إجمالي المبيعات (يومي، شهري، سنوي)
- أفضل المنتجات مبيعاً
- أفضل العملاء
- تحليل الاتجاهات

### 📋 **تقارير العروض والأوامر**
- معدل تحويل العروض لأوامر
- متوسط قيمة الأمر
- أوامر قيد التنفيذ

### 🔄 **تقارير المرتجعات**
- نسبة المرتجعات
- أسباب الإرجاع الأكثر شيوعاً
- المنتجات الأكثر إرجاعاً

---

## 🎯 **نصائح للاستخدام الأمثل**

### ✅ **أفضل الممارسات**
1. **ابدأ بعروض الأسعار** للعملاء الجدد
2. **استخدم أوامر البيع** لحجز المخزون
3. **تأكد من البيانات** قبل إنشاء الفواتير
4. **راجع المرتجعات** بانتظام لتحسين الجودة

### ⚠️ **تجنب هذه الأخطاء**
- عدم التحقق من توفر المخزون
- إنشاء فواتير بدون أوامر للكميات الكبيرة
- عدم تسجيل أسباب المرتجعات
- تجاهل تواريخ انتهاء صلاحية العروض

### 🔧 **اختصارات مفيدة**
- **Ctrl + N**: إنشاء جديد
- **Ctrl + S**: حفظ
- **Ctrl + P**: طباعة
- **F5**: تحديث القائمة

---

## 🔗 **الروابط السريعة**

- **عروض الأسعار**: `/sales/quotations`
- **أوامر البيع**: `/sales/orders`
- **فواتير المبيعات**: `/sales/invoices`
- **مرتجعات المبيعات**: `/sales/returns`
- **تقارير المبيعات**: `/sales/reports`

---

## 🆘 **الدعم والمساعدة**

في حالة وجود أي مشاكل أو استفسارات:
1. راجع هذا الدليل أولاً
2. تحقق من صحة البيانات المدخلة
3. تأكد من الصلاحيات المطلوبة
4. تواصل مع فريق الدعم الفني

---

**💡 ملاحظة**: جميع العمليات في موديول المبيعات مترابطة ومتكاملة مع باقي أنظمة ERP لضمان دقة البيانات وسلاسة العمل.
