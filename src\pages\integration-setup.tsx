import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Progress } from '@/components/ui/progress'
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Database, 
  Zap,
  Code,
  Layers,
  Users,
  Package,
  Warehouse
} from 'lucide-react'

export default function IntegrationSetup() {
  const [setupResult, setSetupResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [progress, setProgress] = useState(0)

  const setupIntegration = async () => {
    setLoading(true)
    setSetupResult(null)
    setProgress(0)
    
    try {
      // محاكاة التقدم
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90))
      }, 200)

      const response = await fetch('/api/postgres/setup-integrated', {
        method: 'POST'
      })
      
      const data = await response.json()
      
      clearInterval(progressInterval)
      setProgress(100)
      setSetupResult(data)
      
    } catch (err) {
      setSetupResult({
        success: false,
        message: 'خطأ في تنفيذ التكامل',
        error: err instanceof Error ? err.message : 'Unknown error'
      })
      setProgress(0)
    } finally {
      setLoading(false)
    }
  }

  const features = [
    {
      icon: <Database className="h-8 w-8 text-blue-600" />,
      title: 'مخطط قاعدة البيانات المتكامل',
      description: 'جداول متوافقة 100% مع الكود الموجود'
    },
    {
      icon: <Code className="h-8 w-8 text-green-600" />,
      title: 'خدمات API متطابقة',
      description: 'واجهات برمجية متناغمة مع النماذج'
    },
    {
      icon: <Layers className="h-8 w-8 text-purple-600" />,
      title: 'أنواع بيانات موحدة',
      description: 'ENUMs وأنواع مخصصة متوافقة'
    },
    {
      icon: <Users className="h-8 w-8 text-orange-600" />,
      title: 'إدارة المستخدمين',
      description: 'نظام مستخدمين وصلاحيات كامل'
    },
    {
      icon: <Package className="h-8 w-8 text-red-600" />,
      title: 'إدارة المنتجات',
      description: 'منتجات ومخزون متكامل'
    },
    {
      icon: <Warehouse className="h-8 w-8 text-teal-600" />,
      title: 'نظام المخازن',
      description: 'إدارة مخازن وحركات مخزون'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-white p-4 rounded-full shadow-lg">
              <Zap className="h-12 w-12 text-blue-600" />
            </div>
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            التكامل الكامل بين الكود وقاعدة البيانات
          </h1>
          <p className="text-xl text-gray-600">
            إعداد متكامل يضمن التناغم التام بين جميع مكونات النظام
          </p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="bg-white shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  {feature.icon}
                </div>
                <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                <p className="text-gray-600 text-sm">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Setup Section */}
        <Card className="bg-white shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">بدء التكامل</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {!setupResult && !loading && (
              <div className="text-center space-y-4">
                <p className="text-gray-600">
                  سيتم إنشاء مخطط قاعدة البيانات المتكامل مع جميع الجداول والأنواع المطلوبة
                </p>
                <Button 
                  onClick={setupIntegration}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3"
                >
                  <Zap className="h-5 w-5 mr-2" />
                  بدء التكامل الكامل
                </Button>
              </div>
            )}

            {loading && (
              <div className="space-y-4">
                <div className="text-center">
                  <Database className="h-12 w-12 mx-auto text-blue-600 animate-pulse mb-4" />
                  <h3 className="text-lg font-semibold">جاري إعداد التكامل...</h3>
                  <p className="text-gray-600">يرجى الانتظار حتى اكتمال العملية</p>
                </div>
                <Progress value={progress} className="w-full" />
                <p className="text-center text-sm text-gray-500">{progress}% مكتمل</p>
              </div>
            )}

            {setupResult && (
              <Alert className={setupResult.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                {setupResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription>
                  <div className="space-y-4">
                    <p className="font-medium text-lg">{setupResult.message}</p>
                    
                    {setupResult.summary && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-blue-600">{setupResult.summary.totalStatements}</p>
                          <p className="text-gray-600">إجمالي الاستعلامات</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-green-600">{setupResult.summary.successful}</p>
                          <p className="text-gray-600">نجح</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-red-600">{setupResult.summary.failed}</p>
                          <p className="text-gray-600">فشل</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-purple-600">{setupResult.summary.tablesCreated}</p>
                          <p className="text-gray-600">جداول</p>
                        </div>
                      </div>
                    )}

                    {setupResult.database?.coreTablesReady && (
                      <div className="bg-green-100 p-4 rounded-lg">
                        <h4 className="font-semibold text-green-800 mb-2">✅ الجداول الأساسية جاهزة</h4>
                        <div className="flex flex-wrap gap-2">
                          {setupResult.database.tables?.map((table: string) => (
                            <span key={table} className="bg-green-200 text-green-800 px-2 py-1 rounded text-xs">
                              {table}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}

                    {setupResult.recommendations && (
                      <div className="space-y-2">
                        <h4 className="font-semibold">الخطوات التالية:</h4>
                        <ul className="space-y-1">
                          {setupResult.recommendations.map((rec: string, index: number) => (
                            <li key={index} className="text-sm">{rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="flex gap-4 pt-4">
                      <Button 
                        onClick={() => window.location.href = '/database-status'}
                        variant="outline"
                      >
                        فحص حالة قاعدة البيانات
                      </Button>
                      <Button 
                        onClick={() => window.location.href = '/'}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        الذهاب للنظام
                      </Button>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
