import { NextApiRequest, NextApiResponse } from 'next'
import { createUser, findUser } from '@/lib/postgres'
import { ROLE_PERMISSIONS } from '@/lib/auth'
import bcrypt from 'bcryptjs'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { email, password, fullName } = req.body

    if (!email || !password || !fullName) {
      return res.status(400).json({
        success: false,
        message: 'جميع الحقول مطلوبة'
      })
    }

    // التحقق من عدم وجود المستخدم مسبقاً
    const existingUser = await findUser(email)

    if (existingUser.success && existingUser.user) {
      return res.status(400).json({
        success: false,
        message: 'المستخدم موجود مسبقاً'
      })
    }

    // تشفير كلمة المرور
    const saltRounds = 10
    const passwordHash = await bcrypt.hash(password, saltRounds)

    // إنشاء المستخدم
    const result = await createUser({
      email,
      username: email.split('@')[0],
      full_name: fullName,
      password_hash: passwordHash,
      role: 'admin'
    })

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: 'فشل في إنشاء المستخدم',
        error: result.error
      })
    }

    const permissions = ROLE_PERMISSIONS[result.user?.role as keyof typeof ROLE_PERMISSIONS] || []

    return res.status(200).json({
      success: true,
      message: 'تم إنشاء المدير الأولي بنجاح',
      user: {
        id: result.user?.id,
        email: result.user?.email,
        username: result.user?.username,
        full_name: result.user?.full_name,
        role: result.user?.role,
        permissions
      }
    })

  } catch (error) {
    console.error('Error creating initial admin:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
