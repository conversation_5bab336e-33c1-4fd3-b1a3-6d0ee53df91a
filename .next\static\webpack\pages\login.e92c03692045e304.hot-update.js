"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/login",{

/***/ "./src/lib/database-setup.ts":
/*!***********************************!*\
  !*** ./src/lib/database-setup.ts ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkInitialData: function() { return /* binding */ checkInitialData; },\n/* harmony export */   createInitialAdmin: function() { return /* binding */ createInitialAdmin; },\n/* harmony export */   setupInitialData: function() { return /* binding */ setupInitialData; }\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"./src/lib/supabase.ts\");\n// إعداد قاعدة البيانات والبيانات الأولية\n\n// إنشاء البيانات الأولية للنظام\nconst setupInitialData = async ()=>{\n    try {\n        console.log(\"بدء إعداد البيانات الأولية...\");\n        // 1. إنشاء الفرع الرئيسي\n        const { data: branch, error: branchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"branches\").insert({\n            name: \"الفرع الرئيسي\",\n            address: \"العنوان الرئيسي للشركة\",\n            phone: \"+201234567890\",\n            email: \"<EMAIL>\",\n            is_active: true\n        }).select().single();\n        if (branchError && branchError.code !== \"23505\") {\n            throw branchError;\n        }\n        console.log(\"تم إنشاء الفرع الرئيسي\");\n        // 2. إنشاء المخزن الرئيسي\n        const { data: warehouse, error: warehouseError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"warehouses\").insert({\n            name: \"المخزن الرئيسي\",\n            location: \"الموقع الرئيسي للمخزن\",\n            branch_id: branch === null || branch === void 0 ? void 0 : branch.id,\n            is_active: true\n        }).select().single();\n        if (warehouseError && warehouseError.code !== \"23505\") {\n            throw warehouseError;\n        }\n        console.log(\"تم إنشاء المخزن الرئيسي\");\n        // 3. إنشاء صندوق النقدية الرئيسي\n        const { data: cashRegister, error: cashRegisterError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"cash_registers\").insert({\n            name: \"الصندوق الرئيسي\",\n            branch_id: branch === null || branch === void 0 ? void 0 : branch.id,\n            current_balance: 0,\n            is_active: true\n        }).select().single();\n        if (cashRegisterError && cashRegisterError.code !== \"23505\") {\n            throw cashRegisterError;\n        }\n        console.log(\"تم إنشاء الصندوق الرئيسي\");\n        // 4. إنشاء بعض المنتجات التجريبية\n        const products = [\n            {\n                name: \"لابتوب HP EliteBook 840\",\n                sku: \"HP-EB-840\",\n                description: \"لابتوب HP EliteBook 840 G8 - Intel Core i7\",\n                category: \"أجهزة كمبيوتر\",\n                unit_price: 25000,\n                cost_price: 20000,\n                stock_quantity: 0,\n                min_stock_level: 5,\n                is_active: true\n            },\n            {\n                name: \"ذاكرة RAM 16GB DDR4\",\n                sku: \"RAM-16GB-DDR4\",\n                description: \"ذاكرة RAM DDR4 16GB 3200MHz\",\n                category: \"قطع غيار\",\n                unit_price: 1500,\n                cost_price: 1200,\n                stock_quantity: 0,\n                min_stock_level: 10,\n                is_active: true\n            },\n            {\n                name: \"قرص صلب SSD 512GB\",\n                sku: \"SSD-512GB\",\n                description: \"قرص صلب SSD 512GB SATA\",\n                category: \"قطع غيار\",\n                unit_price: 2000,\n                cost_price: 1600,\n                stock_quantity: 0,\n                min_stock_level: 8,\n                is_active: true\n            }\n        ];\n        for (const product of products){\n            const { data: createdProduct, error: productError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"products\").insert(product).select().single();\n            if (productError && productError.code !== \"23505\") {\n                console.error(\"خطأ في إنشاء المنتج:\", product.name, productError);\n                continue;\n            }\n            // إنشاء سجل مخزون للمنتج\n            if (createdProduct && warehouse) {\n                await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"inventory\").insert({\n                    product_id: createdProduct.id,\n                    warehouse_id: warehouse.id,\n                    total_stock: 0,\n                    available_stock: 0,\n                    reserved_stock: 0,\n                    min_stock_level: product.min_stock_level\n                });\n            }\n        }\n        console.log(\"تم إنشاء المنتجات التجريبية\");\n        // 5. إنشاء بعض العملاء التجريبيين\n        const customers = [\n            {\n                name: \"أحمد محمد علي\",\n                email: \"<EMAIL>\",\n                phone: \"+201234567890\",\n                address: \"القاهرة، مصر\",\n                credit_limit: 10000,\n                current_balance: 0,\n                is_active: true\n            },\n            {\n                name: \"فاطمة أحمد\",\n                email: \"<EMAIL>\",\n                phone: \"+201234567891\",\n                address: \"الجيزة، مصر\",\n                credit_limit: 15000,\n                current_balance: 0,\n                is_active: true\n            }\n        ];\n        for (const customer of customers){\n            const { error: customerError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"customers\").insert(customer);\n            if (customerError && customerError.code !== \"23505\") {\n                console.error(\"خطأ في إنشاء العميل:\", customer.name, customerError);\n            }\n        }\n        console.log(\"تم إنشاء العملاء التجريبيين\");\n        // 6. إنشاء بعض الموردين التجريبيين\n        const suppliers = [\n            {\n                name: \"شركة التقنية المتقدمة\",\n                email: \"<EMAIL>\",\n                phone: \"+201234567892\",\n                address: \"القاهرة الجديدة، مصر\",\n                is_active: true\n            },\n            {\n                name: \"مؤسسة الحاسوب الحديث\",\n                email: \"<EMAIL>\",\n                phone: \"+201234567893\",\n                address: \"الإسكندرية، مصر\",\n                is_active: true\n            }\n        ];\n        for (const supplier of suppliers){\n            const { error: supplierError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"suppliers\").insert(supplier);\n            if (supplierError && supplierError.code !== \"23505\") {\n                console.error(\"خطأ في إنشاء المورد:\", supplier.name, supplierError);\n            }\n        }\n        console.log(\"تم إنشاء الموردين التجريبيين\");\n        console.log(\"تم إعداد البيانات الأولية بنجاح!\");\n        return {\n            success: true,\n            message: \"تم إعداد البيانات الأولية بنجاح\",\n            data: {\n                branch: branch === null || branch === void 0 ? void 0 : branch.id,\n                warehouse: warehouse === null || warehouse === void 0 ? void 0 : warehouse.id,\n                cashRegister: cashRegister === null || cashRegister === void 0 ? void 0 : cashRegister.id\n            }\n        };\n    } catch (error) {\n        console.error(\"خطأ في إعداد البيانات الأولية:\", error);\n        return {\n            success: false,\n            message: \"فشل في إعداد البيانات الأولية\",\n            error\n        };\n    }\n};\n// التحقق من وجود البيانات الأولية\nconst checkInitialData = async ()=>{\n    try {\n        // التحقق من صحة متغيرات البيئة أولاً\n        const supabaseUrl = \"https://your-project-id.supabase.co\";\n        const supabaseKey = \"your-anon-key-here\";\n        if (!supabaseUrl || !supabaseKey || supabaseUrl.includes(\"your-project-id\") || supabaseKey.includes(\"your-anon-key\")) {\n            console.warn(\"متغيرات البيئة غير مُعدة بشكل صحيح\");\n            return {\n                hasBranches: false,\n                hasWarehouses: false,\n                hasProducts: false,\n                needsSetup: true,\n                configError: true\n            };\n        }\n        const { data: branches, error: branchError } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"branches\").select(\"id\").limit(1);\n        if (branchError) {\n            console.error(\"خطأ في الاتصال بقاعدة البيانات:\", branchError);\n            throw branchError;\n        }\n        const { data: warehouses } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"warehouses\").select(\"id\").limit(1);\n        const { data: products } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from(\"products\").select(\"id\").limit(1);\n        return {\n            hasBranches: ((branches === null || branches === void 0 ? void 0 : branches.length) || 0) > 0,\n            hasWarehouses: ((warehouses === null || warehouses === void 0 ? void 0 : warehouses.length) || 0) > 0,\n            hasProducts: ((products === null || products === void 0 ? void 0 : products.length) || 0) > 0,\n            needsSetup: ((branches === null || branches === void 0 ? void 0 : branches.length) || 0) === 0\n        };\n    } catch (error) {\n        console.error(\"خطأ في التحقق من البيانات الأولية:\", error);\n        return {\n            hasBranches: false,\n            hasWarehouses: false,\n            hasProducts: false,\n            needsSetup: true,\n            error\n        };\n    }\n};\n// إنشاء مستخدم مدير أولي (يتم استدعاؤها من API endpoint)\nconst createInitialAdmin = async (email, password, fullName)=>{\n    try {\n        // استدعاء API endpoint لإنشاء المدير\n        const response = await fetch(\"/api/setup/create-admin\", {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                email,\n                password,\n                fullName\n            })\n        });\n        const result = await response.json();\n        if (!response.ok) {\n            throw new Error(result.message || \"فشل في إنشاء المدير الأولي\");\n        }\n        return result;\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المدير الأولي:\", error);\n        return {\n            success: false,\n            message: error instanceof Error ? error.message : \"فشل في إنشاء المدير الأولي\",\n            error\n        };\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/database-setup.ts\n"));

/***/ })

});