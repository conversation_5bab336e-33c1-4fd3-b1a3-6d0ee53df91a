import { NextApiRequest, NextApiResponse } from 'next'
import { findUser } from '@/lib/postgres'
import bcrypt from 'bcryptjs'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { email, password } = req.body

    if (!email || !password) {
      return res.status(400).json({ 
        success: false,
        message: 'البريد الإلكتروني وكلمة المرور مطلوبان' 
      })
    }

    // البحث عن المستخدم
    const userResult = await findUser(email)
    
    if (!userResult.success || !userResult.user) {
      return res.status(401).json({
        success: false,
        message: 'بيانات تسجيل الدخول غير صحيحة'
      })
    }

    const user = userResult.user

    // التحقق من كلمة المرور
    const isPasswordValid = await bcrypt.compare(password, user.password_hash)
    
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: 'بيانات تسجيل الدخول غير صحيحة'
      })
    }

    // تسجيل دخول ناجح
    return res.status(200).json({
      success: true,
      message: 'تم تسجيل الدخول بنجاح',
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        full_name: user.full_name,
        role: user.role,
        is_active: user.is_active
      }
    })

  } catch (error) {
    console.error('Error during login:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
