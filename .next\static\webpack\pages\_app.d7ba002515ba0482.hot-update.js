"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./src/styles/globals.css":
/*!**************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./src/styles/globals.css ***!
  \**************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \"*, ::before, ::after {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}\\n\\n::backdrop {\\n  --tw-border-spacing-x: 0;\\n  --tw-border-spacing-y: 0;\\n  --tw-translate-x: 0;\\n  --tw-translate-y: 0;\\n  --tw-rotate: 0;\\n  --tw-skew-x: 0;\\n  --tw-skew-y: 0;\\n  --tw-scale-x: 1;\\n  --tw-scale-y: 1;\\n  --tw-pan-x:  ;\\n  --tw-pan-y:  ;\\n  --tw-pinch-zoom:  ;\\n  --tw-scroll-snap-strictness: proximity;\\n  --tw-gradient-from-position:  ;\\n  --tw-gradient-via-position:  ;\\n  --tw-gradient-to-position:  ;\\n  --tw-ordinal:  ;\\n  --tw-slashed-zero:  ;\\n  --tw-numeric-figure:  ;\\n  --tw-numeric-spacing:  ;\\n  --tw-numeric-fraction:  ;\\n  --tw-ring-inset:  ;\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: rgb(59 130 246 / 0.5);\\n  --tw-ring-offset-shadow: 0 0 #0000;\\n  --tw-ring-shadow: 0 0 #0000;\\n  --tw-shadow: 0 0 #0000;\\n  --tw-shadow-colored: 0 0 #0000;\\n  --tw-blur:  ;\\n  --tw-brightness:  ;\\n  --tw-contrast:  ;\\n  --tw-grayscale:  ;\\n  --tw-hue-rotate:  ;\\n  --tw-invert:  ;\\n  --tw-saturate:  ;\\n  --tw-sepia:  ;\\n  --tw-drop-shadow:  ;\\n  --tw-backdrop-blur:  ;\\n  --tw-backdrop-brightness:  ;\\n  --tw-backdrop-contrast:  ;\\n  --tw-backdrop-grayscale:  ;\\n  --tw-backdrop-hue-rotate:  ;\\n  --tw-backdrop-invert:  ;\\n  --tw-backdrop-opacity:  ;\\n  --tw-backdrop-saturate:  ;\\n  --tw-backdrop-sepia:  ;\\n  --tw-contain-size:  ;\\n  --tw-contain-layout:  ;\\n  --tw-contain-paint:  ;\\n  --tw-contain-style:  ;\\n}/*\\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\\n*//*\\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\\n*/\\n\\n*,\\n::before,\\n::after {\\n  box-sizing: border-box; /* 1 */\\n  border-width: 0; /* 2 */\\n  border-style: solid; /* 2 */\\n  border-color: #e5e7eb; /* 2 */\\n}\\n\\n::before,\\n::after {\\n  --tw-content: '';\\n}\\n\\n/*\\n1. Use a consistent sensible line-height in all browsers.\\n2. Prevent adjustments of font size after orientation changes in iOS.\\n3. Use a more readable tab size.\\n4. Use the user's configured `sans` font-family by default.\\n5. Use the user's configured `sans` font-feature-settings by default.\\n6. Use the user's configured `sans` font-variation-settings by default.\\n7. Disable tap highlights on iOS\\n*/\\n\\nhtml,\\n:host {\\n  line-height: 1.5; /* 1 */\\n  -webkit-text-size-adjust: 100%; /* 2 */\\n  -moz-tab-size: 4; /* 3 */\\n  -o-tab-size: 4;\\n     tab-size: 4; /* 3 */\\n  font-family: ui-sans-serif, system-ui, sans-serif, \\\"Apple Color Emoji\\\", \\\"Segoe UI Emoji\\\", \\\"Segoe UI Symbol\\\", \\\"Noto Color Emoji\\\"; /* 4 */\\n  font-feature-settings: normal; /* 5 */\\n  font-variation-settings: normal; /* 6 */\\n  -webkit-tap-highlight-color: transparent; /* 7 */\\n}\\n\\n/*\\n1. Remove the margin in all browsers.\\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\\n*/\\n\\nbody {\\n  margin: 0; /* 1 */\\n  line-height: inherit; /* 2 */\\n}\\n\\n/*\\n1. Add the correct height in Firefox.\\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\\n3. Ensure horizontal rules are visible by default.\\n*/\\n\\nhr {\\n  height: 0; /* 1 */\\n  color: inherit; /* 2 */\\n  border-top-width: 1px; /* 3 */\\n}\\n\\n/*\\nAdd the correct text decoration in Chrome, Edge, and Safari.\\n*/\\n\\nabbr:where([title]) {\\n  -webkit-text-decoration: underline dotted;\\n          text-decoration: underline dotted;\\n}\\n\\n/*\\nRemove the default font size and weight for headings.\\n*/\\n\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n  font-size: inherit;\\n  font-weight: inherit;\\n}\\n\\n/*\\nReset links to optimize for opt-in styling instead of opt-out.\\n*/\\n\\na {\\n  color: inherit;\\n  text-decoration: inherit;\\n}\\n\\n/*\\nAdd the correct font weight in Edge and Safari.\\n*/\\n\\nb,\\nstrong {\\n  font-weight: bolder;\\n}\\n\\n/*\\n1. Use the user's configured `mono` font-family by default.\\n2. Use the user's configured `mono` font-feature-settings by default.\\n3. Use the user's configured `mono` font-variation-settings by default.\\n4. Correct the odd `em` font sizing in all browsers.\\n*/\\n\\ncode,\\nkbd,\\nsamp,\\npre {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace; /* 1 */\\n  font-feature-settings: normal; /* 2 */\\n  font-variation-settings: normal; /* 3 */\\n  font-size: 1em; /* 4 */\\n}\\n\\n/*\\nAdd the correct font size in all browsers.\\n*/\\n\\nsmall {\\n  font-size: 80%;\\n}\\n\\n/*\\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\\n*/\\n\\nsub,\\nsup {\\n  font-size: 75%;\\n  line-height: 0;\\n  position: relative;\\n  vertical-align: baseline;\\n}\\n\\nsub {\\n  bottom: -0.25em;\\n}\\n\\nsup {\\n  top: -0.5em;\\n}\\n\\n/*\\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\\n3. Remove gaps between table borders by default.\\n*/\\n\\ntable {\\n  text-indent: 0; /* 1 */\\n  border-color: inherit; /* 2 */\\n  border-collapse: collapse; /* 3 */\\n}\\n\\n/*\\n1. Change the font styles in all browsers.\\n2. Remove the margin in Firefox and Safari.\\n3. Remove default padding in all browsers.\\n*/\\n\\nbutton,\\ninput,\\noptgroup,\\nselect,\\ntextarea {\\n  font-family: inherit; /* 1 */\\n  font-feature-settings: inherit; /* 1 */\\n  font-variation-settings: inherit; /* 1 */\\n  font-size: 100%; /* 1 */\\n  font-weight: inherit; /* 1 */\\n  line-height: inherit; /* 1 */\\n  letter-spacing: inherit; /* 1 */\\n  color: inherit; /* 1 */\\n  margin: 0; /* 2 */\\n  padding: 0; /* 3 */\\n}\\n\\n/*\\nRemove the inheritance of text transform in Edge and Firefox.\\n*/\\n\\nbutton,\\nselect {\\n  text-transform: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Remove default button styles.\\n*/\\n\\nbutton,\\ninput:where([type='button']),\\ninput:where([type='reset']),\\ninput:where([type='submit']) {\\n  -webkit-appearance: button; /* 1 */\\n  background-color: transparent; /* 2 */\\n  background-image: none; /* 2 */\\n}\\n\\n/*\\nUse the modern Firefox focus style for all focusable elements.\\n*/\\n\\n:-moz-focusring {\\n  outline: auto;\\n}\\n\\n/*\\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\\n*/\\n\\n:-moz-ui-invalid {\\n  box-shadow: none;\\n}\\n\\n/*\\nAdd the correct vertical alignment in Chrome and Firefox.\\n*/\\n\\nprogress {\\n  vertical-align: baseline;\\n}\\n\\n/*\\nCorrect the cursor style of increment and decrement buttons in Safari.\\n*/\\n\\n::-webkit-inner-spin-button,\\n::-webkit-outer-spin-button {\\n  height: auto;\\n}\\n\\n/*\\n1. Correct the odd appearance in Chrome and Safari.\\n2. Correct the outline style in Safari.\\n*/\\n\\n[type='search'] {\\n  -webkit-appearance: textfield; /* 1 */\\n  outline-offset: -2px; /* 2 */\\n}\\n\\n/*\\nRemove the inner padding in Chrome and Safari on macOS.\\n*/\\n\\n::-webkit-search-decoration {\\n  -webkit-appearance: none;\\n}\\n\\n/*\\n1. Correct the inability to style clickable types in iOS and Safari.\\n2. Change font properties to `inherit` in Safari.\\n*/\\n\\n::-webkit-file-upload-button {\\n  -webkit-appearance: button; /* 1 */\\n  font: inherit; /* 2 */\\n}\\n\\n/*\\nAdd the correct display in Chrome and Safari.\\n*/\\n\\nsummary {\\n  display: list-item;\\n}\\n\\n/*\\nRemoves the default spacing and border for appropriate elements.\\n*/\\n\\nblockquote,\\ndl,\\ndd,\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6,\\nhr,\\nfigure,\\np,\\npre {\\n  margin: 0;\\n}\\n\\nfieldset {\\n  margin: 0;\\n  padding: 0;\\n}\\n\\nlegend {\\n  padding: 0;\\n}\\n\\nol,\\nul,\\nmenu {\\n  list-style: none;\\n  margin: 0;\\n  padding: 0;\\n}\\n\\n/*\\nReset default styling for dialogs.\\n*/\\ndialog {\\n  padding: 0;\\n}\\n\\n/*\\nPrevent resizing textareas horizontally by default.\\n*/\\n\\ntextarea {\\n  resize: vertical;\\n}\\n\\n/*\\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\\n2. Set the default placeholder color to the user's configured gray 400 color.\\n*/\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\ninput::placeholder,\\ntextarea::placeholder {\\n  opacity: 1; /* 1 */\\n  color: #9ca3af; /* 2 */\\n}\\n\\n/*\\nSet the default cursor for buttons.\\n*/\\n\\nbutton,\\n[role=\\\"button\\\"] {\\n  cursor: pointer;\\n}\\n\\n/*\\nMake sure disabled buttons don't get the pointer cursor.\\n*/\\n:disabled {\\n  cursor: default;\\n}\\n\\n/*\\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\\n   This can trigger a poorly considered lint error in some tools but is included by design.\\n*/\\n\\nimg,\\nsvg,\\nvideo,\\ncanvas,\\naudio,\\niframe,\\nembed,\\nobject {\\n  display: block; /* 1 */\\n  vertical-align: middle; /* 2 */\\n}\\n\\n/*\\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\\n*/\\n\\nimg,\\nvideo {\\n  max-width: 100%;\\n  height: auto;\\n}\\n\\n/* Make elements with the HTML hidden attribute stay hidden by default */\\n[hidden]:where(:not([hidden=\\\"until-found\\\"])) {\\n  display: none;\\n}\\n\\n[type='text'],input:where(:not([type])),[type='email'],[type='url'],[type='password'],[type='number'],[type='date'],[type='datetime-local'],[type='month'],[type='search'],[type='tel'],[type='time'],[type='week'],[multiple],textarea,select {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  border-radius: 0px;\\n  padding-top: 0.5rem;\\n  padding-right: 0.75rem;\\n  padding-bottom: 0.5rem;\\n  padding-left: 0.75rem;\\n  font-size: 1rem;\\n  line-height: 1.5rem;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 0px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n  border-color: #2563eb;\\n}\\n\\ninput::-moz-placeholder, textarea::-moz-placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\ninput::placeholder,textarea::placeholder {\\n  color: #6b7280;\\n  opacity: 1;\\n}\\n\\n::-webkit-datetime-edit-fields-wrapper {\\n  padding: 0;\\n}\\n\\n::-webkit-date-and-time-value {\\n  min-height: 1.5em;\\n  text-align: inherit;\\n}\\n\\n::-webkit-datetime-edit {\\n  display: inline-flex;\\n}\\n\\n::-webkit-datetime-edit,::-webkit-datetime-edit-year-field,::-webkit-datetime-edit-month-field,::-webkit-datetime-edit-day-field,::-webkit-datetime-edit-hour-field,::-webkit-datetime-edit-minute-field,::-webkit-datetime-edit-second-field,::-webkit-datetime-edit-millisecond-field,::-webkit-datetime-edit-meridiem-field {\\n  padding-top: 0;\\n  padding-bottom: 0;\\n}\\n\\nselect {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e\\\");\\n  background-position: right 0.5rem center;\\n  background-repeat: no-repeat;\\n  background-size: 1.5em 1.5em;\\n  padding-right: 2.5rem;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n}\\n\\n[multiple],[size]:where(select:not([size=\\\"1\\\"])) {\\n  background-image: initial;\\n  background-position: initial;\\n  background-repeat: unset;\\n  background-size: initial;\\n  padding-right: 0.75rem;\\n  -webkit-print-color-adjust: unset;\\n          print-color-adjust: unset;\\n}\\n\\n[type='checkbox'],[type='radio'] {\\n  -webkit-appearance: none;\\n     -moz-appearance: none;\\n          appearance: none;\\n  padding: 0;\\n  -webkit-print-color-adjust: exact;\\n          print-color-adjust: exact;\\n  display: inline-block;\\n  vertical-align: middle;\\n  background-origin: border-box;\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n  flex-shrink: 0;\\n  height: 1rem;\\n  width: 1rem;\\n  color: #2563eb;\\n  background-color: #fff;\\n  border-color: #6b7280;\\n  border-width: 1px;\\n  --tw-shadow: 0 0 #0000;\\n}\\n\\n[type='checkbox'] {\\n  border-radius: 0px;\\n}\\n\\n[type='radio'] {\\n  border-radius: 100%;\\n}\\n\\n[type='checkbox']:focus,[type='radio']:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n  --tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);\\n  --tw-ring-offset-width: 2px;\\n  --tw-ring-offset-color: #fff;\\n  --tw-ring-color: #2563eb;\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\\n}\\n\\n[type='checkbox']:checked,[type='radio']:checked {\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n[type='checkbox']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='radio']:checked {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e\\\");\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='radio']:checked {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:checked:hover,[type='checkbox']:checked:focus,[type='radio']:checked:hover,[type='radio']:checked:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='checkbox']:indeterminate {\\n  background-image: url(\\\"data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e\\\");\\n  border-color: transparent;\\n  background-color: currentColor;\\n  background-size: 100% 100%;\\n  background-position: center;\\n  background-repeat: no-repeat;\\n}\\n\\n@media (forced-colors: active)  {\\n\\n  [type='checkbox']:indeterminate {\\n    -webkit-appearance: auto;\\n       -moz-appearance: auto;\\n            appearance: auto;\\n  }\\n}\\n\\n[type='checkbox']:indeterminate:hover,[type='checkbox']:indeterminate:focus {\\n  border-color: transparent;\\n  background-color: currentColor;\\n}\\n\\n[type='file'] {\\n  background: unset;\\n  border-color: inherit;\\n  border-width: 0;\\n  border-radius: 0;\\n  padding: 0;\\n  font-size: unset;\\n  line-height: inherit;\\n}\\n\\n[type='file']:focus {\\n  outline: 1px solid ButtonText;\\n  outline: 1px auto -webkit-focus-ring-color;\\n}\\n  :root {\\n    --background: 0 0% 100%;\\n    --foreground: 222.2 84% 4.9%;\\n    --card: 0 0% 100%;\\n    --card-foreground: 222.2 84% 4.9%;\\n    --popover: 0 0% 100%;\\n    --popover-foreground: 222.2 84% 4.9%;\\n    --primary: 221.2 83.2% 53.3%;\\n    --primary-foreground: 210 40% 98%;\\n    --secondary: 210 40% 96%;\\n    --secondary-foreground: 222.2 84% 4.9%;\\n    --muted: 210 40% 96%;\\n    --muted-foreground: 215.4 16.3% 46.9%;\\n    --accent: 210 40% 96%;\\n    --accent-foreground: 222.2 84% 4.9%;\\n    --destructive: 0 84.2% 60.2%;\\n    --destructive-foreground: 210 40% 98%;\\n    --border: 214.3 31.8% 91.4%;\\n    --input: 214.3 31.8% 91.4%;\\n    --ring: 221.2 83.2% 53.3%;\\n    --radius: 0.5rem;\\n  }\\n  * {\\n  border-color: hsl(var(--border));\\n}\\n  body {\\n  background-color: hsl(var(--background));\\n  color: hsl(var(--foreground));\\n}\\n.sr-only {\\n  position: absolute;\\n  width: 1px;\\n  height: 1px;\\n  padding: 0;\\n  margin: -1px;\\n  overflow: hidden;\\n  clip: rect(0, 0, 0, 0);\\n  white-space: nowrap;\\n  border-width: 0;\\n}\\n.pointer-events-none {\\n  pointer-events: none;\\n}\\n.collapse {\\n  visibility: collapse;\\n}\\n.static {\\n  position: static;\\n}\\n.fixed {\\n  position: fixed;\\n}\\n.absolute {\\n  position: absolute;\\n}\\n.relative {\\n  position: relative;\\n}\\n.sticky {\\n  position: sticky;\\n}\\n.inset-0 {\\n  inset: 0px;\\n}\\n.inset-y-0 {\\n  top: 0px;\\n  bottom: 0px;\\n}\\n.-right-1 {\\n  right: -0.25rem;\\n}\\n.-right-2 {\\n  right: -0.5rem;\\n}\\n.-top-1 {\\n  top: -0.25rem;\\n}\\n.-top-2 {\\n  top: -0.5rem;\\n}\\n.bottom-2 {\\n  bottom: 0.5rem;\\n}\\n.left-2 {\\n  left: 0.5rem;\\n}\\n.left-3 {\\n  left: 0.75rem;\\n}\\n.left-\\\\[50\\\\%\\\\] {\\n  left: 50%;\\n}\\n.right-0 {\\n  right: 0px;\\n}\\n.right-2 {\\n  right: 0.5rem;\\n}\\n.right-3 {\\n  right: 0.75rem;\\n}\\n.right-4 {\\n  right: 1rem;\\n}\\n.top-0 {\\n  top: 0px;\\n}\\n.top-1\\\\/2 {\\n  top: 50%;\\n}\\n.top-2\\\\.5 {\\n  top: 0.625rem;\\n}\\n.top-4 {\\n  top: 1rem;\\n}\\n.top-\\\\[50\\\\%\\\\] {\\n  top: 50%;\\n}\\n.z-10 {\\n  z-index: 10;\\n}\\n.z-40 {\\n  z-index: 40;\\n}\\n.z-50 {\\n  z-index: 50;\\n}\\n.-mx-1 {\\n  margin-left: -0.25rem;\\n  margin-right: -0.25rem;\\n}\\n.mx-auto {\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n.my-1 {\\n  margin-top: 0.25rem;\\n  margin-bottom: 0.25rem;\\n}\\n.my-2 {\\n  margin-top: 0.5rem;\\n  margin-bottom: 0.5rem;\\n}\\n.mb-1 {\\n  margin-bottom: 0.25rem;\\n}\\n.mb-2 {\\n  margin-bottom: 0.5rem;\\n}\\n.mb-3 {\\n  margin-bottom: 0.75rem;\\n}\\n.mb-4 {\\n  margin-bottom: 1rem;\\n}\\n.mb-6 {\\n  margin-bottom: 1.5rem;\\n}\\n.ml-1 {\\n  margin-left: 0.25rem;\\n}\\n.ml-2 {\\n  margin-left: 0.5rem;\\n}\\n.ml-4 {\\n  margin-left: 1rem;\\n}\\n.ml-auto {\\n  margin-left: auto;\\n}\\n.mr-1 {\\n  margin-right: 0.25rem;\\n}\\n.mr-2 {\\n  margin-right: 0.5rem;\\n}\\n.mr-3 {\\n  margin-right: 0.75rem;\\n}\\n.mt-0 {\\n  margin-top: 0px;\\n}\\n.mt-0\\\\.5 {\\n  margin-top: 0.125rem;\\n}\\n.mt-1 {\\n  margin-top: 0.25rem;\\n}\\n.mt-2 {\\n  margin-top: 0.5rem;\\n}\\n.mt-3 {\\n  margin-top: 0.75rem;\\n}\\n.mt-4 {\\n  margin-top: 1rem;\\n}\\n.mt-6 {\\n  margin-top: 1.5rem;\\n}\\n.line-clamp-2 {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 2;\\n}\\n.block {\\n  display: block;\\n}\\n.inline-block {\\n  display: inline-block;\\n}\\n.inline {\\n  display: inline;\\n}\\n.flex {\\n  display: flex;\\n}\\n.inline-flex {\\n  display: inline-flex;\\n}\\n.table {\\n  display: table;\\n}\\n.grid {\\n  display: grid;\\n}\\n.hidden {\\n  display: none;\\n}\\n.aspect-video {\\n  aspect-ratio: 16 / 9;\\n}\\n.h-1 {\\n  height: 0.25rem;\\n}\\n.h-10 {\\n  height: 2.5rem;\\n}\\n.h-11 {\\n  height: 2.75rem;\\n}\\n.h-12 {\\n  height: 3rem;\\n}\\n.h-16 {\\n  height: 4rem;\\n}\\n.h-2 {\\n  height: 0.5rem;\\n}\\n.h-20 {\\n  height: 5rem;\\n}\\n.h-3 {\\n  height: 0.75rem;\\n}\\n.h-3\\\\.5 {\\n  height: 0.875rem;\\n}\\n.h-32 {\\n  height: 8rem;\\n}\\n.h-4 {\\n  height: 1rem;\\n}\\n.h-5 {\\n  height: 1.25rem;\\n}\\n.h-6 {\\n  height: 1.5rem;\\n}\\n.h-64 {\\n  height: 16rem;\\n}\\n.h-8 {\\n  height: 2rem;\\n}\\n.h-80 {\\n  height: 20rem;\\n}\\n.h-9 {\\n  height: 2.25rem;\\n}\\n.h-96 {\\n  height: 24rem;\\n}\\n.h-\\\\[1px\\\\] {\\n  height: 1px;\\n}\\n.h-\\\\[var\\\\(--radix-select-trigger-height\\\\)\\\\] {\\n  height: var(--radix-select-trigger-height);\\n}\\n.h-auto {\\n  height: auto;\\n}\\n.h-full {\\n  height: 100%;\\n}\\n.h-px {\\n  height: 1px;\\n}\\n.h-screen {\\n  height: 100vh;\\n}\\n.max-h-20 {\\n  max-height: 5rem;\\n}\\n.max-h-32 {\\n  max-height: 8rem;\\n}\\n.max-h-40 {\\n  max-height: 10rem;\\n}\\n.max-h-60 {\\n  max-height: 15rem;\\n}\\n.max-h-64 {\\n  max-height: 16rem;\\n}\\n.max-h-96 {\\n  max-height: 24rem;\\n}\\n.max-h-\\\\[80vh\\\\] {\\n  max-height: 80vh;\\n}\\n.max-h-\\\\[90vh\\\\] {\\n  max-height: 90vh;\\n}\\n.min-h-\\\\[80px\\\\] {\\n  min-height: 80px;\\n}\\n.min-h-screen {\\n  min-height: 100vh;\\n}\\n.w-10 {\\n  width: 2.5rem;\\n}\\n.w-11 {\\n  width: 2.75rem;\\n}\\n.w-12 {\\n  width: 3rem;\\n}\\n.w-16 {\\n  width: 4rem;\\n}\\n.w-2 {\\n  width: 0.5rem;\\n}\\n.w-20 {\\n  width: 5rem;\\n}\\n.w-24 {\\n  width: 6rem;\\n}\\n.w-3 {\\n  width: 0.75rem;\\n}\\n.w-3\\\\.5 {\\n  width: 0.875rem;\\n}\\n.w-32 {\\n  width: 8rem;\\n}\\n.w-4 {\\n  width: 1rem;\\n}\\n.w-40 {\\n  width: 10rem;\\n}\\n.w-48 {\\n  width: 12rem;\\n}\\n.w-5 {\\n  width: 1.25rem;\\n}\\n.w-56 {\\n  width: 14rem;\\n}\\n.w-6 {\\n  width: 1.5rem;\\n}\\n.w-64 {\\n  width: 16rem;\\n}\\n.w-8 {\\n  width: 2rem;\\n}\\n.w-80 {\\n  width: 20rem;\\n}\\n.w-\\\\[1px\\\\] {\\n  width: 1px;\\n}\\n.w-full {\\n  width: 100%;\\n}\\n.w-px {\\n  width: 1px;\\n}\\n.min-w-0 {\\n  min-width: 0px;\\n}\\n.min-w-\\\\[100px\\\\] {\\n  min-width: 100px;\\n}\\n.min-w-\\\\[120px\\\\] {\\n  min-width: 120px;\\n}\\n.min-w-\\\\[150px\\\\] {\\n  min-width: 150px;\\n}\\n.min-w-\\\\[8rem\\\\] {\\n  min-width: 8rem;\\n}\\n.min-w-\\\\[var\\\\(--radix-select-trigger-width\\\\)\\\\] {\\n  min-width: var(--radix-select-trigger-width);\\n}\\n.max-w-20 {\\n  max-width: 5rem;\\n}\\n.max-w-2xl {\\n  max-width: 42rem;\\n}\\n.max-w-4xl {\\n  max-width: 56rem;\\n}\\n.max-w-6xl {\\n  max-width: 72rem;\\n}\\n.max-w-7xl {\\n  max-width: 80rem;\\n}\\n.max-w-lg {\\n  max-width: 32rem;\\n}\\n.max-w-md {\\n  max-width: 28rem;\\n}\\n.max-w-sm {\\n  max-width: 24rem;\\n}\\n.max-w-xs {\\n  max-width: 20rem;\\n}\\n.flex-1 {\\n  flex: 1 1 0%;\\n}\\n.flex-shrink-0 {\\n  flex-shrink: 0;\\n}\\n.shrink-0 {\\n  flex-shrink: 0;\\n}\\n.caption-bottom {\\n  caption-side: bottom;\\n}\\n.border-collapse {\\n  border-collapse: collapse;\\n}\\n.-translate-y-1\\\\/2 {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-x-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-x: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.translate-y-\\\\[-50\\\\%\\\\] {\\n  --tw-translate-y: -50%;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.rotate-90 {\\n  --tw-rotate: 90deg;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n.transform {\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n@keyframes pulse {\\n\\n  50% {\\n    opacity: .5;\\n  }\\n}\\n.animate-pulse {\\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\\n}\\n@keyframes spin {\\n\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n.animate-spin {\\n  animation: spin 1s linear infinite;\\n}\\n.cursor-default {\\n  cursor: default;\\n}\\n.cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n.cursor-pointer {\\n  cursor: pointer;\\n}\\n.select-none {\\n  -webkit-user-select: none;\\n     -moz-user-select: none;\\n          user-select: none;\\n}\\n.list-inside {\\n  list-style-position: inside;\\n}\\n.list-decimal {\\n  list-style-type: decimal;\\n}\\n.grid-cols-1 {\\n  grid-template-columns: repeat(1, minmax(0, 1fr));\\n}\\n.grid-cols-2 {\\n  grid-template-columns: repeat(2, minmax(0, 1fr));\\n}\\n.grid-cols-3 {\\n  grid-template-columns: repeat(3, minmax(0, 1fr));\\n}\\n.grid-cols-4 {\\n  grid-template-columns: repeat(4, minmax(0, 1fr));\\n}\\n.grid-cols-5 {\\n  grid-template-columns: repeat(5, minmax(0, 1fr));\\n}\\n.flex-row {\\n  flex-direction: row;\\n}\\n.flex-col {\\n  flex-direction: column;\\n}\\n.flex-col-reverse {\\n  flex-direction: column-reverse;\\n}\\n.flex-wrap {\\n  flex-wrap: wrap;\\n}\\n.items-start {\\n  align-items: flex-start;\\n}\\n.items-end {\\n  align-items: flex-end;\\n}\\n.items-center {\\n  align-items: center;\\n}\\n.justify-start {\\n  justify-content: flex-start;\\n}\\n.justify-end {\\n  justify-content: flex-end;\\n}\\n.justify-center {\\n  justify-content: center;\\n}\\n.justify-between {\\n  justify-content: space-between;\\n}\\n.gap-2 {\\n  gap: 0.5rem;\\n}\\n.gap-3 {\\n  gap: 0.75rem;\\n}\\n.gap-4 {\\n  gap: 1rem;\\n}\\n.gap-6 {\\n  gap: 1.5rem;\\n}\\n.space-x-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.25rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(0.75rem * var(--tw-space-x-reverse));\\n  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 0;\\n  margin-right: calc(1rem * var(--tw-space-x-reverse));\\n  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));\\n}\\n.space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n}\\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));\\n}\\n.space-y-1\\\\.5 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\\n}\\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));\\n}\\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1rem * var(--tw-space-y-reverse));\\n}\\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));\\n}\\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-y-reverse: 0;\\n  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));\\n  margin-bottom: calc(2rem * var(--tw-space-y-reverse));\\n}\\n.space-x-reverse > :not([hidden]) ~ :not([hidden]) {\\n  --tw-space-x-reverse: 1;\\n}\\n.overflow-auto {\\n  overflow: auto;\\n}\\n.overflow-hidden {\\n  overflow: hidden;\\n}\\n.overflow-x-auto {\\n  overflow-x: auto;\\n}\\n.overflow-y-auto {\\n  overflow-y: auto;\\n}\\n.truncate {\\n  overflow: hidden;\\n  text-overflow: ellipsis;\\n  white-space: nowrap;\\n}\\n.whitespace-nowrap {\\n  white-space: nowrap;\\n}\\n.rounded {\\n  border-radius: 0.25rem;\\n}\\n.rounded-full {\\n  border-radius: 9999px;\\n}\\n.rounded-lg {\\n  border-radius: var(--radius);\\n}\\n.rounded-md {\\n  border-radius: calc(var(--radius) - 2px);\\n}\\n.rounded-sm {\\n  border-radius: calc(var(--radius) - 4px);\\n}\\n.rounded-t-lg {\\n  border-top-left-radius: var(--radius);\\n  border-top-right-radius: var(--radius);\\n}\\n.border {\\n  border-width: 1px;\\n}\\n.border-0 {\\n  border-width: 0px;\\n}\\n.border-2 {\\n  border-width: 2px;\\n}\\n.border-b {\\n  border-bottom-width: 1px;\\n}\\n.border-b-2 {\\n  border-bottom-width: 2px;\\n}\\n.border-l-4 {\\n  border-left-width: 4px;\\n}\\n.border-r {\\n  border-right-width: 1px;\\n}\\n.border-t {\\n  border-top-width: 1px;\\n}\\n.border-t-2 {\\n  border-top-width: 2px;\\n}\\n.border-dashed {\\n  border-style: dashed;\\n}\\n.border-blue-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(147 197 253 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n}\\n.border-blue-600 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(37 99 235 / var(--tw-border-opacity, 1));\\n}\\n.border-destructive\\\\/50 {\\n  border-color: hsl(var(--destructive) / 0.5);\\n}\\n.border-gray-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));\\n}\\n.border-gray-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));\\n}\\n.border-green-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(187 247 208 / var(--tw-border-opacity, 1));\\n}\\n.border-green-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(34 197 94 / var(--tw-border-opacity, 1));\\n}\\n.border-indigo-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(199 210 254 / var(--tw-border-opacity, 1));\\n}\\n.border-input {\\n  border-color: hsl(var(--input));\\n}\\n.border-orange-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 215 170 / var(--tw-border-opacity, 1));\\n}\\n.border-orange-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(253 186 116 / var(--tw-border-opacity, 1));\\n}\\n.border-primary {\\n  border-color: hsl(var(--primary));\\n}\\n.border-purple-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(233 213 255 / var(--tw-border-opacity, 1));\\n}\\n.border-red-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 202 202 / var(--tw-border-opacity, 1));\\n}\\n.border-red-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(252 165 165 / var(--tw-border-opacity, 1));\\n}\\n.border-red-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\n.border-transparent {\\n  border-color: transparent;\\n}\\n.border-white {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-200 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(254 240 138 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-300 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(253 224 71 / var(--tw-border-opacity, 1));\\n}\\n.border-yellow-500 {\\n  --tw-border-opacity: 1;\\n  border-color: rgb(234 179 8 / var(--tw-border-opacity, 1));\\n}\\n.border-l-blue-500 {\\n  --tw-border-opacity: 1;\\n  border-left-color: rgb(59 130 246 / var(--tw-border-opacity, 1));\\n}\\n.border-l-gray-500 {\\n  --tw-border-opacity: 1;\\n  border-left-color: rgb(107 114 128 / var(--tw-border-opacity, 1));\\n}\\n.border-l-orange-500 {\\n  --tw-border-opacity: 1;\\n  border-left-color: rgb(249 115 22 / var(--tw-border-opacity, 1));\\n}\\n.border-l-red-500 {\\n  --tw-border-opacity: 1;\\n  border-left-color: rgb(239 68 68 / var(--tw-border-opacity, 1));\\n}\\n.bg-background {\\n  background-color: hsl(var(--background));\\n}\\n.bg-background\\\\/80 {\\n  background-color: hsl(var(--background) / 0.8);\\n}\\n.bg-black {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-blue-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-border {\\n  background-color: hsl(var(--border));\\n}\\n.bg-card {\\n  background-color: hsl(var(--card));\\n}\\n.bg-cyan-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));\\n}\\n.bg-destructive {\\n  background-color: hsl(var(--destructive));\\n}\\n.bg-emerald-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));\\n}\\n.bg-emerald-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-200 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-300 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(209 213 219 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-400 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(156 163 175 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));\\n}\\n.bg-gray-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-green-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(224 231 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-indigo-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(99 102 241 / var(--tw-bg-opacity, 1));\\n}\\n.bg-muted {\\n  background-color: hsl(var(--muted));\\n}\\n.bg-muted\\\\/50 {\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\n.bg-orange-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));\\n}\\n.bg-orange-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 115 22 / var(--tw-bg-opacity, 1));\\n}\\n.bg-popover {\\n  background-color: hsl(var(--popover));\\n}\\n.bg-primary {\\n  background-color: hsl(var(--primary));\\n}\\n.bg-purple-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 232 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-purple-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));\\n}\\n.bg-red-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));\\n}\\n.bg-rose-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(244 63 94 / var(--tw-bg-opacity, 1));\\n}\\n.bg-secondary {\\n  background-color: hsl(var(--secondary));\\n}\\n.bg-teal-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(204 251 241 / var(--tw-bg-opacity, 1));\\n}\\n.bg-transparent {\\n  background-color: transparent;\\n}\\n.bg-white {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-100 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 249 195 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-50 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 252 232 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-500 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));\\n}\\n.bg-yellow-600 {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));\\n}\\n.bg-opacity-75 {\\n  --tw-bg-opacity: 0.75;\\n}\\n.bg-gradient-to-r {\\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\\n}\\n.bg-gradient-to-br {\\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\\n}\\n.from-blue-50 {\\n  --tw-gradient-from: #eff6ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(239 246 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-green-50 {\\n  --tw-gradient-from: #f0fdf4 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(240 253 244 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-purple-50 {\\n  --tw-gradient-from: #faf5ff var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(250 245 255 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.from-blue-600 {\\n  --tw-gradient-from: #2563eb var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(37 99 235 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n.to-blue-100 {\\n  --tw-gradient-to: #dbeafe var(--tw-gradient-to-position);\\n}\\n.to-green-100 {\\n  --tw-gradient-to: #dcfce7 var(--tw-gradient-to-position);\\n}\\n.to-purple-100 {\\n  --tw-gradient-to: #f3e8ff var(--tw-gradient-to-position);\\n}\\n.to-indigo-100 {\\n  --tw-gradient-to: #e0e7ff var(--tw-gradient-to-position);\\n}\\n.to-indigo-600 {\\n  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);\\n}\\n.fill-current {\\n  fill: currentColor;\\n}\\n.p-0 {\\n  padding: 0px;\\n}\\n.p-1 {\\n  padding: 0.25rem;\\n}\\n.p-12 {\\n  padding: 3rem;\\n}\\n.p-2 {\\n  padding: 0.5rem;\\n}\\n.p-3 {\\n  padding: 0.75rem;\\n}\\n.p-4 {\\n  padding: 1rem;\\n}\\n.p-6 {\\n  padding: 1.5rem;\\n}\\n.p-8 {\\n  padding: 2rem;\\n}\\n.px-2 {\\n  padding-left: 0.5rem;\\n  padding-right: 0.5rem;\\n}\\n.px-2\\\\.5 {\\n  padding-left: 0.625rem;\\n  padding-right: 0.625rem;\\n}\\n.px-3 {\\n  padding-left: 0.75rem;\\n  padding-right: 0.75rem;\\n}\\n.px-4 {\\n  padding-left: 1rem;\\n  padding-right: 1rem;\\n}\\n.px-8 {\\n  padding-left: 2rem;\\n  padding-right: 2rem;\\n}\\n.py-0\\\\.5 {\\n  padding-top: 0.125rem;\\n  padding-bottom: 0.125rem;\\n}\\n.py-1 {\\n  padding-top: 0.25rem;\\n  padding-bottom: 0.25rem;\\n}\\n.py-1\\\\.5 {\\n  padding-top: 0.375rem;\\n  padding-bottom: 0.375rem;\\n}\\n.py-12 {\\n  padding-top: 3rem;\\n  padding-bottom: 3rem;\\n}\\n.py-2 {\\n  padding-top: 0.5rem;\\n  padding-bottom: 0.5rem;\\n}\\n.py-4 {\\n  padding-top: 1rem;\\n  padding-bottom: 1rem;\\n}\\n.py-6 {\\n  padding-top: 1.5rem;\\n  padding-bottom: 1.5rem;\\n}\\n.py-8 {\\n  padding-top: 2rem;\\n  padding-bottom: 2rem;\\n}\\n.py-3 {\\n  padding-top: 0.75rem;\\n  padding-bottom: 0.75rem;\\n}\\n.pb-2 {\\n  padding-bottom: 0.5rem;\\n}\\n.pb-3 {\\n  padding-bottom: 0.75rem;\\n}\\n.pb-4 {\\n  padding-bottom: 1rem;\\n}\\n.pl-10 {\\n  padding-left: 2.5rem;\\n}\\n.pl-4 {\\n  padding-left: 1rem;\\n}\\n.pl-8 {\\n  padding-left: 2rem;\\n}\\n.pr-10 {\\n  padding-right: 2.5rem;\\n}\\n.pr-2 {\\n  padding-right: 0.5rem;\\n}\\n.pr-3 {\\n  padding-right: 0.75rem;\\n}\\n.pr-6 {\\n  padding-right: 1.5rem;\\n}\\n.pr-8 {\\n  padding-right: 2rem;\\n}\\n.pt-0 {\\n  padding-top: 0px;\\n}\\n.pt-2 {\\n  padding-top: 0.5rem;\\n}\\n.pt-3 {\\n  padding-top: 0.75rem;\\n}\\n.pt-4 {\\n  padding-top: 1rem;\\n}\\n.pt-6 {\\n  padding-top: 1.5rem;\\n}\\n.text-left {\\n  text-align: left;\\n}\\n.text-center {\\n  text-align: center;\\n}\\n.text-right {\\n  text-align: right;\\n}\\n.align-middle {\\n  vertical-align: middle;\\n}\\n.font-mono {\\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \\\"Liberation Mono\\\", \\\"Courier New\\\", monospace;\\n}\\n.text-2xl {\\n  font-size: 1.5rem;\\n  line-height: 2rem;\\n}\\n.text-3xl {\\n  font-size: 1.875rem;\\n  line-height: 2.25rem;\\n}\\n.text-4xl {\\n  font-size: 2.25rem;\\n  line-height: 2.5rem;\\n}\\n.text-lg {\\n  font-size: 1.125rem;\\n  line-height: 1.75rem;\\n}\\n.text-sm {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n.text-xl {\\n  font-size: 1.25rem;\\n  line-height: 1.75rem;\\n}\\n.text-xs {\\n  font-size: 0.75rem;\\n  line-height: 1rem;\\n}\\n.font-bold {\\n  font-weight: 700;\\n}\\n.font-medium {\\n  font-weight: 500;\\n}\\n.font-semibold {\\n  font-weight: 600;\\n}\\n.capitalize {\\n  text-transform: capitalize;\\n}\\n.italic {\\n  font-style: italic;\\n}\\n.leading-4 {\\n  line-height: 1rem;\\n}\\n.leading-none {\\n  line-height: 1;\\n}\\n.leading-relaxed {\\n  line-height: 1.625;\\n}\\n.tracking-tight {\\n  letter-spacing: -0.025em;\\n}\\n.tracking-widest {\\n  letter-spacing: 0.1em;\\n}\\n.text-blue-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(59 130 246 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n.text-blue-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n.text-card-foreground {\\n  color: hsl(var(--card-foreground));\\n}\\n.text-current {\\n  color: currentColor;\\n}\\n.text-cyan-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(8 145 178 / var(--tw-text-opacity, 1));\\n}\\n.text-destructive {\\n  color: hsl(var(--destructive));\\n}\\n.text-destructive-foreground {\\n  color: hsl(var(--destructive-foreground));\\n}\\n.text-emerald-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(5 150 105 / var(--tw-text-opacity, 1));\\n}\\n.text-emerald-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(6 95 70 / var(--tw-text-opacity, 1));\\n}\\n.text-foreground {\\n  color: hsl(var(--foreground));\\n}\\n.text-gray-300 {\\n  --tw-text-opacity: 1;\\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-400 {\\n  --tw-text-opacity: 1;\\n  color: rgb(156 163 175 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(31 41 55 / var(--tw-text-opacity, 1));\\n}\\n.text-gray-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n.text-green-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(34 197 94 / var(--tw-text-opacity, 1));\\n}\\n.text-green-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 163 74 / var(--tw-text-opacity, 1));\\n}\\n.text-green-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n.text-green-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(22 101 52 / var(--tw-text-opacity, 1));\\n}\\n.text-green-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(20 83 45 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(79 70 229 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(67 56 202 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 48 163 / var(--tw-text-opacity, 1));\\n}\\n.text-indigo-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(49 46 129 / var(--tw-text-opacity, 1));\\n}\\n.text-muted-foreground {\\n  color: hsl(var(--muted-foreground));\\n}\\n.text-orange-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(249 115 22 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 88 12 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\\n}\\n.text-orange-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(124 45 18 / var(--tw-text-opacity, 1));\\n}\\n.text-popover-foreground {\\n  color: hsl(var(--popover-foreground));\\n}\\n.text-primary {\\n  color: hsl(var(--primary));\\n}\\n.text-primary-foreground {\\n  color: hsl(var(--primary-foreground));\\n}\\n.text-purple-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(168 85 247 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(147 51 234 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 33 168 / var(--tw-text-opacity, 1));\\n}\\n.text-purple-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(88 28 135 / var(--tw-text-opacity, 1));\\n}\\n.text-red-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(239 68 68 / var(--tw-text-opacity, 1));\\n}\\n.text-red-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(220 38 38 / var(--tw-text-opacity, 1));\\n}\\n.text-red-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n}\\n.text-red-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n.text-red-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(127 29 29 / var(--tw-text-opacity, 1));\\n}\\n.text-secondary-foreground {\\n  color: hsl(var(--secondary-foreground));\\n}\\n.text-teal-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(15 118 110 / var(--tw-text-opacity, 1));\\n}\\n.text-teal-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 94 89 / var(--tw-text-opacity, 1));\\n}\\n.text-white {\\n  --tw-text-opacity: 1;\\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-500 {\\n  --tw-text-opacity: 1;\\n  color: rgb(234 179 8 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-600 {\\n  --tw-text-opacity: 1;\\n  color: rgb(202 138 4 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-700 {\\n  --tw-text-opacity: 1;\\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-800 {\\n  --tw-text-opacity: 1;\\n  color: rgb(133 77 14 / var(--tw-text-opacity, 1));\\n}\\n.text-yellow-900 {\\n  --tw-text-opacity: 1;\\n  color: rgb(113 63 18 / var(--tw-text-opacity, 1));\\n}\\n.underline-offset-4 {\\n  text-underline-offset: 4px;\\n}\\n.opacity-0 {\\n  opacity: 0;\\n}\\n.opacity-50 {\\n  opacity: 0.5;\\n}\\n.opacity-60 {\\n  opacity: 0.6;\\n}\\n.opacity-70 {\\n  opacity: 0.7;\\n}\\n.opacity-75 {\\n  opacity: 0.75;\\n}\\n.opacity-90 {\\n  opacity: 0.9;\\n}\\n.shadow {\\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-lg {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-md {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-sm {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.shadow-xl {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n.outline-none {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n.outline {\\n  outline-style: solid;\\n}\\n.ring-0 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-1 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-2 {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n.ring-black {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(0 0 0 / var(--tw-ring-opacity, 1));\\n}\\n.ring-blue-500 {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n.ring-opacity-5 {\\n  --tw-ring-opacity: 0.05;\\n}\\n.ring-offset-background {\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\n.filter {\\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\\n}\\n.backdrop-blur-sm {\\n  --tw-backdrop-blur: blur(4px);\\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\\n}\\n.transition {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-all {\\n  transition-property: all;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-colors {\\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-opacity {\\n  transition-property: opacity;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-shadow {\\n  transition-property: box-shadow;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.transition-transform {\\n  transition-property: transform;\\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\\n  transition-duration: 150ms;\\n}\\n.duration-200 {\\n  transition-duration: 200ms;\\n}\\n.duration-300 {\\n  transition-duration: 300ms;\\n}\\n.duration-500 {\\n  transition-duration: 500ms;\\n}\\n@keyframes enter {\\n\\n  from {\\n    opacity: var(--tw-enter-opacity, 1);\\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\\n  }\\n}\\n@keyframes exit {\\n\\n  to {\\n    opacity: var(--tw-exit-opacity, 1);\\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\\n  }\\n}\\n.animate-in {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\n.slide-in-from-right {\\n  --tw-enter-translate-x: 100%;\\n}\\n.duration-200 {\\n  animation-duration: 200ms;\\n}\\n.duration-300 {\\n  animation-duration: 300ms;\\n}\\n.duration-500 {\\n  animation-duration: 500ms;\\n}\\n\\n/* RTL Support for Arabic */\\n[dir=\\\"rtl\\\"] {\\n  direction: rtl;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n/* Custom scrollbar */\\n::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: hsl(var(--muted));\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: hsl(var(--border));\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: hsl(var(--muted-foreground));\\n}\\n\\n.file\\\\:border-0::file-selector-button {\\n  border-width: 0px;\\n}\\n\\n.file\\\\:bg-transparent::file-selector-button {\\n  background-color: transparent;\\n}\\n\\n.file\\\\:text-sm::file-selector-button {\\n  font-size: 0.875rem;\\n  line-height: 1.25rem;\\n}\\n\\n.file\\\\:font-medium::file-selector-button {\\n  font-weight: 500;\\n}\\n\\n.placeholder\\\\:text-muted-foreground::-moz-placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\n\\n.placeholder\\\\:text-muted-foreground::placeholder {\\n  color: hsl(var(--muted-foreground));\\n}\\n\\n.hover\\\\:scale-105:hover {\\n  --tw-scale-x: 1.05;\\n  --tw-scale-y: 1.05;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.hover\\\\:bg-accent:hover {\\n  background-color: hsl(var(--accent));\\n}\\n\\n.hover\\\\:bg-blue-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-destructive\\\\/80:hover {\\n  background-color: hsl(var(--destructive) / 0.8);\\n}\\n\\n.hover\\\\:bg-destructive\\\\/90:hover {\\n  background-color: hsl(var(--destructive) / 0.9);\\n}\\n\\n.hover\\\\:bg-gray-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-gray-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-green-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(187 247 208 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-green-700:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(21 128 61 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-indigo-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(238 242 255 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-muted\\\\/50:hover {\\n  background-color: hsl(var(--muted) / 0.5);\\n}\\n\\n.hover\\\\:bg-orange-100:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-primary\\\\/80:hover {\\n  background-color: hsl(var(--primary) / 0.8);\\n}\\n\\n.hover\\\\:bg-primary\\\\/90:hover {\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\n\\n.hover\\\\:bg-red-200:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 202 202 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-red-50:hover {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(254 242 242 / var(--tw-bg-opacity, 1));\\n}\\n\\n.hover\\\\:bg-secondary\\\\/80:hover {\\n  background-color: hsl(var(--secondary) / 0.8);\\n}\\n\\n.hover\\\\:bg-opacity-80:hover {\\n  --tw-bg-opacity: 0.8;\\n}\\n\\n.hover\\\\:from-blue-700:hover {\\n  --tw-gradient-from: #1d4ed8 var(--tw-gradient-from-position);\\n  --tw-gradient-to: rgb(29 78 216 / 0) var(--tw-gradient-to-position);\\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\\n}\\n\\n.hover\\\\:to-indigo-700:hover {\\n  --tw-gradient-to: #4338ca var(--tw-gradient-to-position);\\n}\\n\\n.hover\\\\:text-accent-foreground:hover {\\n  color: hsl(var(--accent-foreground));\\n}\\n\\n.hover\\\\:text-blue-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(37 99 235 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-blue-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(29 78 216 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-blue-800:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 64 175 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-cyan-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(14 116 144 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-emerald-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(4 120 87 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-500:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(107 114 128 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-600:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(75 85 99 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(55 65 81 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-gray-900:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(17 24 39 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-green-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(21 128 61 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-indigo-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(67 56 202 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-orange-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(194 65 12 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-orange-800:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(154 52 18 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-purple-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(126 34 206 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-red-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(185 28 28 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-red-800:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(153 27 27 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:text-yellow-700:hover {\\n  --tw-text-opacity: 1;\\n  color: rgb(161 98 7 / var(--tw-text-opacity, 1));\\n}\\n\\n.hover\\\\:underline:hover {\\n  text-decoration-line: underline;\\n}\\n\\n.hover\\\\:opacity-100:hover {\\n  opacity: 1;\\n}\\n\\n.hover\\\\:shadow-lg:hover {\\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-md:hover {\\n  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.hover\\\\:shadow-xl:hover {\\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.focus\\\\:bg-accent:focus {\\n  background-color: hsl(var(--accent));\\n}\\n\\n.focus\\\\:text-accent-foreground:focus {\\n  color: hsl(var(--accent-foreground));\\n}\\n\\n.focus\\\\:outline-none:focus {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus\\\\:ring-2:focus {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus\\\\:ring-inset:focus {\\n  --tw-ring-inset: inset;\\n}\\n\\n.focus\\\\:ring-blue-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(59 130 246 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-indigo-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(99 102 241 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-primary:focus {\\n  --tw-ring-color: hsl(var(--primary));\\n}\\n\\n.focus\\\\:ring-ring:focus {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\n\\n.focus\\\\:ring-yellow-500:focus {\\n  --tw-ring-opacity: 1;\\n  --tw-ring-color: rgb(234 179 8 / var(--tw-ring-opacity, 1));\\n}\\n\\n.focus\\\\:ring-offset-2:focus {\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus-visible\\\\:outline-none:focus-visible {\\n  outline: 2px solid transparent;\\n  outline-offset: 2px;\\n}\\n\\n.focus-visible\\\\:ring-2:focus-visible {\\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\\n}\\n\\n.focus-visible\\\\:ring-ring:focus-visible {\\n  --tw-ring-color: hsl(var(--ring));\\n}\\n\\n.focus-visible\\\\:ring-offset-2:focus-visible {\\n  --tw-ring-offset-width: 2px;\\n}\\n\\n.focus-visible\\\\:ring-offset-background:focus-visible {\\n  --tw-ring-offset-color: hsl(var(--background));\\n}\\n\\n.disabled\\\\:pointer-events-none:disabled {\\n  pointer-events: none;\\n}\\n\\n.disabled\\\\:cursor-not-allowed:disabled {\\n  cursor: not-allowed;\\n}\\n\\n.disabled\\\\:opacity-50:disabled {\\n  opacity: 0.5;\\n}\\n\\n.group:hover .group-hover\\\\:bg-primary\\\\/90 {\\n  background-color: hsl(var(--primary) / 0.9);\\n}\\n\\n.group:hover .group-hover\\\\:text-primary {\\n  color: hsl(var(--primary));\\n}\\n\\n.group:hover .group-hover\\\\:opacity-100 {\\n  opacity: 1;\\n}\\n\\n.peer:disabled ~ .peer-disabled\\\\:cursor-not-allowed {\\n  cursor: not-allowed;\\n}\\n\\n.peer:disabled ~ .peer-disabled\\\\:opacity-70 {\\n  opacity: 0.7;\\n}\\n\\n.data-\\\\[disabled\\\\]\\\\:pointer-events-none[data-disabled] {\\n  pointer-events: none;\\n}\\n\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:translate-y-1[data-side=\\\"bottom\\\"] {\\n  --tw-translate-y: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[side\\\\=left\\\\]\\\\:-translate-x-1[data-side=\\\"left\\\"] {\\n  --tw-translate-x: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[side\\\\=right\\\\]\\\\:translate-x-1[data-side=\\\"right\\\"] {\\n  --tw-translate-x: 0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[side\\\\=top\\\\]\\\\:-translate-y-1[data-side=\\\"top\\\"] {\\n  --tw-translate-y: -0.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[state\\\\=checked\\\\]\\\\:translate-x-5[data-state=\\\"checked\\\"] {\\n  --tw-translate-x: 1.25rem;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[state\\\\=unchecked\\\\]\\\\:translate-x-0[data-state=\\\"unchecked\\\"] {\\n  --tw-translate-x: 0px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-background[data-state=\\\"active\\\"] {\\n  background-color: hsl(var(--background));\\n}\\n\\n.data-\\\\[state\\\\=active\\\\]\\\\:bg-blue-50[data-state=\\\"active\\\"] {\\n  --tw-bg-opacity: 1;\\n  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));\\n}\\n\\n.data-\\\\[state\\\\=checked\\\\]\\\\:bg-primary[data-state=\\\"checked\\\"] {\\n  background-color: hsl(var(--primary));\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:bg-accent[data-state=\\\"open\\\"] {\\n  background-color: hsl(var(--accent));\\n}\\n\\n.data-\\\\[state\\\\=selected\\\\]\\\\:bg-muted[data-state=\\\"selected\\\"] {\\n  background-color: hsl(var(--muted));\\n}\\n\\n.data-\\\\[state\\\\=unchecked\\\\]\\\\:bg-input[data-state=\\\"unchecked\\\"] {\\n  background-color: hsl(var(--input));\\n}\\n\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-blue-900[data-state=\\\"active\\\"] {\\n  --tw-text-opacity: 1;\\n  color: rgb(30 58 138 / var(--tw-text-opacity, 1));\\n}\\n\\n.data-\\\\[state\\\\=active\\\\]\\\\:text-foreground[data-state=\\\"active\\\"] {\\n  color: hsl(var(--foreground));\\n}\\n\\n.data-\\\\[state\\\\=checked\\\\]\\\\:text-primary-foreground[data-state=\\\"checked\\\"] {\\n  color: hsl(var(--primary-foreground));\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:text-muted-foreground[data-state=\\\"open\\\"] {\\n  color: hsl(var(--muted-foreground));\\n}\\n\\n.data-\\\\[disabled\\\\]\\\\:opacity-50[data-disabled] {\\n  opacity: 0.5;\\n}\\n\\n.data-\\\\[state\\\\=active\\\\]\\\\:shadow-sm[data-state=\\\"active\\\"] {\\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:animate-in[data-state=\\\"open\\\"] {\\n  animation-name: enter;\\n  animation-duration: 150ms;\\n  --tw-enter-opacity: initial;\\n  --tw-enter-scale: initial;\\n  --tw-enter-rotate: initial;\\n  --tw-enter-translate-x: initial;\\n  --tw-enter-translate-y: initial;\\n}\\n\\n.data-\\\\[state\\\\=closed\\\\]\\\\:animate-out[data-state=\\\"closed\\\"] {\\n  animation-name: exit;\\n  animation-duration: 150ms;\\n  --tw-exit-opacity: initial;\\n  --tw-exit-scale: initial;\\n  --tw-exit-rotate: initial;\\n  --tw-exit-translate-x: initial;\\n  --tw-exit-translate-y: initial;\\n}\\n\\n.data-\\\\[state\\\\=closed\\\\]\\\\:fade-out-0[data-state=\\\"closed\\\"] {\\n  --tw-exit-opacity: 0;\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:fade-in-0[data-state=\\\"open\\\"] {\\n  --tw-enter-opacity: 0;\\n}\\n\\n.data-\\\\[state\\\\=closed\\\\]\\\\:zoom-out-95[data-state=\\\"closed\\\"] {\\n  --tw-exit-scale: .95;\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:zoom-in-95[data-state=\\\"open\\\"] {\\n  --tw-enter-scale: .95;\\n}\\n\\n.data-\\\\[side\\\\=bottom\\\\]\\\\:slide-in-from-top-2[data-side=\\\"bottom\\\"] {\\n  --tw-enter-translate-y: -0.5rem;\\n}\\n\\n.data-\\\\[side\\\\=left\\\\]\\\\:slide-in-from-right-2[data-side=\\\"left\\\"] {\\n  --tw-enter-translate-x: 0.5rem;\\n}\\n\\n.data-\\\\[side\\\\=right\\\\]\\\\:slide-in-from-left-2[data-side=\\\"right\\\"] {\\n  --tw-enter-translate-x: -0.5rem;\\n}\\n\\n.data-\\\\[side\\\\=top\\\\]\\\\:slide-in-from-bottom-2[data-side=\\\"top\\\"] {\\n  --tw-enter-translate-y: 0.5rem;\\n}\\n\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-left-1\\\\/2[data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-x: -50%;\\n}\\n\\n.data-\\\\[state\\\\=closed\\\\]\\\\:slide-out-to-top-\\\\[48\\\\%\\\\][data-state=\\\"closed\\\"] {\\n  --tw-exit-translate-y: -48%;\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-left-1\\\\/2[data-state=\\\"open\\\"] {\\n  --tw-enter-translate-x: -50%;\\n}\\n\\n.data-\\\\[state\\\\=open\\\\]\\\\:slide-in-from-top-\\\\[48\\\\%\\\\][data-state=\\\"open\\\"] {\\n  --tw-enter-translate-y: -48%;\\n}\\n\\n.dark\\\\:border-destructive:is(.dark *) {\\n  border-color: hsl(var(--destructive));\\n}\\n\\n@media (min-width: 640px) {\\n\\n  .sm\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .sm\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .sm\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .sm\\\\:justify-end {\\n    justify-content: flex-end;\\n  }\\n\\n  .sm\\\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 0;\\n    margin-right: calc(0.5rem * var(--tw-space-x-reverse));\\n    margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));\\n  }\\n\\n  .sm\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n\\n  .sm\\\\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-x-reverse: 1;\\n  }\\n\\n  .sm\\\\:rounded-lg {\\n    border-radius: var(--radius);\\n  }\\n\\n  .sm\\\\:px-6 {\\n    padding-left: 1.5rem;\\n    padding-right: 1.5rem;\\n  }\\n\\n  .sm\\\\:text-left {\\n    text-align: left;\\n  }\\n\\n  .sm\\\\:text-2xl {\\n    font-size: 1.5rem;\\n    line-height: 2rem;\\n  }\\n\\n  .sm\\\\:text-base {\\n    font-size: 1rem;\\n    line-height: 1.5rem;\\n  }\\n}\\n\\n@media (min-width: 768px) {\\n\\n  .md\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .md\\\\:ml-6 {\\n    margin-left: 1.5rem;\\n  }\\n\\n  .md\\\\:block {\\n    display: block;\\n  }\\n\\n  .md\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .md\\\\:w-48 {\\n    width: 12rem;\\n  }\\n\\n  .md\\\\:w-auto {\\n    width: auto;\\n  }\\n\\n  .md\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:grid-cols-6 {\\n    grid-template-columns: repeat(6, minmax(0, 1fr));\\n  }\\n\\n  .md\\\\:flex-row {\\n    flex-direction: row;\\n  }\\n\\n  .md\\\\:items-center {\\n    align-items: center;\\n  }\\n\\n  .md\\\\:justify-between {\\n    justify-content: space-between;\\n  }\\n\\n  .md\\\\:space-y-0 > :not([hidden]) ~ :not([hidden]) {\\n    --tw-space-y-reverse: 0;\\n    margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));\\n    margin-bottom: calc(0px * var(--tw-space-y-reverse));\\n  }\\n}\\n\\n@media (min-width: 1024px) {\\n\\n  .lg\\\\:fixed {\\n    position: fixed;\\n  }\\n\\n  .lg\\\\:inset-y-0 {\\n    top: 0px;\\n    bottom: 0px;\\n  }\\n\\n  .lg\\\\:col-span-1 {\\n    grid-column: span 1 / span 1;\\n  }\\n\\n  .lg\\\\:col-span-2 {\\n    grid-column: span 2 / span 2;\\n  }\\n\\n  .lg\\\\:col-span-3 {\\n    grid-column: span 3 / span 3;\\n  }\\n\\n  .lg\\\\:inline {\\n    display: inline;\\n  }\\n\\n  .lg\\\\:flex {\\n    display: flex;\\n  }\\n\\n  .lg\\\\:hidden {\\n    display: none;\\n  }\\n\\n  .lg\\\\:w-64 {\\n    width: 16rem;\\n  }\\n\\n  .lg\\\\:grid-cols-2 {\\n    grid-template-columns: repeat(2, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-3 {\\n    grid-template-columns: repeat(3, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-4 {\\n    grid-template-columns: repeat(4, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-5 {\\n    grid-template-columns: repeat(5, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-6 {\\n    grid-template-columns: repeat(6, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-7 {\\n    grid-template-columns: repeat(7, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:grid-cols-8 {\\n    grid-template-columns: repeat(8, minmax(0, 1fr));\\n  }\\n\\n  .lg\\\\:flex-col {\\n    flex-direction: column;\\n  }\\n\\n  .lg\\\\:px-8 {\\n    padding-left: 2rem;\\n    padding-right: 2rem;\\n  }\\n\\n  .lg\\\\:pl-64 {\\n    padding-left: 16rem;\\n  }\\n}\\n\\n.\\\\[\\\\&\\\\:has\\\\(\\\\[role\\\\=checkbox\\\\]\\\\)\\\\]\\\\:pr-0:has([role=checkbox]) {\\n  padding-right: 0px;\\n}\\n\\n.\\\\[\\\\&\\\\>span\\\\]\\\\:line-clamp-1>span {\\n  overflow: hidden;\\n  display: -webkit-box;\\n  -webkit-box-orient: vertical;\\n  -webkit-line-clamp: 1;\\n}\\n\\n.\\\\[\\\\&\\\\>svg\\\\+div\\\\]\\\\:translate-y-\\\\[-3px\\\\]>svg+div {\\n  --tw-translate-y: -3px;\\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\\n}\\n\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:absolute>svg {\\n  position: absolute;\\n}\\n\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:left-4>svg {\\n  left: 1rem;\\n}\\n\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:top-4>svg {\\n  top: 1rem;\\n}\\n\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-destructive>svg {\\n  color: hsl(var(--destructive));\\n}\\n\\n.\\\\[\\\\&\\\\>svg\\\\]\\\\:text-foreground>svg {\\n  color: hsl(var(--foreground));\\n}\\n\\n.\\\\[\\\\&\\\\>svg\\\\~\\\\*\\\\]\\\\:pl-7>svg~* {\\n  padding-left: 1.75rem;\\n}\\n\\n.\\\\[\\\\&\\\\>tr\\\\]\\\\:last\\\\:border-b-0:last-child>tr {\\n  border-bottom-width: 0px;\\n}\\n\\n.\\\\[\\\\&_p\\\\]\\\\:leading-relaxed p {\\n  line-height: 1.625;\\n}\\n\\n.\\\\[\\\\&_tr\\\\:last-child\\\\]\\\\:border-0 tr:last-child {\\n  border-width: 0px;\\n}\\n\\n.\\\\[\\\\&_tr\\\\]\\\\:border-b tr {\\n  border-bottom-width: 1px;\\n}\\n\", \"\",{\"version\":3,\"sources\":[\"webpack://src/styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc;;AAAd;EAAA,wBAAc;EAAd,wBAAc;EAAd,mBAAc;EAAd,mBAAc;EAAd,cAAc;EAAd,cAAc;EAAd,cAAc;EAAd,eAAc;EAAd,eAAc;EAAd,aAAc;EAAd,aAAc;EAAd,kBAAc;EAAd,sCAAc;EAAd,8BAAc;EAAd,6BAAc;EAAd,4BAAc;EAAd,eAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,kBAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,sCAAc;EAAd,kCAAc;EAAd,2BAAc;EAAd,sBAAc;EAAd,8BAAc;EAAd,YAAc;EAAd,kBAAc;EAAd,gBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,cAAc;EAAd,gBAAc;EAAd,aAAc;EAAd,mBAAc;EAAd,qBAAc;EAAd,2BAAc;EAAd,yBAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,uBAAc;EAAd,wBAAc;EAAd,yBAAc;EAAd,sBAAc;EAAd,oBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd;AAAc,CAAd;;CAAc,CAAd;;;CAAc;;AAAd;;;EAAA,sBAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,mBAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,gBAAc;AAAA;;AAAd;;;;;;;;CAAc;;AAAd;;EAAA,gBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gBAAc,EAAd,MAAc;EAAd,cAAc;KAAd,WAAc,EAAd,MAAc;EAAd,+HAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,wCAAc,EAAd,MAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,SAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,yCAAc;UAAd,iCAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;EAAA,kBAAc;EAAd,oBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;EAAd,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,mBAAc;AAAA;;AAAd;;;;;CAAc;;AAAd;;;;EAAA,+GAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,+BAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,cAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,cAAc;EAAd,cAAc;EAAd,kBAAc;EAAd,wBAAc;AAAA;;AAAd;EAAA,eAAc;AAAA;;AAAd;EAAA,WAAc;AAAA;;AAAd;;;;CAAc;;AAAd;EAAA,cAAc,EAAd,MAAc;EAAd,qBAAc,EAAd,MAAc;EAAd,yBAAc,EAAd,MAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;EAAA,oBAAc,EAAd,MAAc;EAAd,8BAAc,EAAd,MAAc;EAAd,gCAAc,EAAd,MAAc;EAAd,eAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;EAAd,uBAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;EAAd,SAAc,EAAd,MAAc;EAAd,UAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,oBAAc;AAAA;;AAAd;;;CAAc;;AAAd;;;;EAAA,0BAAc,EAAd,MAAc;EAAd,6BAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,aAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,YAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,6BAAc,EAAd,MAAc;EAAd,oBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,wBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,0BAAc,EAAd,MAAc;EAAd,aAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,kBAAc;AAAA;;AAAd;;CAAc;;AAAd;;;;;;;;;;;;;EAAA,SAAc;AAAA;;AAAd;EAAA,SAAc;EAAd,UAAc;AAAA;;AAAd;EAAA,UAAc;AAAA;;AAAd;;;EAAA,gBAAc;EAAd,SAAc;EAAd,UAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,UAAc;AAAA;;AAAd;;CAAc;;AAAd;EAAA,gBAAc;AAAA;;AAAd;;;CAAc;;AAAd;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;EAAA,UAAc,EAAd,MAAc;EAAd,cAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;AAAA;;AAAd;;CAAc;AAAd;EAAA,eAAc;AAAA;;AAAd;;;;CAAc;;AAAd;;;;;;;;EAAA,cAAc,EAAd,MAAc;EAAd,sBAAc,EAAd,MAAc;AAAA;;AAAd;;CAAc;;AAAd;;EAAA,eAAc;EAAd,YAAc;AAAA;;AAAd,wEAAc;AAAd;EAAA,aAAc;AAAA;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd,kBAAc;EAAd,mBAAc;EAAd,sBAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,mBAAc;EAAd,sBAAc;AAAA;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd,iFAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,cAAc;EAAd;AAAc;;AAAd;EAAA,mPAAc;EAAd,wCAAc;EAAd,4BAAc;EAAd,4BAAc;EAAd,qBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,wBAAc;EAAd,sBAAc;EAAd,iCAAc;UAAd;AAAc;;AAAd;EAAA,wBAAc;KAAd,qBAAc;UAAd,gBAAc;EAAd,UAAc;EAAd,iCAAc;UAAd,yBAAc;EAAd,qBAAc;EAAd,sBAAc;EAAd,6BAAc;EAAd,yBAAc;KAAd,sBAAc;UAAd,iBAAc;EAAd,cAAc;EAAd,YAAc;EAAd,WAAc;EAAd,cAAc;EAAd,sBAAc;EAAd,qBAAc;EAAd,iBAAc;EAAd;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA;AAAc;;AAAd;EAAA,8BAAc;EAAd,mBAAc;EAAd,4CAAc;EAAd,2BAAc;EAAd,4BAAc;EAAd,wBAAc;EAAd,2GAAc;EAAd,yGAAc;EAAd;AAAc;;AAAd;EAAA,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd;AAAc;;AAAd;EAAA,sQAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,oKAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,uOAAc;EAAd,yBAAc;EAAd,8BAAc;EAAd,0BAAc;EAAd,2BAAc;EAAd,4BAAc;AAAA;;AAAd;;EAAA;IAAA,wBAAc;OAAd,qBAAc;YAAd;EAAc;AAAA;;AAAd;EAAA,yBAAc;EAAd;AAAc;;AAAd;EAAA,iBAAc;EAAd,qBAAc;EAAd,eAAc;EAAd,gBAAc;EAAd,UAAc;EAAd,gBAAc;EAAd;AAAc;;AAAd;EAAA,6BAAc;EAAd;AAAc;EAAd;IAAA,uBAAc;IAAd,4BAAc;IAAd,iBAAc;IAAd,iCAAc;IAAd,oBAAc;IAAd,oCAAc;IAAd,4BAAc;IAAd,iCAAc;IAAd,wBAAc;IAAd,sCAAc;IAAd,oBAAc;IAAd,qCAAc;IAAd,qBAAc;IAAd,mCAAc;IAAd,4BAAc;IAAd,qCAAc;IAAd,2BAAc;IAAd,0BAAc;IAAd,yBAAc;IAAd,gBAAc;EAAA;EAAd;EAAA;AAAc;EAAd;EAAA,wCAAc;EAAd;AAAc;AAEd;EAAA,kBAAmB;EAAnB,UAAmB;EAAnB,WAAmB;EAAnB,UAAmB;EAAnB,YAAmB;EAAnB,gBAAmB;EAAnB,sBAAmB;EAAnB,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,QAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,oBAAmB;EAAnB,4BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA;EAAmB;AAAA;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,yBAAmB;KAAnB,sBAAmB;UAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,sDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,oDAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,2DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,gEAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,+DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,8DAAmB;EAAnB;AAAmB;AAAnB;EAAA,uBAAmB;EAAnB,4DAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,gBAAmB;EAAnB,uBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,qCAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,qEAAmB;EAAnB;AAAmB;AAAnB;EAAA,4DAAmB;EAAnB,mEAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,sBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,qBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,iBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,mBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA,kBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,0EAAmB;EAAnB,8FAAmB;EAAnB;AAAmB;AAAnB;EAAA,+EAAmB;EAAnB,mGAAmB;EAAnB;AAAmB;AAAnB;EAAA,6EAAmB;EAAnB,iGAAmB;EAAnB;AAAmB;AAAnB;EAAA,0CAAmB;EAAnB,uDAAmB;EAAnB;AAAmB;AAAnB;EAAA,gFAAmB;EAAnB,oGAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,2GAAmB;EAAnB,yGAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA,oBAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA,6BAAmB;EAAnB,+QAAmB;EAAnB;AAAmB;AAAnB;EAAA,gKAAmB;EAAnB,wJAAmB;EAAnB,iLAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,wBAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+FAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,4BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,+BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA,8BAAmB;EAAnB,wDAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;;EAAA;IAAA,mCAAmB;IAAnB;EAAmB;AAAA;AAAnB;;EAAA;IAAA,kCAAmB;IAAnB;EAAmB;AAAA;AAAnB;EAAA,qBAAmB;EAAnB,yBAAmB;EAAnB,2BAAmB;EAAnB,yBAAmB;EAAnB,0BAAmB;EAAnB,+BAAmB;EAAnB;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;AAAnB;EAAA;AAAmB;;AA0DnB,2BAA2B;AAC3B;EACE,cAAc;AAChB;;AAEA;EACE,yBAAyB;AAC3B;;AAEA,qBAAqB;AACrB;EACE,UAAU;EACV,WAAW;AACb;;AAEA;EACE,6BAA6B;AAC/B;;AAEA;EACE,8BAA8B;EAC9B,kBAAkB;AACpB;;AAEA;EACE,wCAAwC;AAC1C;;AAtFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,mBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,4DAuFA;EAvFA,mEAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,+EAuFA;EAvFA,mGAuFA;EAvFA;AAuFA;;AAvFA;EAAA,6EAuFA;EAvFA,iGAuFA;EAvFA;AAuFA;;AAvFA;EAAA,gFAuFA;EAvFA,oGAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,8BAuFA;EAvFA;AAuFA;;AAvFA;EAAA,2GAuFA;EAvFA,yGAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,8BAuFA;EAvFA;AAuFA;;AAvFA;EAAA,2GAuFA;EAvFA,yGAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,yBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,0BAuFA;EAvFA;AAuFA;;AAvFA;EAAA,yBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,0BAuFA;EAvFA;AAuFA;;AAvFA;EAAA,yBAuFA;EAvFA;AAuFA;;AAvFA;EAAA,qBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,kBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,0CAuFA;EAvFA,uDAuFA;EAvFA;AAuFA;;AAvFA;EAAA,qBAuFA;EAvFA,yBAuFA;EAvFA,2BAuFA;EAvFA,yBAuFA;EAvFA,0BAuFA;EAvFA,+BAuFA;EAvFA;AAuFA;;AAvFA;EAAA,oBAuFA;EAvFA,yBAuFA;EAvFA,0BAuFA;EAvFA,wBAuFA;EAvFA,yBAuFA;EAvFA,8BAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;;EAAA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA,uBAuFA;IAvFA,sDAuFA;IAvFA;EAuFA;;EAvFA;IAAA,uBAuFA;IAvFA,2DAuFA;IAvFA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA,oBAuFA;IAvFA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA,iBAuFA;IAvFA;EAuFA;;EAvFA;IAAA,eAuFA;IAvFA;EAuFA;AAAA;;AAvFA;;EAAA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA,uBAuFA;IAvFA,2DAuFA;IAvFA;EAuFA;AAAA;;AAvFA;;EAAA;IAAA;EAuFA;;EAvFA;IAAA,QAuFA;IAvFA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA;EAuFA;;EAvFA;IAAA,kBAuFA;IAvFA;EAuFA;;EAvFA;IAAA;EAuFA;AAAA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA,gBAuFA;EAvFA,oBAuFA;EAvFA,4BAuFA;EAvFA;AAuFA;;AAvFA;EAAA,sBAuFA;EAvFA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA;;AAvFA;EAAA;AAuFA\",\"sourcesContent\":[\"@tailwind base;\\n@tailwind components;\\n@tailwind utilities;\\n\\n@layer base {\\n  :root {\\n    --background: 0 0% 100%;\\n    --foreground: 222.2 84% 4.9%;\\n    --card: 0 0% 100%;\\n    --card-foreground: 222.2 84% 4.9%;\\n    --popover: 0 0% 100%;\\n    --popover-foreground: 222.2 84% 4.9%;\\n    --primary: 221.2 83.2% 53.3%;\\n    --primary-foreground: 210 40% 98%;\\n    --secondary: 210 40% 96%;\\n    --secondary-foreground: 222.2 84% 4.9%;\\n    --muted: 210 40% 96%;\\n    --muted-foreground: 215.4 16.3% 46.9%;\\n    --accent: 210 40% 96%;\\n    --accent-foreground: 222.2 84% 4.9%;\\n    --destructive: 0 84.2% 60.2%;\\n    --destructive-foreground: 210 40% 98%;\\n    --border: 214.3 31.8% 91.4%;\\n    --input: 214.3 31.8% 91.4%;\\n    --ring: 221.2 83.2% 53.3%;\\n    --radius: 0.5rem;\\n  }\\n\\n  .dark {\\n    --background: 222.2 84% 4.9%;\\n    --foreground: 210 40% 98%;\\n    --card: 222.2 84% 4.9%;\\n    --card-foreground: 210 40% 98%;\\n    --popover: 222.2 84% 4.9%;\\n    --popover-foreground: 210 40% 98%;\\n    --primary: 217.2 91.2% 59.8%;\\n    --primary-foreground: 222.2 84% 4.9%;\\n    --secondary: 217.2 32.6% 17.5%;\\n    --secondary-foreground: 210 40% 98%;\\n    --muted: 217.2 32.6% 17.5%;\\n    --muted-foreground: 215 20.2% 65.1%;\\n    --accent: 217.2 32.6% 17.5%;\\n    --accent-foreground: 210 40% 98%;\\n    --destructive: 0 62.8% 30.6%;\\n    --destructive-foreground: 210 40% 98%;\\n    --border: 217.2 32.6% 17.5%;\\n    --input: 217.2 32.6% 17.5%;\\n    --ring: 224.3 76.3% 94.1%;\\n  }\\n}\\n\\n@layer base {\\n  * {\\n    @apply border-border;\\n  }\\n  body {\\n    @apply bg-background text-foreground;\\n  }\\n}\\n\\n/* RTL Support for Arabic */\\n[dir=\\\"rtl\\\"] {\\n  direction: rtl;\\n}\\n\\n[dir=\\\"rtl\\\"] .rtl\\\\:rotate-180 {\\n  transform: rotate(180deg);\\n}\\n\\n/* Custom scrollbar */\\n::-webkit-scrollbar {\\n  width: 6px;\\n  height: 6px;\\n}\\n\\n::-webkit-scrollbar-track {\\n  background: hsl(var(--muted));\\n}\\n\\n::-webkit-scrollbar-thumb {\\n  background: hsl(var(--border));\\n  border-radius: 3px;\\n}\\n\\n::-webkit-scrollbar-thumb:hover {\\n  background: hsl(var(--muted-foreground));\\n}\\n\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[6].oneOf[13].use[2]!./src/styles/globals.css\n"));

/***/ })

});