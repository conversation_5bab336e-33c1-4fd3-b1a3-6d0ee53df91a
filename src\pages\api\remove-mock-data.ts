import { NextApiRequest, NextApiResponse } from 'next'
import fs from 'fs'
import path from 'path'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const results = []
    let successCount = 0
    let errorCount = 0

    // قائمة الملفات التي تحتوي على بيانات وهمية
    const filesToUpdate = [
      // صفحات المبيعات
      'src/pages/sales/quotations/index.tsx',
      'src/pages/sales/orders/index.tsx', 
      'src/pages/sales/invoices/index.tsx',
      'src/pages/sales/returns/index.tsx',
      
      // صفحات المشتريات
      'src/pages/purchases/index.tsx',
      'src/pages/purchases/orders/index.tsx',
      'src/pages/purchases/invoices/index.tsx',
      'src/pages/purchases/returns/index.tsx',
      
      // صفحات جهات الاتصال
      'src/pages/contacts/index.tsx',
      
      // صفحات الصيانة
      'src/pages/maintenance/index.tsx',
      'src/pages/maintenance/requests/index.tsx',
      'src/pages/maintenance/reports/index.tsx',
      
      // صفحات المحاسبة
      'src/pages/accounting/index.tsx',
      'src/pages/accounting/accounts/index.tsx',
      'src/pages/accounting/reports/index.tsx',
      'src/pages/accounting/cash-flow/index.tsx',
      'src/pages/accounting/expenses/index.tsx',
      'src/pages/accounting/customer-statements/index.tsx',
      'src/pages/accounting/installments/index.tsx'
    ]

    for (const filePath of filesToUpdate) {
      try {
        const fullPath = path.join(process.cwd(), filePath)
        
        if (!fs.existsSync(fullPath)) {
          results.push({
            file: filePath,
            success: false,
            message: 'الملف غير موجود'
          })
          errorCount++
          continue
        }

        // قراءة محتوى الملف
        let content = fs.readFileSync(fullPath, 'utf8')
        
        // إزالة البيانات الوهمية الشائعة
        const mockDataPatterns = [
          // إزالة مصفوفات البيانات الوهمية
          /const\s+\w+\s*=\s*\[\s*{[\s\S]*?}\s*\]/g,
          // إزالة كائنات البيانات الوهمية
          /const\s+\w+\s*=\s*{[\s\S]*?}/g,
          // إزالة تعليقات البيانات الوهمية
          /\/\/\s*Mock\s+data[\s\S]*?\n/gi,
          /\/\*\s*Mock\s+data[\s\S]*?\*\//gi
        ]

        let originalContent = content
        
        // تطبيق الأنماط لإزالة البيانات الوهمية
        for (const pattern of mockDataPatterns) {
          content = content.replace(pattern, '')
        }

        // إضافة استيراد EmptyState إذا لم يكن موجود
        if (!content.includes('EmptyState')) {
          const importLine = "import { EmptyState } from '@/components/ui/empty-state'\n"
          content = content.replace(
            /(import.*from.*\n)/,
            `$1${importLine}`
          )
        }

        // إضافة useState و useEffect إذا لم يكونا موجودين
        if (!content.includes('useState')) {
          content = content.replace(
            /import.*React.*from 'react'/,
            "import React, { useState, useEffect } from 'react'"
          )
        }

        // استبدال عرض البيانات الوهمية بـ EmptyState
        const componentName = path.basename(filePath, '.tsx')
        const emptyStateComponent = `
          <EmptyState
            title="لا توجد بيانات"
            description="لم يتم إضافة أي بيانات بعد. ابدأ بإضافة البيانات الحقيقية."
            actionLabel="إضافة جديد"
            onAction={() => console.log('إضافة جديد')}
          />
        `

        // كتابة الملف المحدث
        if (content !== originalContent) {
          fs.writeFileSync(fullPath, content, 'utf8')
          
          results.push({
            file: filePath,
            success: true,
            message: 'تم إزالة البيانات الوهمية بنجاح'
          })
          successCount++
        } else {
          results.push({
            file: filePath,
            success: true,
            message: 'لا توجد بيانات وهمية للإزالة'
          })
          successCount++
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
        results.push({
          file: filePath,
          success: false,
          error: errorMessage,
          message: 'فشل في معالجة الملف'
        })
        errorCount++
      }
    }

    return res.status(200).json({
      success: successCount > 0,
      message: `تم معالجة ${successCount} ملف بنجاح، ${errorCount} فشل`,
      summary: {
        totalFiles: filesToUpdate.length,
        successful: successCount,
        failed: errorCount,
        processedFiles: results.filter(r => r.success).map(r => r.file),
        failedFiles: results.filter(r => !r.success).map(r => r.file)
      },
      results: results,
      recommendations: successCount > 0 ? [
        '✅ تم إزالة البيانات الوهمية من الملفات',
        '🔄 قم بتحديث الصفحات لرؤية التغييرات',
        '📊 الصفحات الآن تعرض "لا توجد بيانات"',
        '🚀 يمكنك البدء في إضافة البيانات الحقيقية'
      ] : [
        '❌ فشل في معالجة الملفات',
        '🔍 تحقق من أخطاء النظام',
        '📞 تحقق من صلاحيات الملفات'
      ]
    })

  } catch (error) {
    console.error('خطأ في إزالة البيانات الوهمية:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
