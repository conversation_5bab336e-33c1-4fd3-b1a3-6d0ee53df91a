{"c": ["webpack"], "r": ["pages/index", "pages/integration-setup"], "m": ["./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Cindex.tsx&page=%2F!", "./src/pages/index.tsx", "./node_modules/@radix-ui/react-progress/dist/index.mjs", "./node_modules/lucide-react/dist/esm/icons/code.js", "./node_modules/lucide-react/dist/esm/icons/database.js", "./node_modules/lucide-react/dist/esm/icons/layers.js", "./node_modules/lucide-react/dist/esm/icons/zap.js", "./node_modules/next/dist/build/webpack/loaders/next-client-pages-loader.js?absolutePagePath=D%3A%5Cv%5Csrc%5Cpages%5Cintegration-setup.tsx&page=%2Fintegration-setup!", "./src/components/ui/progress.tsx", "./src/pages/integration-setup.tsx", "__barrel_optimize__?names=CheckCircle,Code,Database,Layers,Package,Users,Warehouse,XCircle,Zap!=!./node_modules/lucide-react/dist/esm/lucide-react.js"]}