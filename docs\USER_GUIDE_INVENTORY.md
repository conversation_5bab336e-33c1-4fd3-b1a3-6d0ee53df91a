# 📦 دليل استخدام موديول إدارة المخازن

## 🎯 **نظرة عامة**
موديول إدارة المخازن هو نظام متكامل لإدارة المخزون والمنتجات، تتبع حركات المخزون، إدارة المخازن المتعددة، والتحكم في مستويات المخزون مع تكامل كامل مع المبيعات والمشتريات.

---

## 📋 **المكونات الرئيسية**

### 1. **إدارة المنتجات (Products Management)**
### 2. **إدارة المخازن (Warehouses Management)**  
### 3. **حركات المخزون (Stock Movements)**
### 4. **التحويلات بين المخازن (Stock Transfers)**
### 5. **تقارير المخزون (Inventory Reports)**

---

## 🏷️ **1. إدارة المنتجات (Products Management)**

### 📍 **الوصول**: `/inventory/products`

### ✨ **الميزات الرئيسية**
- قاعدة بيانات شاملة للمنتجات
- تصنيفات وفئات متعددة
- إدارة الأسعار والتكاليف
- تتبع مستويات المخزون
- دعم المنتجات المركبة

### 🔧 **كيفية الاستخدام**

#### **إضافة منتج جديد**
1. انقر على "منتج جديد"
2. **البيانات الأساسية**:
   - اسم المنتج (عربي/إنجليزي)
   - كود المنتج (SKU) - تلقائي أو يدوي
   - الوصف التفصيلي
   - الفئة والتصنيف
3. **بيانات التسعير**:
   - سعر التكلفة
   - سعر البيع
   - سعر الجملة (اختياري)
   - العملة
4. **بيانات المخزون**:
   - وحدة القياس (قطعة، كيلو، متر، إلخ)
   - الحد الأدنى للمخزون
   - الحد الأقصى للمخزون
   - نقطة إعادة الطلب
5. **معلومات إضافية**:
   - الباركود
   - تاريخ انتهاء الصلاحية (إن وجد)
   - الوزن والأبعاد
   - صورة المنتج
6. **حفظ المنتج**

#### **المنتجات المركبة (Composite Products)**
- منتج رئيسي يتكون من عدة مكونات
- مثال: لابتوب HP = جهاز أساسي + ذاكرة + تخزين
- يمكن بيع المكونات منفردة أو مجمعة
- تتبع منفصل لمخزون كل مكون

#### **إدارة الفئات والتصنيفات**
- **الفئات الرئيسية**: إلكترونيات، أجهزة كمبيوتر، إكسسوارات
- **الفئات الفرعية**: لابتوب، ديسكتوب، طابعات
- **التصنيفات**: حسب العلامة التجارية، النوع، الاستخدام

---

## 🏢 **2. إدارة المخازن (Warehouses Management)**

### 📍 **الوصول**: `/setup/warehouses`

### ✨ **الميزات الرئيسية**
- مخازن متعددة لكل فرع
- تتبع منفصل لكل مخزن
- إدارة المواقع داخل المخزن
- صلاحيات الوصول للمخازن

### 🔧 **كيفية الاستخدام**

#### **إنشاء مخزن جديد**
1. انقر "مخزن جديد"
2. **البيانات الأساسية**:
   - اسم المخزن
   - كود المخزن
   - الفرع التابع له
   - العنوان والموقع
3. **بيانات التشغيل**:
   - نوع المخزن (رئيسي، فرعي، مؤقت)
   - السعة التخزينية
   - المسؤول عن المخزن
   - ساعات العمل
4. **الإعدادات**:
   - السماح بالمخزون السالب
   - طريقة تقييم المخزون (FIFO, LIFO, متوسط)
   - مستوى الأمان المطلوب

#### **إدارة المواقع داخل المخزن**
- **الممرات (Aisles)**: A, B, C
- **الرفوف (Shelves)**: 1, 2, 3
- **المستويات (Levels)**: أعلى، وسط، أسفل
- **مثال موقع**: A-2-وسط

---

## 📊 **3. حركات المخزون (Stock Movements)**

### 📍 **الوصول**: `/inventory/movements`

### ✨ **الميزات الرئيسية**
- تتبع جميع حركات المخزون
- تسجيل تلقائي من المبيعات والمشتريات
- حركات يدوية للتعديلات
- تاريخ كامل لكل منتج

### 🔧 **أنواع الحركات**

#### **الحركات التلقائية**
- 📈 **شراء**: زيادة المخزون من فواتير المشتريات
- 📉 **بيع**: نقص المخزون من فواتير المبيعات
- 🔒 **حجز**: حجز كمية لأوامر البيع
- 🔓 **إلغاء حجز**: إلغاء حجز عند إلغاء الأمر
- 🔄 **إرجاع**: زيادة/نقص من المرتجعات
- 🔧 **صيانة**: استخدام قطع غيار في الصيانة

#### **الحركات اليدوية**
- ➕ **إضافة مخزون**: زيادة يدوية
- ➖ **خصم مخزون**: نقص يدوي
- 🔄 **تعديل**: تصحيح الكميات
- 📦 **جرد**: تسوية بعد الجرد الفعلي

#### **إنشاء حركة يدوية**
1. انقر "حركة جديدة"
2. **بيانات الحركة**:
   - نوع الحركة
   - المخزن
   - تاريخ الحركة
   - المرجع (رقم المستند)
3. **المنتجات**:
   - اختيار المنتج
   - الكمية (موجبة للزيادة، سالبة للنقص)
   - السبب والملاحظات
4. **تأكيد الحركة**

---

## 🔄 **4. التحويلات بين المخازن (Stock Transfers)**

### 📍 **الوصول**: `/inventory/transfers`

### ✨ **الميزات الرئيسية**
- نقل المخزون بين المخازن
- تتبع حالة التحويل
- تأكيد الاستلام
- تقارير التحويلات

### 🔧 **كيفية الاستخدام**

#### **إنشاء تحويل جديد**
1. انقر "تحويل جديد"
2. **بيانات التحويل**:
   - المخزن المرسل
   - المخزن المستقبل
   - تاريخ التحويل
   - سبب التحويل
3. **المنتجات المحولة**:
   - اختيار المنتجات
   - الكميات المحولة
   - التحقق من التوفر
4. **إرسال التحويل**

#### **حالات التحويل**
- 📝 **مسودة**: قيد التحضير
- 📤 **مرسل**: تم إرسال البضاعة
- 🚚 **في الطريق**: قيد النقل
- 📦 **مستلم**: تم الاستلام والتأكيد
- ❌ **ملغي**: تم إلغاء التحويل

#### **تأكيد الاستلام**
1. المخزن المستقبل يراجع التحويل
2. التحقق من الكميات المستلمة
3. تسجيل أي اختلافات
4. تأكيد الاستلام

---

## 📊 **5. تقارير المخزون (Inventory Reports)**

### 📍 **الوصول**: `/inventory/reports`

### 📈 **التقارير المتاحة**

#### **تقرير المخزون الحالي**
- كميات المخزون لكل منتج
- القيمة الإجمالية للمخزون
- المنتجات تحت الحد الأدنى
- المنتجات منتهية الصلاحية

#### **تقرير حركات المخزون**
- جميع الحركات خلال فترة محددة
- تصنيف حسب نوع الحركة
- تفاصيل كل حركة
- الأرصدة قبل وبعد كل حركة

#### **تقرير التحويلات**
- التحويلات بين المخازن
- حالة كل تحويل
- الكميات المحولة والمستلمة
- أي اختلافات في الكميات

#### **تقرير تقييم المخزون**
- قيمة المخزون حسب التكلفة
- قيمة المخزون حسب سعر البيع
- نسبة الربح المتوقعة
- تحليل بطيء الحركة

#### **تقرير ABC Analysis**
- تصنيف المنتجات حسب الأهمية
- **فئة A**: منتجات عالية القيمة (20% من المنتجات، 80% من القيمة)
- **فئة B**: منتجات متوسطة القيمة
- **فئة C**: منتجات منخفضة القيمة

---

## 🎯 **ميزات متقدمة**

### 🔍 **الجرد الدوري**
- جدولة جرد دوري للمنتجات
- مقارنة الجرد الفعلي مع النظام
- تسوية الاختلافات تلقائياً
- تقارير دقة الجرد

### 📱 **الباركود والـ QR Code**
- إنشاء باركود لكل منتج
- مسح الباركود للبحث السريع
- تسريع عمليات الاستلام والإرسال
- تقليل الأخطاء البشرية

### 🔔 **التنبيهات التلقائية**
- تنبيه عند الوصول للحد الأدنى
- إشعار انتهاء الصلاحية
- تحذير المخزون السالب
- تقارير دورية تلقائية

### 📊 **تحليلات متقدمة**
- معدل دوران المخزون
- متوسط فترة البقاء في المخزن
- تحليل الطلب والتنبؤ
- تحسين مستويات المخزون

---

## 🎯 **نصائح للاستخدام الأمثل**

### ✅ **أفضل الممارسات**
1. **حدد الحد الأدنى والأقصى** لكل منتج
2. **راجع التقارير** بانتظام
3. **نفذ جرد دوري** شهري أو ربع سنوي
4. **استخدم الباركود** لتسريع العمليات
5. **درب الموظفين** على النظام

### ⚠️ **تجنب هذه الأخطاء**
- عدم تحديث بيانات المنتجات
- تجاهل التنبيهات
- عدم تسجيل الحركات اليدوية
- إهمال تأكيد التحويلات

### 🔧 **اختصارات مفيدة**
- **Ctrl + F**: البحث السريع
- **Ctrl + N**: إضافة جديد
- **F5**: تحديث البيانات
- **Ctrl + P**: طباعة التقرير

---

## 🔗 **الروابط السريعة**

- **المنتجات**: `/inventory/products`
- **حركات المخزون**: `/inventory/movements`
- **التحويلات**: `/inventory/transfers`
- **المخازن**: `/setup/warehouses`
- **تقارير المخزون**: `/inventory/reports`

---

## 🆘 **الدعم والمساعدة**

في حالة وجود أي مشاكل أو استفسارات:
1. راجع هذا الدليل أولاً
2. تحقق من الصلاحيات المطلوبة
3. تأكد من صحة البيانات
4. تواصل مع فريق الدعم الفني

---

**💡 ملاحظة**: إدارة المخزون هي قلب نظام ERP، وجميع الموديولات الأخرى تعتمد عليها. لذلك من المهم جداً الحفاظ على دقة البيانات وتحديثها باستمرار.
