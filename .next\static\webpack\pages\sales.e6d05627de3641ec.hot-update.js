"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/sales",{

/***/ "./src/pages/sales/index.tsx":
/*!***********************************!*\
  !*** ./src/pages/sales/index.tsx ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SalesOverview; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth */ \"./src/lib/auth.ts\");\n/* harmony import */ var _components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/layout/Layout */ \"./src/components/layout/Layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/table */ \"./src/components/ui/table.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Clock,DollarSign,FileText,RotateCcw,ShoppingCart,TrendingDown,TrendingUp,Users!=!lucide-react */ \"__barrel_optimize__?names=ArrowRight,Clock,DollarSign,FileText,RotateCcw,ShoppingCart,TrendingDown,TrendingUp,Users!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction SalesOverview() {\n    _s();\n    const { user } = (0,_hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth)();\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [salesData, setSalesData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        orders: [],\n        invoices: [],\n        customers: [],\n        stats: {\n            totalQuotations: 0,\n            pendingQuotations: 0,\n            approvedQuotations: 0,\n            expiredQuotations: 0,\n            totalOrders: 0,\n            pendingOrders: 0,\n            confirmedOrders: 0,\n            shippedOrders: 0,\n            totalInvoices: 0,\n            pendingInvoices: 0,\n            paidInvoices: 0,\n            overdueInvoices: 0,\n            totalReturns: 0,\n            pendingReturns: 0,\n            processedReturns: 0,\n            totalValue: 0,\n            paidValue: 0,\n            pendingValue: 0,\n            returnValue: 0\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchSalesData();\n    }, []);\n    const fetchSalesData = async ()=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/postgres/data?type=sales-orders\");\n            const result = await response.json();\n            if (result.success) {\n                // حساب الإحصائيات من البيانات الحقيقية\n                const orders = result.data || [];\n                const stats = {\n                    totalQuotations: 0,\n                    pendingQuotations: 0,\n                    approvedQuotations: 0,\n                    expiredQuotations: 0,\n                    totalOrders: orders.length,\n                    pendingOrders: orders.filter((o)=>o.order_status === \"pending\").length,\n                    confirmedOrders: orders.filter((o)=>o.order_status === \"confirmed\").length,\n                    shippedOrders: orders.filter((o)=>o.order_status === \"shipped\").length,\n                    totalInvoices: 0,\n                    pendingInvoices: 0,\n                    paidInvoices: 0,\n                    overdueInvoices: 0,\n                    totalReturns: 0,\n                    pendingReturns: 0,\n                    processedReturns: 0,\n                    totalValue: orders.reduce((sum, order)=>sum + (order.final_amount || 0), 0),\n                    paidValue: orders.filter((o)=>o.payment_status === \"paid\").reduce((sum, order)=>sum + (order.final_amount || 0), 0),\n                    pendingValue: orders.filter((o)=>o.payment_status === \"pending\").reduce((sum, order)=>sum + (order.final_amount || 0), 0),\n                    returnValue: 0\n                };\n                setSalesData({\n                    orders,\n                    invoices: [],\n                    customers: [],\n                    stats\n                });\n            }\n        } catch (error) {\n            console.error(\"خطأ في جلب بيانات المبيعات:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Recent activities\n    const recentQuotations = [\n        {\n            id: \"1\",\n            quotation_number: \"QT-2024-001\",\n            customer_name: \"شركة التقنية المتقدمة\",\n            status: \"pending\",\n            total_amount: 83000,\n            created_at: \"2024-01-25\"\n        },\n        {\n            id: \"2\",\n            quotation_number: \"QT-2024-002\",\n            customer_name: \"مؤسسة الإلكترونيات الحديثة\",\n            status: \"approved\",\n            total_amount: 50300,\n            created_at: \"2024-01-24\"\n        },\n        {\n            id: \"3\",\n            quotation_number: \"QT-2024-003\",\n            customer_name: \"شركة الأجهزة الذكية\",\n            status: \"expired\",\n            total_amount: 35980,\n            created_at: \"2024-01-23\"\n        }\n    ];\n    const recentInvoices = [\n        {\n            id: \"1\",\n            invoice_number: \"INV-2024-001\",\n            customer_name: \"شركة التقنية المتقدمة\",\n            status: \"pending\",\n            total_amount: 83000,\n            due_date: \"2024-02-20\"\n        },\n        {\n            id: \"2\",\n            invoice_number: \"INV-2024-002\",\n            customer_name: \"مؤسسة الإلكترونيات الحديثة\",\n            status: \"paid\",\n            total_amount: 50300,\n            due_date: \"2024-02-05\"\n        },\n        {\n            id: \"3\",\n            invoice_number: \"INV-2024-003\",\n            customer_name: \"شركة الأجهزة الذكية\",\n            status: \"overdue\",\n            total_amount: 35980,\n            due_date: \"2024-01-25\"\n        }\n    ];\n    const topCustomers = [\n        {\n            name: \"شركة التقنية المتقدمة\",\n            orders: 45,\n            value: 890000\n        },\n        {\n            name: \"مؤسسة الإلكترونيات الحديثة\",\n            orders: 38,\n            value: 720000\n        },\n        {\n            name: \"شركة الأجهزة الذكية\",\n            orders: 32,\n            value: 580000\n        },\n        {\n            name: \"متجر الكمبيوتر المتقدم\",\n            orders: 28,\n            value: 450000\n        },\n        {\n            name: \"شركة الحلول التقنية\",\n            orders: 25,\n            value: 380000\n        }\n    ];\n    // Helper functions\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"في الانتظار\";\n            case \"approved\":\n                return \"معتمد\";\n            case \"confirmed\":\n                return \"مؤكد\";\n            case \"shipped\":\n                return \"تم الشحن\";\n            case \"paid\":\n                return \"مدفوعة\";\n            case \"overdue\":\n                return \"متأخرة\";\n            case \"expired\":\n                return \"منتهية الصلاحية\";\n            default:\n                return \"غير محدد\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"pending\":\n                return \"bg-yellow-100 text-yellow-800\";\n            case \"approved\":\n                return \"bg-blue-100 text-blue-800\";\n            case \"confirmed\":\n                return \"bg-green-100 text-green-800\";\n            case \"shipped\":\n                return \"bg-green-100 text-green-800\";\n            case \"paid\":\n                return \"bg-green-100 text-green-800\";\n            case \"overdue\":\n                return \"bg-red-100 text-red-800\";\n            case \"expired\":\n                return \"bg-gray-100 text-gray-800\";\n            default:\n                return \"bg-gray-100 text-gray-800\";\n        }\n    };\n    if (!(0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(user, _lib_auth__WEBPACK_IMPORTED_MODULE_3__.PERMISSIONS.SALES_VIEW)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-900\",\n                        children: \"غير مصرح\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                        lineNumber: 209,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"ليس لديك صلاحية لعرض وحدة المبيعات\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                lineNumber: 208,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n            lineNumber: 207,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_Layout__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold tracking-tight\",\n                                    children: \"المبيعات\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-muted-foreground\",\n                                    children: \"لوحة تحكم شاملة لإدارة المبيعات والعملاء\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex space-x-2 space-x-reverse\",\n                            children: (0,_lib_auth__WEBPACK_IMPORTED_MODULE_3__.hasPermission)(user, _lib_auth__WEBPACK_IMPORTED_MODULE_3__.PERMISSIONS.SALES_CREATE) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                        href: \"/sales/quotations\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            variant: \"outline\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"عرض سعر جديد\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 231,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                        href: \"/sales/invoices\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 21\n                                                }, this),\n                                                \"فاتورة مبيعات جديدة\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 237,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 220,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/sales/quotations\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"عروض الأسعار\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-4 w-4 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: salesData.stats.totalQuotations\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    salesData.stats.pendingQuotations,\n                                                    \" في الانتظار\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 255,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/sales/orders\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"طلبات المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.ShoppingCart, {\n                                                className: \"h-4 w-4 text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 266,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: salesData.stats.totalOrders\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    salesData.stats.pendingOrders,\n                                                    \" في الانتظار\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 272,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 270,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 265,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 264,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/sales/invoices\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"فواتير المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 282,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.FileText, {\n                                                className: \"h-4 w-4 text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 281,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: salesData.stats.totalInvoices\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    salesData.stats.pendingInvoices,\n                                                    \" تحتاج دفع\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 285,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 280,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 279,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                            href: \"/sales/returns\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                                className: \"hover:shadow-md transition-shadow cursor-pointer\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                        className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                                className: \"text-sm font-medium\",\n                                                children: \"مرتجعات المبيعات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.RotateCcw, {\n                                                className: \"h-4 w-4 text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-2xl font-bold\",\n                                                children: salesData.stats.totalReturns\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: [\n                                                    salesData.stats.pendingReturns,\n                                                    \" في الانتظار\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 302,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 295,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 294,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 248,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"إجمالي قيمة المبيعات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingUp, {\n                                            className: \"h-4 w-4 text-muted-foreground\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 315,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(salesData.stats.totalValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"إجمالي المبيعات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 319,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"المبلغ المحصل\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 325,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.DollarSign, {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 326,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-green-600\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(salesData.stats.paidValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                salesData.stats.totalValue > 0 ? Math.round(salesData.stats.paidValue / salesData.stats.totalValue * 100) : 0,\n                                                \"% من الإجمالي\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 330,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"المبلغ المستحق\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Clock, {\n                                            className: \"h-4 w-4 text-yellow-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 337,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-yellow-600\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(salesData.stats.pendingValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: \"يجب تحصيله\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 336,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between space-y-0 pb-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            className: \"text-sm font-medium\",\n                                            children: \"قيمة المرتجعات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 349,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.TrendingDown, {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 350,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl font-bold text-red-600\",\n                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(salesData.stats.returnValue)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                salesData.stats.totalValue > 0 ? Math.round(salesData.stats.returnValue / salesData.stats.totalValue * 100) : 0,\n                                                \"% من الإجمالي\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"أحدث عروض الأسعار\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 366,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                            href: \"/sales/quotations\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.ArrowRight, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 365,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentQuotations.map((quotation)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: quotation.quotation_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: quotation.customer_name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatDate)(quotation.created_at)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 381,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 378,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(quotation.total_amount)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 384,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(quotation.status)),\n                                                                children: getStatusText(quotation.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 385,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, quotation.id, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 374,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                                    className: \"flex flex-row items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                            children: \"أحدث فواتير المبيعات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 398,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                            href: \"/sales/invoices\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                children: [\n                                                    \"عرض الكل\",\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.ArrowRight, {\n                                                        className: \"h-4 w-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 400,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 399,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: recentInvoices.map((invoice)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-3 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: invoice.invoice_number\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 411,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: invoice.customer_name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 412,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-400\",\n                                                                children: [\n                                                                    \"استحقاق: \",\n                                                                    (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatDate)(invoice.due_date)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium\",\n                                                                children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(invoice.total_amount)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(getStatusColor(invoice.status)),\n                                                                children: getStatusText(invoice.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 415,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, invoice.id, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardTitle, {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Clock_DollarSign_FileText_RotateCcw_ShoppingCart_TrendingDown_TrendingUp_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__.Users, {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: \"أهم العملاء\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_6__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-md border\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.Table, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"اسم العميل\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 441,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"عدد الطلبات\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 442,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"إجمالي القيمة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 443,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableHead, {\n                                                        children: \"متوسط قيمة الطلب\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                lineNumber: 440,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 439,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableBody, {\n                                            children: topCustomers.map((customer, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableRow, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-blue-600 font-medium text-sm\",\n                                                                            children: index + 1\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                            lineNumber: 453,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                        lineNumber: 452,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"font-medium\",\n                                                                        children: customer.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                        lineNumber: 455,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                                lineNumber: 451,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                            lineNumber: 450,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: customer.orders\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                            lineNumber: 458,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            className: \"font-medium\",\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(customer.value)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                            lineNumber: 459,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_7__.TableCell, {\n                                                            children: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.formatCurrency)(customer.value / customer.orders)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                            lineNumber: 447,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                    lineNumber: 438,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                            lineNumber: 436,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n                    lineNumber: 429,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n            lineNumber: 218,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\sales\\\\index.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(SalesOverview, \"24jh74yxpinBrtga+B56cbxIiZ8=\", false, function() {\n    return [\n        _hooks_useAuth__WEBPACK_IMPORTED_MODULE_2__.useAuth\n    ];\n});\n_c = SalesOverview;\nvar _c;\n$RefreshReg$(_c, \"SalesOverview\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/sales/index.tsx\n"));

/***/ })

});