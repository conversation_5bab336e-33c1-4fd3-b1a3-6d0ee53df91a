import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Textarea } from '@/components/ui/textarea'
import {
  CheckCircle,
  XCircle,
  AlertTriangle,
  Database,
  Play,
  Copy
} from 'lucide-react'

export default function DebugDatabase() {
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [setupLoading, setSetupLoading] = useState(false)
  const [sql, setSql] = useState(`-- اختبار بسيط
SELECT NOW() as current_time, 'اختبار الاتصال' as message;`)

  const testQueries = [
    {
      name: 'اختبار الاتصال',
      sql: `SELECT NOW() as current_time, 'اختبار الاتصال' as message;`
    },
    {
      name: 'فحص الجداول الموجودة',
      sql: `SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;`
    },
    {
      name: 'إنشاء نوع مخصص',
      sql: `DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN
        CREATE TYPE user_role AS ENUM ('admin', 'manager', 'employee', 'cashier');
    END IF;
END $$;`
    },
    {
      name: 'إنشاء جدول الفروع',
      sql: `CREATE TABLE IF NOT EXISTS branches (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    address TEXT NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);`
    },
    {
      name: 'إدراج بيانات تجريبية',
      sql: `INSERT INTO branches (name, address, phone, email)
VALUES ('الفرع الرئيسي', 'العنوان الرئيسي للشركة', '+201234567890', '<EMAIL>')
ON CONFLICT DO NOTHING;`
    }
  ]

  const executeQuery = async (queryToRun?: string) => {
    setLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/postgres/single-query', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          sql: queryToRun || sql
        })
      })

      const data = await response.json()
      setResult(data)
    } catch (err) {
      setResult({
        success: false,
        message: 'خطأ في الاتصال بالخادم',
        error: err instanceof Error ? err.message : 'Unknown error'
      })
    } finally {
      setLoading(false)
    }
  }

  const setupSimple = async () => {
    setSetupLoading(true)
    setResult(null)

    try {
      const response = await fetch('/api/postgres/setup-simple', {
        method: 'POST'
      })

      const data = await response.json()
      setResult(data)
    } catch (err) {
      setResult({
        success: false,
        message: 'خطأ في تنفيذ الإعداد المبسط',
        error: err instanceof Error ? err.message : 'Unknown error'
      })
    } finally {
      setSetupLoading(false)
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
  }

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            تشخيص قاعدة البيانات PostgreSQL
          </h1>
          <p className="text-gray-600">
            اختبار الاستعلامات خطوة بخطوة لفهم المشكلة
          </p>
        </div>

        {/* Quick Setup */}
        <Card>
          <CardHeader>
            <CardTitle>إعداد سريع</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex gap-4 mb-4">
              <Button
                onClick={setupSimple}
                disabled={setupLoading}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                {setupLoading ? (
                  <Database className="h-4 w-4 mr-2 animate-pulse" />
                ) : (
                  <Database className="h-4 w-4 mr-2" />
                )}
                {setupLoading ? 'جاري الإعداد المبسط...' : 'إعداد مبسط للجداول'}
              </Button>
            </div>
            <p className="text-sm text-gray-600">
              سيتم إنشاء الجداول الأساسية: branches, warehouses, users, products
            </p>
          </CardContent>
        </Card>

        {/* Quick Tests */}
        <Card>
          <CardHeader>
            <CardTitle>اختبارات سريعة</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {testQueries.map((test, index) => (
                <Button
                  key={index}
                  variant="outline"
                  onClick={() => executeQuery(test.sql)}
                  disabled={loading}
                  className="h-auto p-4 text-right"
                >
                  <div>
                    <div className="font-medium">{test.name}</div>
                    <div className="text-xs text-gray-500 mt-1">
                      {test.sql.substring(0, 50)}...
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Custom Query */}
        <Card>
          <CardHeader>
            <CardTitle>استعلام مخصص</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Textarea
              value={sql}
              onChange={(e) => setSql(e.target.value)}
              placeholder="أدخل استعلام SQL هنا..."
              rows={8}
              className="font-mono text-sm"
            />
            <div className="flex gap-2">
              <Button
                onClick={() => executeQuery()}
                disabled={loading}
                className="flex items-center"
              >
                {loading ? (
                  <Database className="h-4 w-4 mr-2 animate-pulse" />
                ) : (
                  <Play className="h-4 w-4 mr-2" />
                )}
                {loading ? 'جاري التنفيذ...' : 'تنفيذ الاستعلام'}
              </Button>
              <Button
                variant="outline"
                onClick={() => copyToClipboard(sql)}
                className="flex items-center"
              >
                <Copy className="h-4 w-4 mr-2" />
                نسخ
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Result */}
        {result && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                {result.success ? (
                  <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                ) : (
                  <XCircle className="h-5 w-5 mr-2 text-red-600" />
                )}
                نتيجة الاستعلام
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                <AlertDescription>
                  <div className="space-y-2">
                    <p className="font-medium">{result.message}</p>

                    {result.error && (
                      <div className="bg-red-100 p-3 rounded text-sm">
                        <strong>خطأ:</strong> {JSON.stringify(result.error, null, 2)}
                      </div>
                    )}

                    {result.data && result.data.length > 0 && (
                      <div className="bg-green-100 p-3 rounded text-sm">
                        <strong>البيانات:</strong>
                        <pre className="mt-2 overflow-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </div>
                    )}

                    {result.rowCount !== undefined && (
                      <p className="text-sm">عدد الصفوف المتأثرة: {result.rowCount}</p>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        )}

        {/* Database Info */}
        <Card>
          <CardHeader>
            <CardTitle>معلومات قاعدة البيانات</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-gray-600">المضيف</p>
                <p className="font-medium">{process.env.NEXT_PUBLIC_DB_HOST || 'localhost'}</p>
              </div>
              <div>
                <p className="text-gray-600">المنفذ</p>
                <p className="font-medium">{process.env.NEXT_PUBLIC_DB_PORT || '5432'}</p>
              </div>
              <div>
                <p className="text-gray-600">قاعدة البيانات</p>
                <p className="font-medium">{process.env.NEXT_PUBLIC_DB_NAME || 'Vero_ERP_ABA'}</p>
              </div>
              <div>
                <p className="text-gray-600">المستخدم</p>
                <p className="font-medium">{process.env.NEXT_PUBLIC_DB_USER || 'openpg'}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
