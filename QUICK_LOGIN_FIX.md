# حل مشكلة تعطيل حقول تسجيل الدخول

## المشكلة
حقول البريد الإلكتروني وكلمة المرور معطلة في صفحة تسجيل الدخول لأن النظام يتطلب إعداد أولي.

## الحلول المتاحة

### الحل الأول: إعداد النظام (الموصى به)

1. **انقر على "بدء الإعداد الآن"** في التنبيه الظاهر في صفحة تسجيل الدخول
2. **أو اذهب مباشرة إلى**: `http://localhost:3000/setup`

#### خطوات الإعداد:

**الخطوة 1: فحص قاعدة البيانات**
- سيتم فحص الاتصال تلقائياً
- تأكد من أن ملفات migration مطبقة في Supabase

**الخطوة 2: إنشاء المدير الأولي**
- أدخل البيانات التالية:
  - **الاسم الكامل**: مدير النظام
  - **البريد الإلكتروني**: <EMAIL>
  - **كلمة المرور**: كلمة مرور قوية (6 أحرف على الأقل)
  - **تأكيد كلمة المرور**: نفس كلمة المرور

**الخطوة 3: إعداد البيانات الأولية**
- سيتم إنشاء الفروع والمخازن والمنتجات التجريبية

**الخطوة 4: اكتمال الإعداد**
- سيتم توجيهك لصفحة تسجيل الدخول

### الحل الثاني: تسجيل الدخول بدون إعداد

1. **انقر على "تسجيل الدخول بدون إعداد"** في التنبيه
2. **أدخل بيانات مستخدم موجود مسبقاً**

## متطلبات قاعدة البيانات

تأكد من تطبيق ملفات migration في Supabase SQL Editor:

1. **انسخ محتوى** `supabase/migrations/20240101000000_initial_schema.sql`
2. **الصق في SQL Editor** في Supabase واضغط Run
3. **انسخ محتوى** `supabase/migrations/20240130000000_maintenance_system.sql`
4. **الصق في SQL Editor** في Supabase واضغط Run

## متطلبات متغيرات البيئة

**⚠️ مهم جداً**: يجب إعداد متغيرات البيئة أولاً!

1. **اذهب إلى مشروع Supabase الخاص بك**
2. **انسخ القيم من Settings > API**
3. **عدّل ملف `.env.local`** واستبدل القيم الوهمية:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-actual-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key
```

**بدون هذه القيم الصحيحة، النظام لن يعمل!**

## استكشاف الأخطاء

### خطأ: "relation users does not exist"
**الحل**: تطبيق ملفات migration في Supabase

### خطأ: "Invalid login credentials"
**الحل**: إنشاء مستخدم من خلال صفحة الإعداد

### خطأ: "Missing Supabase environment variables"
**الحل**: إعداد متغيرات البيئة في `.env.local`

## بيانات تسجيل الدخول الافتراضية

بعد إكمال الإعداد، يمكنك تسجيل الدخول بـ:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: الكلمة التي أدخلتها في الإعداد

## ملاحظات مهمة

1. **أمان**: غير كلمة المرور الافتراضية بعد أول تسجيل دخول
2. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية من قاعدة البيانات
3. **الصلاحيات**: تأكد من تفعيل Row Level Security في Supabase
