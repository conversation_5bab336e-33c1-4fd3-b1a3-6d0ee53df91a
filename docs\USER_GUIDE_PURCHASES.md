# 🛒 دليل استخدام موديول المشتريات

## 🎯 **نظرة عامة**
موديول المشتريات هو نظام متكامل لإدارة جميع عمليات الشراء من طلبات الشراء حتى فواتير الموردين والمرتجعات، مع تكامل كامل مع المخزون والمحاسبة.

---

## 📋 **المكونات الرئيسية**

### 1. **طلبات الشراء (Purchase Orders)**
### 2. **فواتير المشتريات (Purchase Invoices)**  
### 3. **مرتجعات المشتريات (Purchase Returns)**
### 4. **إدارة الموردين (Suppliers Management)**

---

## 📝 **1. طلبات الشراء (Purchase Orders)**

### 📍 **الوصول**: `/purchases/orders`

### ✨ **الميزات الرئيسية**
- إنشاء طلبات شراء للموردين
- تتبع حالة الطلبات
- تحويل الطلبات إلى فواتير
- إدارة تواريخ التسليم المتوقعة

### 🔧 **كيفية الاستخدام**

#### **إنشاء طلب شراء جديد**
1. انقر على "طلب شراء جديد"
2. **بيانات المورد**:
   - اختر المورد من القائمة
   - أو أدخل بيانات مورد جديد
   - سيتم ملء البيانات تلقائياً (الاسم، الهاتف، العنوان)
3. **بيانات الطلب**:
   - تاريخ الطلب (تلقائي)
   - تاريخ التسليم المتوقع
   - الفرع والمخزن المستقبل
   - شروط الدفع
4. **إضافة المنتجات**:
   - انقر "إضافة منتج"
   - اختر المنتج من القائمة أو أدخل منتج جديد
   - حدد الكمية المطلوبة
   - أدخل سعر الشراء
   - حدد معدل الضريبة (إن وجدت)
5. **الخصومات والإجماليات**:
   - خصم على كل منتج منفرد
   - خصم إجمالي على الطلب
   - حساب الضرائب
   - الإجمالي النهائي
6. **حفظ الطلب**

#### **إدارة طلبات الشراء**
- **عرض القائمة**: جميع الطلبات مع الفلترة والبحث
- **تعديل الطلب**: تحديث البيانات والمنتجات
- **تغيير الحالة**: مسودة → مرسل → مؤكد → مستلم
- **تحويل لفاتورة**: عند استلام البضاعة
- **طباعة/إرسال**: للمورد

#### **حالات طلبات الشراء**
- 📝 **مسودة**: قيد التحضير
- 📤 **مرسل**: تم إرساله للمورد
- ✅ **مؤكد**: أكد المورد الطلب
- 📦 **مستلم جزئياً**: استلام جزء من الطلب
- ✅ **مستلم بالكامل**: تم الاستلام الكامل
- ❌ **ملغي**: تم إلغاء الطلب

---

## 🧾 **2. فواتير المشتريات (Purchase Invoices)**

### 📍 **الوصول**: `/purchases/invoices`

### ✨ **الميزات الرئيسية**
- إنشاء فواتير من طلبات الشراء
- إضافة المخزون تلقائياً
- تكامل مع المحاسبة
- إدارة طرق الدفع

### 🔧 **كيفية الاستخدام**

#### **إنشاء فاتورة شراء جديدة**
1. **من طلب شراء**: انقر "إنشاء فاتورة"
2. **إنشاء مباشر**: انقر "فاتورة شراء جديدة"
3. **بيانات الفاتورة**:
   - بيانات المورد
   - رقم فاتورة المورد
   - تاريخ الفاتورة
   - تاريخ الاستحقاق
   - طريقة الدفع
   - الفرع والمخزن
4. **المنتجات والأسعار**:
   - الكميات المستلمة
   - أسعار الشراء
   - الخصومات
   - الضرائب
5. **مراجعة الإجماليات**
6. **حفظ وترحيل**

#### **التأثير على الأنظمة الأخرى**
- 📦 **المخزون**: إضافة الكميات تلقائياً
- 💰 **المحاسبة**: إضافة معاملة في كشف المورد
- 💳 **التدفق النقدي**: تسجيل المصروف
- 📊 **التقارير**: تحديث إحصائيات المشتريات

#### **طرق الدفع**
- 💵 **نقدي**: دفع فوري
- 📅 **آجل**: إضافة للذمم الدائنة
- 🏦 **بنكي**: تحويل بنكي
- 📝 **شيك**: دفع بشيك

---

## 🔄 **3. مرتجعات المشتريات (Purchase Returns)**

### 📍 **الوصول**: `/purchases/returns`

### ✨ **الميزات الرئيسية**
- ربط بفواتير المشتريات
- إرجاع جزئي أو كامل
- خصم من المخزون تلقائياً
- تعديل كشف حساب المورد

### 🔧 **كيفية الاستخدام**

#### **إنشاء مرتجع شراء جديد**
1. انقر "مرتجع شراء جديد"
2. **اختيار الفاتورة**:
   - ابحث برقم الفاتورة أو اسم المورد
   - اختر الفاتورة المراد الإرجاع منها
3. **عرض بيانات الفاتورة**:
   - جميع المنتجات والكميات
   - أسعار الشراء الأصلية
4. **تحديد المنتجات المرتجعة**:
   - اختر المنتجات المراد إرجاعها
   - حدد الكمية المرتجعة
   - سبب الإرجاع (معيب، غير مطابق، تالف، إلخ)
   - حالة المنتج المرتجع
5. **تأكيد المرتجع**

#### **أسباب الإرجاع الشائعة**
- 🔧 **معيب**: منتج به عيب تصنيع
- 📏 **غير مطابق**: لا يطابق المواصفات
- 📦 **تالف**: تلف أثناء الشحن
- 📅 **منتهي الصلاحية**: تجاوز تاريخ الانتهاء
- 🔄 **تغيير في الطلب**: تعديل متطلبات الشراء

---

## 👥 **4. إدارة الموردين (Suppliers Management)**

### 📍 **الوصول**: `/contacts` (فلترة الموردين)

### ✨ **الميزات الرئيسية**
- قاعدة بيانات شاملة للموردين
- تتبع أداء الموردين
- كشوف حسابات الموردين
- تقييم الموردين

### 🔧 **إدارة بيانات الموردين**

#### **إضافة مورد جديد**
1. انتقل لصفحة جهات الاتصال
2. انقر "إضافة جهة اتصال جديدة"
3. **البيانات الأساسية**:
   - اسم المورد/الشركة
   - نوع جهة الاتصال: "مورد" أو "عميل ومورد"
   - رقم الهاتف
   - البريد الإلكتروني
   - العنوان
4. **البيانات التجارية**:
   - الرقم الضريبي
   - شروط الدفع المفضلة
   - حد الائتمان
   - العملة المفضلة
5. **معلومات إضافية**:
   - الموقع الإلكتروني
   - ملاحظات
   - تصنيف المورد

#### **تقييم أداء الموردين**
- 📊 **معدل التسليم في الوقت المحدد**
- 💰 **دقة الأسعار والفواتير**
- 🏆 **جودة المنتجات**
- 📞 **سرعة الاستجابة**
- 🔄 **معدل المرتجعات**

---

## 📊 **التقارير والإحصائيات**

### 📈 **تقارير المشتريات**
- إجمالي المشتريات (يومي، شهري، سنوي)
- أكثر المنتجات شراءً
- أفضل الموردين
- تحليل التكاليف

### 📋 **تقارير الطلبات**
- طلبات قيد التنفيذ
- متوسط وقت التسليم
- معدل تنفيذ الطلبات

### 🔄 **تقارير المرتجعات**
- نسبة المرتجعات لكل مورد
- أسباب الإرجاع الأكثر شيوعاً
- تكلفة المرتجعات

### 👥 **تقارير الموردين**
- كشوف حسابات الموردين
- تقييم أداء الموردين
- تحليل الائتمان

---

## 💡 **ميزات متقدمة**

### 🔄 **التكامل مع المخزون**
- تحديث تلقائي للمخزون عند الاستلام
- تتبع تكلفة المنتجات
- إدارة المخزون الأدنى والحد الأقصى

### 💰 **التكامل مع المحاسبة**
- تسجيل تلقائي في كشوف حسابات الموردين
- ربط مع التدفق النقدي
- حساب الضرائب والخصومات

### 📱 **الإشعارات التلقائية**
- تنبيهات تواريخ الاستحقاق
- إشعارات تأخير التسليم
- تذكيرات المدفوعات

---

## 🎯 **نصائح للاستخدام الأمثل**

### ✅ **أفضل الممارسات**
1. **أنشئ طلبات شراء** لجميع المشتريات
2. **تأكد من البيانات** قبل إرسال الطلبات
3. **راجع الفواتير** مقابل طلبات الشراء
4. **سجل المرتجعات فوراً** مع الأسباب
5. **قيّم أداء الموردين** بانتظام

### ⚠️ **تجنب هذه الأخطاء**
- إنشاء فواتير بدون طلبات شراء
- عدم التحقق من الكميات المستلمة
- تجاهل تواريخ الاستحقاق
- عدم تسجيل أسباب المرتجعات

### 🔧 **اختصارات مفيدة**
- **Ctrl + N**: إنشاء جديد
- **Ctrl + S**: حفظ
- **Ctrl + P**: طباعة
- **F5**: تحديث القائمة

---

## 🔗 **الروابط السريعة**

- **طلبات الشراء**: `/purchases/orders`
- **فواتير المشتريات**: `/purchases/invoices`
- **مرتجعات المشتريات**: `/purchases/returns`
- **إدارة الموردين**: `/contacts?type=supplier`
- **تقارير المشتريات**: `/purchases/reports`

---

## 🆘 **الدعم والمساعدة**

في حالة وجود أي مشاكل أو استفسارات:
1. راجع هذا الدليل أولاً
2. تحقق من صحة البيانات المدخلة
3. تأكد من الصلاحيات المطلوبة
4. تواصل مع فريق الدعم الفني

---

**💡 ملاحظة**: جميع العمليات في موديول المشتريات مترابطة ومتكاملة مع باقي أنظمة ERP لضمان دقة البيانات وسلاسة العمل.
