import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { hasPermission, PERMISSIONS } from '@/lib/auth'
import Layout from '@/components/layout/Layout'
import ProductForm from '@/components/products/ProductForm'
import ProductDetailsDialog from '@/components/products/ProductDetailsDialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Package,
  AlertTriangle,
  Eye,
  BarChart3,
  Warehouse,
  TrendingUp,
  TrendingDown,
  RefreshCw
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

// Enhanced mock data with warehouse distribution
const mockWarehouses = [
  { id: '1', name: 'مخزن الإلكترونيات الرئيسي', branch_id: '1' },
  { id: '2', name: 'مخزن الهواتف', branch_id: '1' },
  { id: '3', name: 'مخزن الإسكندرية', branch_id: '2' },
]

const mockProducts = [
  // منتج رئيسي مركب
  {
    id: '1',
    name: 'HP ZBook Studio G9',
    sku: 'HP-ZBOOK-G9-001',
    barcode: '1234567890123',
    category: 'الحاسوب المحمول',
    brand: 'HP',
    unit_price: 85000,
    cost_price: 65000,
    stock_quantity: 8,
    min_stock_level: 3,
    max_stock_level: 20,
    unit: 'قطعة',
    description: 'HP ZBook Studio G9 - محطة عمل متنقلة احترافية',
    product_type: 'composite', // simple, composite, component
    base_price: 75000, // السعر الأساسي بدون مكونات
    warehouse_stock: [
      { warehouse_id: '1', warehouse_name: 'مخزن الإلكترونيات الرئيسي', quantity: 5 },
      { warehouse_id: '3', warehouse_name: 'مخزن الإسكندرية', quantity: 3 },
    ],
    // المكونات الاختيارية
    optional_components: [
      {
        component_id: '5',
        component_name: 'رام إضافية 16GB DDR5',
        is_required: false,
        default_quantity: 0,
        max_quantity: 4, // يمكن إضافة حتى 4 رامات
        description: 'يمكن إضافة حتى 4 رامات للحصول على 64GB إجمالي'
      },
      {
        component_id: '6',
        component_name: 'SSD إضافي 1TB NVMe',
        is_required: false,
        default_quantity: 0,
        max_quantity: 2, // يمكن إضافة حتى 2 SSD
        description: 'يمكن إضافة حتى 2 أقراص SSD للحصول على 2TB إضافي'
      },
      {
        component_id: '7',
        component_name: 'بطارية إضافية HP ZBook',
        is_required: false,
        default_quantity: 0,
        max_quantity: 1,
        description: 'بطارية احتياطية لزيادة وقت التشغيل'
      },
    ],
    is_active: true,
    created_at: '2024-01-01',
    last_movement: '2024-01-15',
    total_sold: 12,
    total_purchased: 20,
  },
  {
    id: '2',
    name: 'iPhone 15 Pro',
    sku: 'APPLE-IP15P-001',
    barcode: '2345678901234',
    category: 'الهواتف الذكية',
    brand: 'Apple',
    unit_price: 38000,
    cost_price: 30000,
    stock_quantity: 3,
    min_stock_level: 8,
    max_stock_level: 30,
    unit: 'قطعة',
    description: 'iPhone 15 Pro بذاكرة 256GB',
    product_type: 'simple',
    warehouse_stock: [
      { warehouse_id: '2', warehouse_name: 'مخزن الهواتف', quantity: 3 },
    ],
    is_active: true,
    created_at: '2024-01-02',
    last_movement: '2024-01-14',
    total_sold: 12,
    total_purchased: 15,
  },
  {
    id: '3',
    name: 'Samsung Galaxy S24',
    sku: 'SAMSUNG-S24-001',
    barcode: '3456789012345',
    category: 'الهواتف الذكية',
    brand: 'Samsung',
    unit_price: 28000,
    cost_price: 22000,
    stock_quantity: 7,
    min_stock_level: 10,
    max_stock_level: 25,
    unit: 'قطعة',
    description: 'Samsung Galaxy S24 بذاكرة 512GB',
    product_type: 'simple',
    warehouse_stock: [
      { warehouse_id: '2', warehouse_name: 'مخزن الهواتف', quantity: 4 },
      { warehouse_id: '3', warehouse_name: 'مخزن الإسكندرية', quantity: 3 },
    ],
    is_active: true,
    created_at: '2024-01-03',
    last_movement: '2024-01-13',
    total_sold: 18,
    total_purchased: 25,
  },
  {
    id: '4',
    name: 'AirPods Pro',
    sku: 'APPLE-AIRPODS-001',
    barcode: '4567890123456',
    category: 'الإكسسوارات',
    brand: 'Apple',
    unit_price: 8500,
    cost_price: 6500,
    stock_quantity: 0,
    min_stock_level: 15,
    max_stock_level: 50,
    unit: 'قطعة',
    description: 'AirPods Pro الجيل الثاني',
    product_type: 'simple',
    warehouse_stock: [],
    is_active: true,
    created_at: '2024-01-04',
    last_movement: '2024-01-12',
    total_sold: 35,
    total_purchased: 35,
  },
  // مكونات يمكن بيعها منفصلة
  {
    id: '5',
    name: 'رام إضافية 16GB DDR5',
    sku: 'RAM-16GB-DDR5-001',
    barcode: '5678901234567',
    category: 'مكونات الحاسوب',
    brand: 'Kingston',
    unit_price: 3500,
    cost_price: 2800,
    stock_quantity: 25,
    min_stock_level: 10,
    max_stock_level: 100,
    unit: 'قطعة',
    description: 'ذاكرة رام 16GB DDR5 للحاسوب المحمول',
    product_type: 'component',
    compatible_with: ['1'], // متوافق مع HP ZBook
    warehouse_stock: [
      { warehouse_id: '1', warehouse_name: 'مخزن الإلكترونيات الرئيسي', quantity: 20 },
      { warehouse_id: '3', warehouse_name: 'مخزن الإسكندرية', quantity: 5 },
    ],
    is_active: true,
    created_at: '2024-01-05',
    last_movement: '2024-01-14',
    total_sold: 8,
    total_purchased: 33,
  },
  {
    id: '6',
    name: 'SSD إضافي 1TB NVMe',
    sku: 'SSD-1TB-NVME-001',
    barcode: '6789012345678',
    category: 'مكونات الحاسوب',
    brand: 'Samsung',
    unit_price: 4500,
    cost_price: 3600,
    stock_quantity: 15,
    min_stock_level: 8,
    max_stock_level: 50,
    unit: 'قطعة',
    description: 'قرص تخزين SSD 1TB NVMe M.2',
    product_type: 'component',
    compatible_with: ['1'], // متوافق مع HP ZBook
    warehouse_stock: [
      { warehouse_id: '1', warehouse_name: 'مخزن الإلكترونيات الرئيسي', quantity: 12 },
      { warehouse_id: '3', warehouse_name: 'مخزن الإسكندرية', quantity: 3 },
    ],
    is_active: true,
    created_at: '2024-01-06',
    last_movement: '2024-01-13',
    total_sold: 5,
    total_purchased: 20,
  },
  {
    id: '7',
    name: 'بطارية إضافية HP ZBook',
    sku: 'BAT-HP-ZBOOK-001',
    barcode: '7890123456789',
    category: 'مكونات الحاسوب',
    brand: 'HP',
    unit_price: 2800,
    cost_price: 2200,
    stock_quantity: 12,
    min_stock_level: 5,
    max_stock_level: 30,
    unit: 'قطعة',
    description: 'بطارية ليثيوم أيون للحاسوب المحمول HP ZBook',
    product_type: 'component',
    compatible_with: ['1'], // متوافق مع HP ZBook
    warehouse_stock: [
      { warehouse_id: '1', warehouse_name: 'مخزن الإلكترونيات الرئيسي', quantity: 8 },
      { warehouse_id: '3', warehouse_name: 'مخزن الإسكندرية', quantity: 4 },
    ],
    is_active: true,
    created_at: '2024-01-07',
    last_movement: '2024-01-12',
    total_sold: 3,
    total_purchased: 15,
  },
]

export default function ProductsPage() {
  const { user } = useAuth()
  const [products, setProducts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [selectedProductType, setSelectedProductType] = useState('all')
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [editingProduct, setEditingProduct] = useState<any>(null)
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false)

  useEffect(() => {
    fetchProducts()
  }, [])

  const fetchProducts = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/postgres/data?type=products')
      const result = await response.json()

      if (result.success) {
        setProducts(result.data || [])
      } else {
        console.error('فشل في جلب المنتجات:', result.error)
      }
    } catch (error) {
      console.error('خطأ في جلب المنتجات:', error)
    } finally {
      setLoading(false)
    }
  }

  // تصفية المنتجات
  const filteredProducts = products.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.sku.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory
    const matchesProductType = selectedProductType === 'all' || product.product_type === selectedProductType
    return matchesSearch && matchesCategory && matchesProductType
  })

  // الحصول على الفئات الفريدة
  const categories = ['all', ...Array.from(new Set(products.map(p => p.category)))]

  // أنواع المنتجات
  const productTypes = [
    { value: 'all', label: 'جميع الأنواع' },
    { value: 'simple', label: 'منتجات بسيطة' },
    { value: 'composite', label: 'منتجات مركبة' },
    { value: 'component', label: 'مكونات' },
  ]

  const canCreate = hasPermission(user, PERMISSIONS.PRODUCTS_CREATE)
  const canEdit = hasPermission(user, PERMISSIONS.PRODUCTS_EDIT)
  const canDelete = hasPermission(user, PERMISSIONS.PRODUCTS_DELETE)

  const handleAddProduct = () => {
    setEditingProduct(null)
    setIsAddDialogOpen(true)
  }

  const handleEditProduct = (product: any) => {
    setEditingProduct(product)
    setIsAddDialogOpen(true)
  }

  const handleDeleteProduct = (productId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
      setProducts(products.filter(p => p.id !== productId))
    }
  }

  const handleViewProduct = (product: any) => {
    setSelectedProduct(product)
    setIsDetailsDialogOpen(true)
  }

  const handleAddToCart = (productConfig: any) => {
    console.log('إضافة للسلة:', productConfig)

    // إنشاء رسالة تفصيلية
    let message = `تم إضافة ${productConfig.product_name} للسلة\n\n`
    message += `السعر الأساسي: ${productConfig.base_price.toLocaleString()} ج.م\n`

    if (productConfig.selected_components.length > 0) {
      message += `\nالمكونات المضافة:\n`
      productConfig.selected_components.forEach((comp: any) => {
        message += `• ${comp.component_name} × ${comp.quantity} = ${comp.total_price.toLocaleString()} ج.م\n`
      })
      message += `\nإجمالي المكونات: ${productConfig.total_components} قطعة\n`
    }

    message += `\nالإجمالي النهائي: ${productConfig.total_price.toLocaleString()} ج.م`

    alert(message)
  }

  const handleSaveProduct = (productData: any) => {
    if (editingProduct) {
      // تحديث منتج موجود
      setProducts(products.map(p =>
        p.id === editingProduct.id ? { ...productData, id: editingProduct.id } : p
      ))
    } else {
      // إضافة منتج جديد
      setProducts([...products, productData])
    }
    setIsAddDialogOpen(false)
    setEditingProduct(null)
  }

  const getStockStatus = (product: any) => {
    if (product.stock_quantity <= 0) {
      return { status: 'out-of-stock', color: 'text-red-600', text: 'نفد المخزون' }
    } else if (product.stock_quantity <= product.min_stock_level) {
      return { status: 'low-stock', color: 'text-orange-600', text: 'مخزون منخفض' }
    } else {
      return { status: 'in-stock', color: 'text-green-600', text: 'متوفر' }
    }
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">إدارة المنتجات</h1>
            <p className="text-gray-600">إدارة جميع المنتجات والمخزون</p>
          </div>
          {canCreate && (
            <Button onClick={handleAddProduct}>
              <Plus className="h-4 w-4 mr-2" />
              إضافة منتج جديد
            </Button>
          )}
        </div>

        {/* Enhanced Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في المنتجات..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="اختر الفئة" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map(category => (
                      <SelectItem key={category} value={category}>
                        {category === 'all' ? 'جميع الفئات' : category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Select value={selectedProductType} onValueChange={setSelectedProductType}>
                  <SelectTrigger>
                    <SelectValue placeholder="نوع المنتج" />
                  </SelectTrigger>
                  <SelectContent>
                    {productTypes.map(type => (
                      <SelectItem key={type.value} value={type.value}>
                        {type.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي المنتجات</CardTitle>
              <Package className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{products.length}</div>
              <p className="text-xs text-gray-500">
                {products.filter(p => p.is_active).length} نشط
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">قيمة المخزون</CardTitle>
              <TrendingUp className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(products.reduce((sum, p) => sum + (p.stock_quantity * p.cost_price), 0))}
              </div>
              <p className="text-xs text-gray-500">التكلفة الإجمالية</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">مخزون منخفض</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {products.filter(p => p.stock_quantity <= p.min_stock_level && p.stock_quantity > 0).length}
              </div>
              <p className="text-xs text-gray-500">يحتاج إعادة طلب</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">نفد المخزون</CardTitle>
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {products.filter(p => p.stock_quantity <= 0).length}
              </div>
              <p className="text-xs text-gray-500">غير متوفر</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي الوحدات</CardTitle>
              <Warehouse className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {products.reduce((sum, p) => sum + p.stock_quantity, 0)}
              </div>
              <p className="text-xs text-gray-500">في جميع المخازن</p>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Products Table */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>قائمة المنتجات</CardTitle>
            <div className="flex items-center space-x-2 space-x-reverse">
              <Button variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                تحديث
              </Button>
              <Button variant="outline" size="sm">
                <BarChart3 className="h-4 w-4 mr-2" />
                تقرير
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>المنتج</TableHead>
                  <TableHead>الفئة والعلامة</TableHead>
                  <TableHead>الأسعار</TableHead>
                  <TableHead>المخزون</TableHead>
                  <TableHead>توزيع المخازن</TableHead>
                  <TableHead>الأداء</TableHead>
                  <TableHead>الحالة</TableHead>
                  <TableHead>الإجراءات</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredProducts.map((product) => {
                  const stockStatus = getStockStatus(product)
                  const profitMargin = ((product.unit_price - product.cost_price) / product.unit_price * 100).toFixed(1)

                  return (
                    <TableRow key={product.id}>
                      <TableCell className="font-medium">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                            product.product_type === 'composite' ? 'bg-blue-100' :
                            product.product_type === 'component' ? 'bg-green-100' : 'bg-gray-100'
                          }`}>
                            <Package className={`h-5 w-5 ${
                              product.product_type === 'composite' ? 'text-blue-600' :
                              product.product_type === 'component' ? 'text-green-600' : 'text-gray-500'
                            }`} />
                          </div>
                          <div>
                            <div className="flex items-center space-x-2 space-x-reverse">
                              <span className="font-medium">{product.name}</span>
                              {product.product_type === 'composite' && (
                                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                  مركب
                                </span>
                              )}
                              {product.product_type === 'component' && (
                                <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                                  مكون
                                </span>
                              )}
                            </div>
                            <div className="text-xs text-gray-500">
                              {product.sku} • {product.unit}
                            </div>
                            {product.product_type === 'composite' && product.optional_components && (
                              <div className="text-xs text-blue-600 mt-1">
                                {product.optional_components.length} مكون اختياري
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div>
                          <div className="font-medium text-sm">{product.category}</div>
                          <div className="text-xs text-gray-500">{product.brand}</div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="text-right">
                          <div className="font-medium">{formatCurrency(product.unit_price)}</div>
                          {product.product_type === 'composite' && product.base_price && (
                            <div className="text-xs text-blue-600">
                              أساسي: {formatCurrency(product.base_price)}
                            </div>
                          )}
                          <div className="text-xs text-gray-500">
                            تكلفة: {formatCurrency(product.cost_price)}
                          </div>
                          <div className="text-xs text-green-600">
                            ربح: {profitMargin}%
                          </div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="text-center">
                          <div className={`text-lg font-bold ${stockStatus.color}`}>
                            {product.stock_quantity}
                          </div>
                          <div className="text-xs text-gray-500">
                            حد أدنى: {product.min_stock_level}
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                            <div
                              className={`h-1 rounded-full ${
                                stockStatus.status === 'out-of-stock' ? 'bg-red-500' :
                                stockStatus.status === 'low-stock' ? 'bg-orange-500' : 'bg-green-500'
                              }`}
                              style={{
                                width: `${Math.min((product.stock_quantity / product.max_stock_level) * 100, 100)}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="space-y-1">
                          {product.warehouse_stock.length > 0 ? (
                            product.warehouse_stock.map((ws, index) => (
                              <div key={index} className="flex items-center justify-between text-xs">
                                <span className="text-gray-600 truncate max-w-20">
                                  {ws.warehouse_name}
                                </span>
                                <span className="font-medium">{ws.quantity}</span>
                              </div>
                            ))
                          ) : (
                            <span className="text-xs text-gray-400">لا يوجد مخزون</span>
                          )}
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="text-center">
                          <div className="flex items-center justify-center space-x-1 space-x-reverse">
                            <TrendingUp className="h-3 w-3 text-green-500" />
                            <span className="text-xs font-medium">{product.total_sold}</span>
                          </div>
                          <div className="text-xs text-gray-500">مبيعات</div>
                          <div className="flex items-center justify-center space-x-1 space-x-reverse mt-1">
                            <TrendingDown className="h-3 w-3 text-blue-500" />
                            <span className="text-xs font-medium">{product.total_purchased}</span>
                          </div>
                          <div className="text-xs text-gray-500">مشتريات</div>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex flex-col items-center space-y-1">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            stockStatus.status === 'out-of-stock' ? 'bg-red-100 text-red-800' :
                            stockStatus.status === 'low-stock' ? 'bg-orange-100 text-orange-800' :
                            'bg-green-100 text-green-800'
                          }`}>
                            {stockStatus.text}
                          </span>
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            product.is_active ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {product.is_active ? 'نشط' : 'معطل'}
                          </span>
                        </div>
                      </TableCell>

                      <TableCell>
                        <div className="flex space-x-1 space-x-reverse">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewProduct(product)}
                            title="عرض التفاصيل"
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {canEdit && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditProduct(product)}
                              title="تعديل"
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {/* عرض حركات المخزون */}}
                            title="حركات المخزون"
                          >
                            <BarChart3 className="h-4 w-4" />
                          </Button>
                          {canDelete && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteProduct(product.id)}
                              title="حذف"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        {/* Product Form Dialog */}
        <ProductForm
          isOpen={isAddDialogOpen}
          onClose={() => {
            setIsAddDialogOpen(false)
            setEditingProduct(null)
          }}
          product={editingProduct}
          onSave={handleSaveProduct}
        />

        {/* Product Details Dialog */}
        <ProductDetailsDialog
          isOpen={isDetailsDialogOpen}
          onClose={() => setIsDetailsDialogOpen(false)}
          product={selectedProduct}
          onAddToCart={handleAddToCart}
        />
      </div>
    </Layout>
  )
}
