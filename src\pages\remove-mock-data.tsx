import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  XCircle, 
  Code, 
  AlertTriangle,
  FileCode,
  Trash2
} from 'lucide-react'

export default function RemoveMockData() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [confirmed, setConfirmed] = useState(false)

  const removeMockData = async () => {
    if (!confirmed) {
      alert('يرجى تأكيد العملية أولاً')
      return
    }

    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/remove-mock-data', {
        method: 'POST'
      })
      
      const data = await response.json()
      setResult(data)
      
    } catch (err) {
      setResult({
        success: false,
        message: 'خطأ في إزالة البيانات الوهمية من الكود'
      })
    } finally {
      setLoading(false)
    }
  }

  const filesToUpdate = [
    { name: 'عروض الأسعار', file: 'sales/quotations', icon: '💰' },
    { name: 'أوامر المبيعات', file: 'sales/orders', icon: '🛒' },
    { name: 'فواتير المبيعات', file: 'sales/invoices', icon: '🧾' },
    { name: 'مرتجعات المبيعات', file: 'sales/returns', icon: '🔄' },
    { name: 'المشتريات', file: 'purchases', icon: '📦' },
    { name: 'أوامر الشراء', file: 'purchases/orders', icon: '📋' },
    { name: 'فواتير المشتريات', file: 'purchases/invoices', icon: '📄' },
    { name: 'مرتجعات المشتريات', file: 'purchases/returns', icon: '↩️' },
    { name: 'جهات الاتصال', file: 'contacts', icon: '👥' },
    { name: 'الصيانة', file: 'maintenance', icon: '🔧' },
    { name: 'طلبات الصيانة', file: 'maintenance/requests', icon: '🎫' },
    { name: 'تقارير الصيانة', file: 'maintenance/reports', icon: '📊' },
    { name: 'المحاسبة', file: 'accounting', icon: '💳' },
    { name: 'دليل الحسابات', file: 'accounting/accounts', icon: '📚' },
    { name: 'التقارير المحاسبية', file: 'accounting/reports', icon: '📈' },
    { name: 'التدفق النقدي', file: 'accounting/cash-flow', icon: '💸' },
    { name: 'المصروفات', file: 'accounting/expenses', icon: '💰' },
    { name: 'كشوف العملاء', file: 'accounting/customer-statements', icon: '📋' },
    { name: 'الأقساط', file: 'accounting/installments', icon: '📅' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-50 to-blue-100 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="bg-white p-4 rounded-full shadow-lg inline-block mb-4">
            <Code className="h-12 w-12 text-purple-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            إزالة البيانات الوهمية من الكود
          </h1>
          <p className="text-xl text-gray-600">
            إزالة جميع البيانات الوهمية المكتوبة في ملفات الكود
          </p>
        </div>

        {/* Warning */}
        <Alert className="border-orange-200 bg-orange-50">
          <AlertTriangle className="h-4 w-4 text-orange-600" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium text-orange-800">⚠️ تحذير مهم</p>
              <p className="text-orange-700">
                هذه العملية ستقوم بتعديل ملفات الكود لإزالة البيانات الوهمية المكتوبة مباشرة في الكود.
                تأكد من عمل نسخة احتياطية قبل المتابعة.
              </p>
            </div>
          </AlertDescription>
        </Alert>

        {/* Files to be updated */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-purple-700">
              <FileCode className="h-6 w-6 mr-2" />
              الملفات التي سيتم تحديثها
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {filesToUpdate.map((item, index) => (
                <div key={index} className="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
                  <div className="text-2xl mb-2">{item.icon}</div>
                  <p className="text-sm font-medium text-purple-800">{item.name}</p>
                  <p className="text-xs text-purple-600">{item.file}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* What will happen */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-green-700">
              <CheckCircle className="h-6 w-6 mr-2" />
              ما سيحدث
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                <span>إزالة جميع المصفوفات والكائنات الوهمية من الكود</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                <span>استبدال البيانات الوهمية بمكون "لا توجد بيانات"</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                <span>إضافة استيرادات المكونات المطلوبة</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                <span>الحفاظ على هيكل الكود والوظائف</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Confirmation and Action */}
        <Card className="bg-white shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">تأكيد العملية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {!result && !loading && (
              <div className="space-y-4">
                <div className="flex items-center justify-center space-x-4">
                  <input
                    type="checkbox"
                    id="confirm"
                    checked={confirmed}
                    onChange={(e) => setConfirmed(e.target.checked)}
                    className="w-4 h-4 text-purple-600"
                  />
                  <label htmlFor="confirm" className="text-gray-700">
                    أؤكد أنني أريد إزالة البيانات الوهمية من ملفات الكود
                  </label>
                </div>
                
                <div className="text-center">
                  <Button 
                    onClick={removeMockData}
                    disabled={!confirmed}
                    size="lg"
                    className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-8 py-3"
                  >
                    <Code className="h-5 w-5 mr-2" />
                    إزالة البيانات الوهمية من الكود
                  </Button>
                </div>
              </div>
            )}

            {loading && (
              <div className="text-center space-y-4">
                <FileCode className="h-12 w-12 mx-auto text-purple-600 animate-pulse" />
                <h3 className="text-lg font-semibold">جاري معالجة الملفات...</h3>
                <p className="text-gray-600">يرجى الانتظار حتى اكتمال العملية</p>
              </div>
            )}

            {result && (
              <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                {result.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription>
                  <div className="space-y-4">
                    <p className="font-medium text-lg">{result.message}</p>
                    
                    {result.summary && (
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-blue-600">{result.summary.totalFiles}</p>
                          <p className="text-gray-600">إجمالي الملفات</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-green-600">{result.summary.successful}</p>
                          <p className="text-gray-600">تم معالجتها</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-red-600">{result.summary.failed}</p>
                          <p className="text-gray-600">فشل</p>
                        </div>
                      </div>
                    )}

                    {result.recommendations && (
                      <div className="space-y-2">
                        <h4 className="font-semibold">النتائج:</h4>
                        <ul className="space-y-1">
                          {result.recommendations.map((rec: string, index: number) => (
                            <li key={index} className="text-sm">{rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="flex gap-4 pt-4">
                      <Button 
                        onClick={() => window.location.href = '/dashboard'}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        لوحة التحكم
                      </Button>
                      <Button 
                        onClick={() => window.location.reload()}
                        variant="outline"
                      >
                        تحديث الصفحة
                      </Button>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
