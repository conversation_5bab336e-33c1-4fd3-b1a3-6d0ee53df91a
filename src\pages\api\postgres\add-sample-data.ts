import { NextApiRequest, NextApiResponse } from 'next'
import { query } from '@/lib/postgres'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // إضافة عملاء
    await query(`
      INSERT INTO customers (name, email, phone, address, credit_limit, current_balance) VALUES
      ('شركة التقنية المتقدمة', '<EMAIL>', '+201234567890', 'القاهرة، مدينة نصر', 50000, 0),
      ('مؤسسة الإلكترونيات الحديثة', '<EMAIL>', '+201234567891', 'الجيزة، المهندسين', 30000, 5000),
      ('شركة الأجهزة الذكية', '<EMAIL>', '+201234567892', 'الإسكندرية، سيدي جابر', 25000, 0),
      ('متجر الكمبيوتر المتقدم', '<EMAIL>', '+201234567893', 'القاهرة، مصر الجديدة', 20000, 2500),
      ('شركة الحلول التقنية', '<EMAIL>', '+201234567894', 'الجيزة، الدقي', 15000, 0)
      ON CONFLICT (email) DO NOTHING
    `)

    // إضافة موردين
    await query(`
      INSERT INTO suppliers (name, email, phone, address, contact_person, payment_terms) VALUES
      ('شركة استيراد الإلكترونيات', '<EMAIL>', '+201234567895', 'القاهرة، العتبة', 'أحمد المبيعات', '30 يوم'),
      ('مؤسسة قطع الغيار الأصلية', '<EMAIL>', '+201234567896', 'الإسكندرية، العطارين', 'سارة التوريد', '15 يوم'),
      ('شركة التوزيع التقني', '<EMAIL>', '+201234567897', 'الجيزة، فيصل', 'محمد الهندسة', '45 يوم')
      ON CONFLICT (email) DO NOTHING
    `)

    // إضافة منتجات
    await query(`
      INSERT INTO products (name, sku, barcode, description, category, brand, unit_price, cost_price, stock_quantity, min_stock_level, max_stock_level) VALUES
      ('لابتوب HP EliteBook 840', 'HP-EB-840-G8', '1234567890123', 'لابتوب HP EliteBook 840 G8 - Intel Core i7', 'أجهزة كمبيوتر', 'HP', 25000, 20000, 15, 5, 50),
      ('ذاكرة RAM 16GB DDR4', 'RAM-DDR4-16GB', '1234567890124', 'ذاكرة RAM DDR4 16GB 3200MHz', 'قطع غيار', 'Kingston', 1500, 1200, 50, 10, 100),
      ('قرص صلب SSD 1TB', 'SSD-1TB-SATA', '1234567890125', 'قرص صلب SSD 1TB SATA III', 'قطع غيار', 'Samsung', 2500, 2000, 30, 8, 80),
      ('شاشة LED 24 بوصة', 'MON-LED-24', '1234567890126', 'شاشة LED 24 بوصة Full HD', 'شاشات', 'Dell', 3500, 2800, 20, 5, 40),
      ('لوحة مفاتيح لاسلكية', 'KB-WIRELESS', '1234567890127', 'لوحة مفاتيح لاسلكية مع ماوس', 'ملحقات', 'Logitech', 450, 350, 100, 20, 200),
      ('iPhone 15 Pro', 'APPLE-IP15P-001', '2345678901234', 'iPhone 15 Pro بذاكرة 256GB', 'هواتف ذكية', 'Apple', 38000, 30000, 8, 3, 25),
      ('Samsung Galaxy S24', 'SAMSUNG-S24-001', '3456789012345', 'Samsung Galaxy S24 بذاكرة 512GB', 'هواتف ذكية', 'Samsung', 28000, 22000, 12, 5, 30),
      ('AirPods Pro', 'APPLE-AIRPODS-001', '4567890123456', 'AirPods Pro الجيل الثاني', 'إكسسوارات', 'Apple', 8500, 6500, 25, 10, 50)
      ON CONFLICT (sku) DO NOTHING
    `)

    // إضافة مخزون للمنتجات
    await query(`
      INSERT INTO inventory (product_id, warehouse_id, total_stock, available_stock, reserved_stock, minimum_stock)
      SELECT p.id, 1, p.stock_quantity, p.stock_quantity, 0, p.min_stock_level
      FROM products p
      WHERE NOT EXISTS (SELECT 1 FROM inventory i WHERE i.product_id = p.id AND i.warehouse_id = 1)
    `)

    // إضافة بعض أوامر المبيعات
    await query(`
      INSERT INTO sales_orders (order_number, customer_id, branch_id, total_amount, discount_amount, tax_amount, final_amount, payment_method, payment_status, order_status, created_by) VALUES
      ('SO-2024-001', 1, 1, 25000, 0, 3750, 28750, 'cash', 'paid', 'completed', 1),
      ('SO-2024-002', 2, 1, 38000, 2000, 5400, 41400, 'card', 'pending', 'pending', 1),
      ('SO-2024-003', 3, 1, 15000, 500, 2175, 16675, 'bank_transfer', 'paid', 'shipped', 1)
      ON CONFLICT (order_number) DO NOTHING
    `)

    // إضافة تفاصيل أوامر المبيعات
    await query(`
      INSERT INTO sales_order_items (sales_order_id, product_id, quantity, unit_price, discount_amount, total_amount) VALUES
      (1, 1, 1, 25000, 0, 25000),
      (2, 6, 1, 38000, 2000, 36000),
      (3, 2, 10, 1500, 50, 14950)
      ON CONFLICT DO NOTHING
    `)

    // إضافة حركات مخزون
    await query(`
      INSERT INTO stock_movements (product_id, warehouse_id, movement_type, quantity, reference_type, reference_number, created_by) VALUES
      (1, 1, 'sell', -1, 'order', 'SO-2024-001', 1),
      (6, 1, 'sell', -1, 'order', 'SO-2024-002', 1),
      (2, 1, 'sell', -10, 'order', 'SO-2024-003', 1),
      (1, 1, 'purchase', 20, 'purchase', 'PO-2024-001', 1),
      (2, 1, 'purchase', 60, 'purchase', 'PO-2024-002', 1)
      ON CONFLICT DO NOTHING
    `)

    return res.status(200).json({
      success: true,
      message: 'تم إضافة البيانات التجريبية بنجاح',
      data: {
        customers: 5,
        suppliers: 3,
        products: 8,
        salesOrders: 3,
        stockMovements: 5
      }
    })

  } catch (error) {
    console.error('خطأ في إضافة البيانات التجريبية:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في إضافة البيانات التجريبية',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
