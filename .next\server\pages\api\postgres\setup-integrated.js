"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/postgres/setup-integrated";
exports.ids = ["pages/api/postgres/setup-integrated"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fsetup-integrated&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Csetup-integrated.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fsetup-integrated&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Csetup-integrated.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_postgres_setup_integrated_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\postgres\\setup-integrated.ts */ \"(api)/./src/pages/api/postgres/setup-integrated.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_setup_integrated_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_setup_integrated_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/postgres/setup-integrated\",\n        pathname: \"/api/postgres/setup-integrated\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_postgres_setup_integrated_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fsetup-integrated&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Csetup-integrated.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/postgres.ts":
/*!*****************************!*\
  !*** ./src/lib/postgres.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTables: () => (/* binding */ checkTables),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   findUser: () => (/* binding */ findUser),\n/* harmony export */   getCustomers: () => (/* binding */ getCustomers),\n/* harmony export */   getInventory: () => (/* binding */ getInventory),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getSuppliers: () => (/* binding */ getSuppliers),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\n// إعداد الاتصال بقاعدة البيانات PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"5432\"),\n    database: process.env.DB_NAME || \"Vero_ERP_ABA\",\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    ssl:  false ? 0 : false,\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const client = await pool.connect();\n        const result = await client.query(\"SELECT NOW()\");\n        client.release();\n        return {\n            success: true,\n            time: result.rows[0].now\n        };\n    } catch (error) {\n        console.error(\"خطأ في الاتصال بقاعدة البيانات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    try {\n        const result = await pool.query(text, params);\n        return {\n            success: true,\n            data: result.rows,\n            rowCount: result.rowCount\n        };\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للتحقق من وجود الجداول\nconst checkTables = async ()=>{\n    try {\n        const result = await query(`\n      SELECT table_name\n      FROM information_schema.tables\n      WHERE table_schema = 'public'\n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = result.data?.map((row)=>row.table_name) || [];\n        return {\n            success: true,\n            tables,\n            hasBranches: tables.includes(\"branches\"),\n            hasUsers: tables.includes(\"users\"),\n            hasProducts: tables.includes(\"products\"),\n            hasWarehouses: tables.includes(\"warehouses\"),\n            needsSetup: !tables.includes(\"branches\")\n        };\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return {\n            success: false,\n            error,\n            needsSetup: true\n        };\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const result = await query(`\n      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)\n      VALUES ($1, $2, $3, $4, $5, true, NOW())\n      RETURNING id, email, username, full_name, role\n    `, [\n            userData.email,\n            userData.username,\n            userData.full_name,\n            userData.password_hash,\n            userData.role || \"admin\"\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0]\n        };\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للبحث عن مستخدم\nconst findUser = async (email)=>{\n    try {\n        const result = await query(`\n      SELECT id, email, username, full_name, role, password_hash, is_active\n      FROM users\n      WHERE email = $1 AND is_active = true\n    `, [\n            email\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0] || null\n        };\n    } catch (error) {\n        console.error(\"خطأ في البحث عن المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المنتجات متوافقة مع الكود\nconst getProducts = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        p.*,\n        i.total_stock,\n        i.available_stock,\n        i.reserved_stock\n      FROM products p\n      LEFT JOIN inventory i ON p.id = i.product_id\n      WHERE p.is_active = true\n      ORDER BY p.name\n    `);\n        return {\n            success: true,\n            data: result.data?.map((product)=>({\n                    ...product,\n                    current_stock: product.total_stock || 0,\n                    price: product.unit_price,\n                    cost: product.cost_price\n                })) || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المنتجات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المخزون متوافقة مع الكود\nconst getInventory = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        i.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        p.category as product_category,\n        w.name as warehouse_name\n      FROM inventory i\n      JOIN products p ON i.product_id = p.id\n      JOIN warehouses w ON i.warehouse_id = w.id\n      ORDER BY i.updated_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات العملاء\nconst getCustomers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM customers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب العملاء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات الموردين\nconst getSuppliers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM suppliers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب الموردين:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/postgres.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/postgres/setup-integrated.ts":
/*!****************************************************!*\
  !*** ./src/pages/api/postgres/setup-integrated.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/postgres */ \"(api)/./src/lib/postgres.ts\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! fs */ \"fs\");\n/* harmony import */ var fs__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(fs__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        // قراءة ملف المخطط المتكامل\n        const sqlFilePath = path__WEBPACK_IMPORTED_MODULE_2___default().join(process.cwd(), \"integrated_database_schema.sql\");\n        if (!fs__WEBPACK_IMPORTED_MODULE_1___default().existsSync(sqlFilePath)) {\n            return res.status(404).json({\n                success: false,\n                message: \"ملف integrated_database_schema.sql غير موجود\"\n            });\n        }\n        const sqlContent = fs__WEBPACK_IMPORTED_MODULE_1___default().readFileSync(sqlFilePath, \"utf8\");\n        // تقسيم الاستعلامات بعناية\n        const statements = [];\n        let currentStatement = \"\";\n        let inDollarQuote = false;\n        let dollarTag = \"\";\n        const lines = sqlContent.split(\"\\n\");\n        for (const line of lines){\n            const trimmedLine = line.trim();\n            // تجاهل التعليقات والأسطر الفارغة\n            if (trimmedLine.startsWith(\"--\") || trimmedLine === \"\") {\n                continue;\n            }\n            // التعامل مع DO blocks\n            if (trimmedLine.includes(\"DO $$\")) {\n                inDollarQuote = true;\n                dollarTag = \"$$\";\n                currentStatement += line + \"\\n\";\n                continue;\n            }\n            if (inDollarQuote) {\n                currentStatement += line + \"\\n\";\n                if (trimmedLine.includes(dollarTag) && !trimmedLine.startsWith(\"DO\")) {\n                    inDollarQuote = false;\n                    statements.push(currentStatement.trim());\n                    currentStatement = \"\";\n                }\n                continue;\n            }\n            currentStatement += line + \"\\n\";\n            // إنهاء الاستعلام عند الفاصلة المنقوطة\n            if (trimmedLine.endsWith(\";\")) {\n                statements.push(currentStatement.trim());\n                currentStatement = \"\";\n            }\n        }\n        // إضافة آخر استعلام إذا لم ينته بفاصلة منقوطة\n        if (currentStatement.trim()) {\n            statements.push(currentStatement.trim());\n        }\n        console.log(`عدد الاستعلامات المعالجة: ${statements.length}`);\n        const results = [];\n        let successCount = 0;\n        let errorCount = 0;\n        // تنفيذ كل استعلام\n        for(let i = 0; i < statements.length; i++){\n            const statement = statements[i];\n            if (statement.length === 0) continue;\n            console.log(`تنفيذ الاستعلام ${i + 1}: ${statement.substring(0, 50)}...`);\n            try {\n                const result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(statement);\n                if (result.success) {\n                    successCount++;\n                    results.push({\n                        index: i + 1,\n                        statement: statement.substring(0, 100) + (statement.length > 100 ? \"...\" : \"\"),\n                        success: true,\n                        message: \"تم التنفيذ بنجاح\",\n                        rowCount: result.rowCount\n                    });\n                    console.log(`✅ نجح الاستعلام ${i + 1}`);\n                } else {\n                    errorCount++;\n                    results.push({\n                        index: i + 1,\n                        statement: statement.substring(0, 100) + (statement.length > 100 ? \"...\" : \"\"),\n                        success: false,\n                        error: result.error,\n                        message: \"فشل في التنفيذ\"\n                    });\n                    console.log(`❌ فشل الاستعلام ${i + 1}:`, result.error);\n                }\n            } catch (error) {\n                errorCount++;\n                const errorMessage = error instanceof Error ? error.message : \"خطأ غير معروف\";\n                results.push({\n                    index: i + 1,\n                    statement: statement.substring(0, 100) + (statement.length > 100 ? \"...\" : \"\"),\n                    success: false,\n                    error: errorMessage,\n                    message: \"خطأ في التنفيذ\"\n                });\n                console.log(`❌ خطأ في الاستعلام ${i + 1}:`, errorMessage);\n            }\n        }\n        // فحص الجداول والأنواع بعد التنفيذ\n        const tablesCheck = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT table_name \n      FROM information_schema.tables \n      WHERE table_schema = 'public' \n      ORDER BY table_name\n    `);\n        const enumsCheck = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT typname \n      FROM pg_type \n      WHERE typname IN ('user_role', 'transaction_type', 'payment_method', 'movement_type', 'reference_type')\n    `);\n        const tables = tablesCheck.success ? tablesCheck.data?.map((row)=>row.table_name) : [];\n        const enums = enumsCheck.success ? enumsCheck.data?.map((row)=>row.typname) : [];\n        // إضافة بيانات تجريبية للمنتجات إذا كانت الجداول موجودة\n        if (tables?.includes(\"products\") && tables?.includes(\"inventory\")) {\n            try {\n                await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n          INSERT INTO products (name, sku, description, category, unit_price, cost_price, stock_quantity, min_stock_level)\n          VALUES \n            ('لابتوب HP EliteBook', 'HP-EB-840-G8', 'لابتوب HP EliteBook 840 G8 - Intel Core i7', 'أجهزة كمبيوتر', 25000, 20000, 15, 5),\n            ('ذاكرة RAM 16GB', 'RAM-DDR4-16GB', 'ذاكرة RAM DDR4 16GB 3200MHz', 'قطع غيار', 1500, 1200, 50, 10)\n          ON CONFLICT (sku) DO NOTHING\n        `);\n                await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n          INSERT INTO inventory (product_id, warehouse_id, total_stock, available_stock, reserved_stock)\n          SELECT p.id, 1, p.stock_quantity, p.stock_quantity, 0\n          FROM products p\n          WHERE NOT EXISTS (SELECT 1 FROM inventory i WHERE i.product_id = p.id)\n        `);\n            } catch (error) {\n                console.log(\"تحذير: فشل في إضافة البيانات التجريبية:\", error);\n            }\n        }\n        return res.status(200).json({\n            success: successCount > 0,\n            message: `تم تنفيذ ${successCount} استعلام بنجاح، ${errorCount} فشل`,\n            summary: {\n                totalStatements: statements.length,\n                successful: successCount,\n                failed: errorCount,\n                tablesCreated: tables?.length || 0,\n                enumsCreated: enums?.length || 0\n            },\n            database: {\n                tables: tables,\n                enums: enums,\n                coreTablesReady: [\n                    \"branches\",\n                    \"warehouses\",\n                    \"users\",\n                    \"products\",\n                    \"inventory\"\n                ].every((t)=>tables?.includes(t))\n            },\n            results: results.slice(0, 15),\n            recommendations: successCount > 0 ? [\n                \"✅ تم إنشاء المخطط المتكامل بنجاح\",\n                \"\\uD83C\\uDFAF يمكنك الآن إنشاء مستخدم جديد\",\n                \"\\uD83D\\uDD04 النظام جاهز للاستخدام الكامل\",\n                \"\\uD83D\\uDCCA تم إضافة بيانات تجريبية\"\n            ] : [\n                \"❌ فشل في إنشاء المخطط\",\n                \"\\uD83D\\uDD0D تحقق من أخطاء قاعدة البيانات\",\n                \"\\uD83D\\uDCDE تحقق من الاتصال بـ PostgreSQL\"\n            ]\n        });\n    } catch (error) {\n        console.error(\"خطأ في إعداد المخطط المتكامل:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/postgres/setup-integrated.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fsetup-integrated&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Csetup-integrated.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();