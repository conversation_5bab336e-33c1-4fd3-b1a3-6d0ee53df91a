"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/postgres/data";
exports.ids = ["pages/api/postgres/data"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fdata&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Cdata.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fdata&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Cdata.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_postgres_data_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\postgres\\data.ts */ \"(api)/./src/pages/api/postgres/data.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_data_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_data_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/postgres/data\",\n        pathname: \"/api/postgres/data\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_postgres_data_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fdata&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Cdata.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/postgres.ts":
/*!*****************************!*\
  !*** ./src/lib/postgres.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTables: () => (/* binding */ checkTables),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   findUser: () => (/* binding */ findUser),\n/* harmony export */   getCustomers: () => (/* binding */ getCustomers),\n/* harmony export */   getInventory: () => (/* binding */ getInventory),\n/* harmony export */   getMaintenanceRequests: () => (/* binding */ getMaintenanceRequests),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getPurchaseOrders: () => (/* binding */ getPurchaseOrders),\n/* harmony export */   getSalesOrders: () => (/* binding */ getSalesOrders),\n/* harmony export */   getStockMovements: () => (/* binding */ getStockMovements),\n/* harmony export */   getSuppliers: () => (/* binding */ getSuppliers),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\n// إعداد الاتصال بقاعدة البيانات PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"5432\"),\n    database: process.env.DB_NAME || \"Vero_ERP_ABA\",\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    ssl:  false ? 0 : false,\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const client = await pool.connect();\n        const result = await client.query(\"SELECT NOW()\");\n        client.release();\n        return {\n            success: true,\n            time: result.rows[0].now\n        };\n    } catch (error) {\n        console.error(\"خطأ في الاتصال بقاعدة البيانات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    try {\n        const result = await pool.query(text, params);\n        return {\n            success: true,\n            data: result.rows,\n            rowCount: result.rowCount\n        };\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للتحقق من وجود الجداول\nconst checkTables = async ()=>{\n    try {\n        const result = await query(`\n      SELECT table_name\n      FROM information_schema.tables\n      WHERE table_schema = 'public'\n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = result.data?.map((row)=>row.table_name) || [];\n        return {\n            success: true,\n            tables,\n            hasBranches: tables.includes(\"branches\"),\n            hasUsers: tables.includes(\"users\"),\n            hasProducts: tables.includes(\"products\"),\n            hasWarehouses: tables.includes(\"warehouses\"),\n            needsSetup: !tables.includes(\"branches\")\n        };\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return {\n            success: false,\n            error,\n            needsSetup: true\n        };\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const result = await query(`\n      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)\n      VALUES ($1, $2, $3, $4, $5, true, NOW())\n      RETURNING id, email, username, full_name, role\n    `, [\n            userData.email,\n            userData.username,\n            userData.full_name,\n            userData.password_hash,\n            userData.role || \"admin\"\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0]\n        };\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للبحث عن مستخدم\nconst findUser = async (email)=>{\n    try {\n        const result = await query(`\n      SELECT id, email, username, full_name, role, password_hash, is_active\n      FROM users\n      WHERE email = $1 AND is_active = true\n    `, [\n            email\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0] || null\n        };\n    } catch (error) {\n        console.error(\"خطأ في البحث عن المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المنتجات متوافقة مع الكود\nconst getProducts = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        p.*,\n        i.total_stock,\n        i.available_stock,\n        i.reserved_stock\n      FROM products p\n      LEFT JOIN inventory i ON p.id = i.product_id\n      WHERE p.is_active = true\n      ORDER BY p.name\n    `);\n        return {\n            success: true,\n            data: result.data?.map((product)=>({\n                    ...product,\n                    current_stock: product.total_stock || 0,\n                    price: product.unit_price,\n                    cost: product.cost_price\n                })) || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المنتجات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المخزون متوافقة مع الكود\nconst getInventory = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        i.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        p.category as product_category,\n        w.name as warehouse_name\n      FROM inventory i\n      JOIN products p ON i.product_id = p.id\n      JOIN warehouses w ON i.warehouse_id = w.id\n      ORDER BY i.updated_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات العملاء\nconst getCustomers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM customers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب العملاء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات الموردين\nconst getSuppliers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM suppliers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب الموردين:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات أوامر المبيعات\nconst getSalesOrders = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        so.*,\n        c.name as customer_name,\n        b.name as branch_name,\n        u.full_name as created_by_name\n      FROM sales_orders so\n      LEFT JOIN customers c ON so.customer_id = c.id\n      LEFT JOIN branches b ON so.branch_id = b.id\n      LEFT JOIN users u ON so.created_by = u.id\n      ORDER BY so.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب أوامر المبيعات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات أوامر الشراء\nconst getPurchaseOrders = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        po.*,\n        s.name as supplier_name,\n        b.name as branch_name,\n        w.name as warehouse_name,\n        u.full_name as created_by_name\n      FROM purchase_orders po\n      LEFT JOIN suppliers s ON po.supplier_id = s.id\n      LEFT JOIN branches b ON po.branch_id = b.id\n      LEFT JOIN warehouses w ON po.warehouse_id = w.id\n      LEFT JOIN users u ON po.created_by = u.id\n      ORDER BY po.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب أوامر الشراء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات حركات المخزون\nconst getStockMovements = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        sm.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        w.name as warehouse_name,\n        u.full_name as created_by_name\n      FROM stock_movements sm\n      JOIN products p ON sm.product_id = p.id\n      JOIN warehouses w ON sm.warehouse_id = w.id\n      LEFT JOIN users u ON sm.created_by = u.id\n      ORDER BY sm.created_at DESC\n      LIMIT 100\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب حركات المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات طلبات الصيانة\nconst getMaintenanceRequests = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        mr.*,\n        t.full_name as technician_name,\n        at.full_name as assigned_technician_name,\n        b.name as branch_name,\n        w.name as warehouse_name\n      FROM maintenance_requests mr\n      LEFT JOIN users t ON mr.technician_id = t.id\n      LEFT JOIN users at ON mr.assigned_technician = at.id\n      LEFT JOIN branches b ON mr.branch_id = b.id\n      LEFT JOIN warehouses w ON mr.warehouse_id = w.id\n      ORDER BY mr.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب طلبات الصيانة:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/postgres.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/postgres/data.ts":
/*!****************************************!*\
  !*** ./src/pages/api/postgres/data.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/postgres */ \"(api)/./src/lib/postgres.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"GET\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    const { type } = req.query;\n    try {\n        let result;\n        switch(type){\n            case \"products\":\n                result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getProducts)();\n                break;\n            case \"inventory\":\n                result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getInventory)();\n                break;\n            case \"customers\":\n                result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getCustomers)();\n                break;\n            case \"suppliers\":\n                result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getSuppliers)();\n                break;\n            case \"sales-orders\":\n                result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getSalesOrders)();\n                break;\n            case \"purchase-orders\":\n                result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getPurchaseOrders)();\n                break;\n            case \"stock-movements\":\n                result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getStockMovements)();\n                break;\n            case \"maintenance-requests\":\n                result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getMaintenanceRequests)();\n                break;\n            case \"all\":\n                // جلب جميع البيانات\n                const [products, inventory, customers, suppliers, salesOrders, purchaseOrders, stockMovements, maintenanceRequests] = await Promise.all([\n                    (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getProducts)(),\n                    (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getInventory)(),\n                    (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getCustomers)(),\n                    (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getSuppliers)(),\n                    (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getSalesOrders)(),\n                    (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getPurchaseOrders)(),\n                    (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getStockMovements)(),\n                    (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.getMaintenanceRequests)()\n                ]);\n                return res.status(200).json({\n                    success: true,\n                    data: {\n                        products: products.data || [],\n                        inventory: inventory.data || [],\n                        customers: customers.data || [],\n                        suppliers: suppliers.data || [],\n                        salesOrders: salesOrders.data || [],\n                        purchaseOrders: purchaseOrders.data || [],\n                        stockMovements: stockMovements.data || [],\n                        maintenanceRequests: maintenanceRequests.data || []\n                    },\n                    summary: {\n                        productsCount: products.data?.length || 0,\n                        inventoryCount: inventory.data?.length || 0,\n                        customersCount: customers.data?.length || 0,\n                        suppliersCount: suppliers.data?.length || 0,\n                        salesOrdersCount: salesOrders.data?.length || 0,\n                        purchaseOrdersCount: purchaseOrders.data?.length || 0,\n                        stockMovementsCount: stockMovements.data?.length || 0,\n                        maintenanceRequestsCount: maintenanceRequests.data?.length || 0\n                    }\n                });\n            default:\n                return res.status(400).json({\n                    success: false,\n                    message: \"نوع البيانات غير صحيح\",\n                    availableTypes: [\n                        \"products\",\n                        \"inventory\",\n                        \"customers\",\n                        \"suppliers\",\n                        \"sales-orders\",\n                        \"purchase-orders\",\n                        \"stock-movements\",\n                        \"maintenance-requests\",\n                        \"all\"\n                    ]\n                });\n        }\n        if (result.success) {\n            return res.status(200).json({\n                success: true,\n                data: result.data,\n                count: result.data?.length || 0\n            });\n        } else {\n            return res.status(500).json({\n                success: false,\n                message: \"فشل في جلب البيانات\",\n                error: result.error\n            });\n        }\n    } catch (error) {\n        console.error(\"خطأ في API البيانات:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/postgres/data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fdata&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Cdata.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();