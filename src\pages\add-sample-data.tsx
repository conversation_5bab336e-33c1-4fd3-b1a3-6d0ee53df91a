import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  XCircle, 
  Database, 
  Users,
  Package,
  ShoppingCart,
  Truck,
  BarChart3
} from 'lucide-react'

export default function AddSampleData() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)

  const addSampleData = async () => {
    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/postgres/add-sample-data', {
        method: 'POST'
      })
      
      const data = await response.json()
      setResult(data)
      
    } catch (err) {
      setResult({
        success: false,
        message: 'خطأ في إضافة البيانات التجريبية'
      })
    } finally {
      setLoading(false)
    }
  }

  const dataTypes = [
    {
      title: 'العملاء',
      count: '5 عملاء',
      icon: <Users className="h-8 w-8 text-blue-600" />,
      description: 'شركات وعملاء متنوعين'
    },
    {
      title: 'الموردين',
      count: '3 موردين',
      icon: <Truck className="h-8 w-8 text-green-600" />,
      description: 'موردين للإلكترونيات وقطع الغيار'
    },
    {
      title: 'المنتجات',
      count: '8 منتجات',
      icon: <Package className="h-8 w-8 text-purple-600" />,
      description: 'لابتوبات، هواتف، إكسسوارات'
    },
    {
      title: 'أوامر المبيعات',
      count: '3 أوامر',
      icon: <ShoppingCart className="h-8 w-8 text-orange-600" />,
      description: 'أوامر مبيعات بحالات مختلفة'
    },
    {
      title: 'حركات المخزون',
      count: '5 حركات',
      icon: <BarChart3 className="h-8 w-8 text-red-600" />,
      description: 'مبيعات ومشتريات'
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="bg-white p-4 rounded-full shadow-lg inline-block mb-4">
            <Database className="h-12 w-12 text-blue-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            إضافة بيانات تجريبية
          </h1>
          <p className="text-xl text-gray-600">
            إضافة بيانات تجريبية لاختبار النظام
          </p>
        </div>

        {/* Data Types */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {dataTypes.map((type, index) => (
            <Card key={index} className="bg-white shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-6 text-center">
                <div className="flex justify-center mb-4">
                  {type.icon}
                </div>
                <h3 className="text-lg font-semibold mb-2">{type.title}</h3>
                <p className="text-2xl font-bold text-blue-600 mb-2">{type.count}</p>
                <p className="text-gray-600 text-sm">{type.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Add Data Section */}
        <Card className="bg-white shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">إضافة البيانات التجريبية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {!result && !loading && (
              <div className="text-center space-y-4">
                <p className="text-gray-600">
                  سيتم إضافة بيانات تجريبية شاملة لجميع المديولات
                </p>
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="font-medium text-yellow-800 mb-2">⚠️ تنبيه</h4>
                  <p className="text-yellow-700 text-sm">
                    هذه البيانات للاختبار فقط. يمكنك حذفها لاحقاً وإضافة بياناتك الحقيقية.
                  </p>
                </div>
                <Button 
                  onClick={addSampleData}
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3"
                >
                  <Database className="h-5 w-5 mr-2" />
                  إضافة البيانات التجريبية
                </Button>
              </div>
            )}

            {loading && (
              <div className="text-center space-y-4">
                <Database className="h-12 w-12 mx-auto text-blue-600 animate-pulse" />
                <h3 className="text-lg font-semibold">جاري إضافة البيانات...</h3>
                <p className="text-gray-600">يرجى الانتظار حتى اكتمال العملية</p>
              </div>
            )}

            {result && (
              <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                {result.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription>
                  <div className="space-y-4">
                    <p className="font-medium text-lg">{result.message}</p>
                    
                    {result.data && (
                      <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-blue-600">{result.data.customers}</p>
                          <p className="text-gray-600">عملاء</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-green-600">{result.data.suppliers}</p>
                          <p className="text-gray-600">موردين</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-purple-600">{result.data.products}</p>
                          <p className="text-gray-600">منتجات</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-orange-600">{result.data.salesOrders}</p>
                          <p className="text-gray-600">أوامر مبيعات</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-red-600">{result.data.stockMovements}</p>
                          <p className="text-gray-600">حركات مخزون</p>
                        </div>
                      </div>
                    )}

                    <div className="flex gap-4 pt-4">
                      <Button 
                        onClick={() => window.location.href = '/products'}
                        variant="outline"
                      >
                        عرض المنتجات
                      </Button>
                      <Button 
                        onClick={() => window.location.href = '/sales'}
                        variant="outline"
                      >
                        عرض المبيعات
                      </Button>
                      <Button 
                        onClick={() => window.location.href = '/dashboard'}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        لوحة التحكم
                      </Button>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
