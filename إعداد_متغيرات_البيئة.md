# إعداد متغيرات البيئة - خطوة بخطوة 🔧

## المشكلة الحالية ❌
```
Failed to load resource: net::ERR_NAME_NOT_RESOLVED
your-project-id.supabase.co/rest/v1/branches
```

هذا يعني أن متغيرات البيئة لا تزال تحتوي على قيم وهمية!

## الحل خطوة بخطوة ✅

### الخطوة 1: إنشاء مشروع Supabase

1. **اذهب إلى**: [supabase.com](https://supabase.com)
2. **سجل دخول** أو أنشئ حساب جديد
3. **انقر على "New Project"**
4. **اختر Organization** أو أنشئ واحدة جديدة
5. **أدخل تفاصيل المشروع**:
   - **Name**: Business Management System
   - **Database Password**: كلمة مرور قوية (احفظها!)
   - **Region**: اختر الأقرب لك
6. **انقر "Create new project"**
7. **انتظر** حتى يكتمل إنشاء المشروع (2-3 دقائق)

### الخطوة 2: الحصول على مفاتيح API

1. **في لوحة تحكم Supabase**، اذهب إلى:
   ```
   Settings > API
   ```

2. **انسخ القيم التالية**:
   - **Project URL**: `https://xxxxx.supabase.co`
   - **anon public**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **service_role**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### الخطوة 3: تحديث ملف .env.local

1. **افتح ملف `.env.local`** في مجلد المشروع
2. **استبدل القيم الوهمية** بالقيم الحقيقية:

```env
# استبدل هذه القيم بالقيم الحقيقية من Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-actual-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-actual-anon-key-here
SUPABASE_SERVICE_ROLE_KEY=your-actual-service-role-key-here

# باقي الإعدادات (لا تغيرها)
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret
NODE_ENV=development
```

### الخطوة 4: تطبيق قاعدة البيانات

1. **في Supabase**، اذهب إلى:
   ```
   SQL Editor
   ```

2. **انسخ محتوى ملف**:
   ```
   supabase/migrations/20240101000000_initial_schema.sql
   ```

3. **الصق في SQL Editor** واضغط **"RUN"**

4. **انسخ محتوى ملف**:
   ```
   supabase/migrations/20240130000000_maintenance_system.sql
   ```

5. **الصق في SQL Editor** واضغط **"RUN"**

### الخطوة 5: إعادة تشغيل النظام

1. **أوقف الخادم** (Ctrl+C في Terminal)
2. **أعد تشغيله**:
   ```bash
   npm run dev
   ```

3. **اذهب إلى**: `http://localhost:3000`

## التحقق من النجاح ✅

إذا تم كل شيء بشكل صحيح، سترى:
- ✅ لا توجد أخطاء في Console
- ✅ صفحة تسجيل الدخول تعمل
- ✅ يمكن الوصول لصفحة الإعداد

## استكشاف الأخطاء 🔧

### خطأ: "Invalid API key"
**الحل**: تأكد من نسخ المفاتيح بشكل صحيح

### خطأ: "relation does not exist"
**الحل**: تطبيق ملفات migration في SQL Editor

### خطأ: "CORS error"
**الحل**: تأكد من إعداد Domain في Supabase Authentication

---

**⚠️ مهم**: احفظ كلمة مرور قاعدة البيانات في مكان آمن!
