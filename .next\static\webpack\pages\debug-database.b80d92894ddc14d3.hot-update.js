"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/debug-database",{

/***/ "./src/pages/debug-database.tsx":
/*!**************************************!*\
  !*** ./src/pages/debug-database.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugDatabase; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"./src/components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Copy,Database,Play,XCircle!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Copy,Database,Play,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DebugDatabase() {\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [setupLoading, setSetupLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sql, setSql] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"-- اختبار بسيط\\nSELECT NOW() as current_time, 'اختبار الاتصال' as message;\");\n    const testQueries = [\n        {\n            name: \"اختبار الاتصال\",\n            sql: \"SELECT NOW() as current_time, 'اختبار الاتصال' as message;\"\n        },\n        {\n            name: \"فحص الجداول الموجودة\",\n            sql: \"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;\"\n        },\n        {\n            name: \"إنشاء نوع مخصص\",\n            sql: \"DO $$\\nBEGIN\\n    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN\\n        CREATE TYPE user_role AS ENUM ('admin', 'manager', 'employee', 'cashier');\\n    END IF;\\nEND $$;\"\n        },\n        {\n            name: \"إنشاء جدول الفروع\",\n            sql: \"CREATE TABLE IF NOT EXISTS branches (\\n    id SERIAL PRIMARY KEY,\\n    name VARCHAR(255) NOT NULL,\\n    address TEXT NOT NULL,\\n    phone VARCHAR(50),\\n    email VARCHAR(255),\\n    is_active BOOLEAN DEFAULT true,\\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\\n);\"\n        },\n        {\n            name: \"إدراج بيانات تجريبية\",\n            sql: \"INSERT INTO branches (name, address, phone, email)\\nVALUES ('الفرع الرئيسي', 'العنوان الرئيسي للشركة', '+201234567890', '<EMAIL>')\\nON CONFLICT DO NOTHING;\"\n        }\n    ];\n    const executeQuery = async (queryToRun)=>{\n        setLoading(true);\n        setResult(null);\n        try {\n            const response = await fetch(\"/api/postgres/single-query\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sql: queryToRun || sql\n                })\n            });\n            const data = await response.json();\n            setResult(data);\n        } catch (err) {\n            setResult({\n                success: false,\n                message: \"خطأ في الاتصال بالخادم\",\n                error: err instanceof Error ? err.message : \"Unknown error\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const setupSimple = async ()=>{\n        setSetupLoading(true);\n        setResult(null);\n        try {\n            const response = await fetch(\"/api/postgres/setup-simple\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            setResult(data);\n        } catch (err) {\n            setResult({\n                success: false,\n                message: \"خطأ في تنفيذ الإعداد المبسط\",\n                error: err instanceof Error ? err.message : \"Unknown error\"\n            });\n        } finally{\n            setSetupLoading(false);\n        }\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"تشخيص قاعدة البيانات PostgreSQL\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"اختبار الاستعلامات خطوة بخطوة لفهم المشكلة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"اختبارات سريعة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 130,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                children: testQueries.map((test, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>executeQuery(test.sql),\n                                        disabled: loading,\n                                        className: \"h-auto p-4 text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: test.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: [\n                                                        test.sql.substring(0, 50),\n                                                        \"...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 145,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 134,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"استعلام مخصص\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                    value: sql,\n                                    onChange: (e)=>setSql(e.target.value),\n                                    placeholder: \"أدخل استعلام SQL هنا...\",\n                                    rows: 8,\n                                    className: \"font-mono text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>executeQuery(),\n                                            disabled: loading,\n                                            className: \"flex items-center\",\n                                            children: [\n                                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Database, {\n                                                    className: \"h-4 w-4 mr-2 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 175,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Play, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 19\n                                                }, this),\n                                                loading ? \"جاري التنفيذ...\" : \"تنفيذ الاستعلام\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>copyToClipboard(sql),\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Copy, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 186,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"نسخ\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this),\n                result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    result.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.CheckCircle, {\n                                        className: \"h-5 w-5 mr-2 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.XCircle, {\n                                        className: \"h-5 w-5 mr-2 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"نتيجة الاستعلام\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 197,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                className: result.success ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: result.message\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 21\n                                            }, this),\n                                            result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-100 p-3 rounded text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"خطأ:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \" \",\n                                                    JSON.stringify(result.error, null, 2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 23\n                                            }, this),\n                                            result.data && result.data.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-100 p-3 rounded text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"البيانات:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"mt-2 overflow-auto\",\n                                                        children: JSON.stringify(result.data, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 23\n                                            }, this),\n                                            result.rowCount !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    \"عدد الصفوف المتأثرة: \",\n                                                    result.rowCount\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 209,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 207,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 206,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"معلومات قاعدة البيانات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"المضيف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: process.env.NEXT_PUBLIC_DB_HOST || \"localhost\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 244,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"المنفذ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: process.env.NEXT_PUBLIC_DB_PORT || \"5432\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 248,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"قاعدة البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: process.env.NEXT_PUBLIC_DB_NAME || \"Vero_ERP_ABA\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"المستخدم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: process.env.NEXT_PUBLIC_DB_USER || \"openpg\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 243,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 242,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 238,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n            lineNumber: 117,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n        lineNumber: 116,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugDatabase, \"hm71hE+kApM6ptN9udLgQN9AHhI=\");\n_c = DebugDatabase;\nvar _c;\n$RefreshReg$(_c, \"DebugDatabase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvZGVidWctZGF0YWJhc2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBZ0M7QUFDK0M7QUFDaEM7QUFDZ0I7QUFDWjtBQVE5QjtBQUVOLFNBQVNjOztJQUN0QixNQUFNLENBQUNDLFFBQVFDLFVBQVUsR0FBR2hCLCtDQUFRQSxDQUFNO0lBQzFDLE1BQU0sQ0FBQ2lCLFNBQVNDLFdBQVcsR0FBR2xCLCtDQUFRQSxDQUFDO0lBQ3ZDLE1BQU0sQ0FBQ21CLGNBQWNDLGdCQUFnQixHQUFHcEIsK0NBQVFBLENBQUM7SUFDakQsTUFBTSxDQUFDcUIsS0FBS0MsT0FBTyxHQUFHdEIsK0NBQVFBLENBQUU7SUFHaEMsTUFBTXVCLGNBQWM7UUFDbEI7WUFDRUMsTUFBTTtZQUNOSCxLQUFNO1FBQ1I7UUFDQTtZQUNFRyxNQUFNO1lBQ05ILEtBQU07UUFDUjtRQUNBO1lBQ0VHLE1BQU07WUFDTkgsS0FBTTtRQU1SO1FBQ0E7WUFDRUcsTUFBTTtZQUNOSCxLQUFNO1FBVVI7UUFDQTtZQUNFRyxNQUFNO1lBQ05ILEtBQU07UUFHUjtLQUNEO0lBRUQsTUFBTUksZUFBZSxPQUFPQztRQUMxQlIsV0FBVztRQUNYRixVQUFVO1FBRVYsSUFBSTtZQUNGLE1BQU1XLFdBQVcsTUFBTUMsTUFBTSw4QkFBOEI7Z0JBQ3pEQyxRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJaLEtBQUtLLGNBQWNMO2dCQUNyQjtZQUNGO1lBRUEsTUFBTWEsT0FBTyxNQUFNUCxTQUFTUSxJQUFJO1lBQ2hDbkIsVUFBVWtCO1FBQ1osRUFBRSxPQUFPRSxLQUFLO1lBQ1pwQixVQUFVO2dCQUNScUIsU0FBUztnQkFDVEMsU0FBUztnQkFDVEMsT0FBT0gsZUFBZUksUUFBUUosSUFBSUUsT0FBTyxHQUFHO1lBQzlDO1FBQ0YsU0FBVTtZQUNScEIsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNdUIsY0FBYztRQUNsQnJCLGdCQUFnQjtRQUNoQkosVUFBVTtRQUVWLElBQUk7WUFDRixNQUFNVyxXQUFXLE1BQU1DLE1BQU0sOEJBQThCO2dCQUN6REMsUUFBUTtZQUNWO1lBRUEsTUFBTUssT0FBTyxNQUFNUCxTQUFTUSxJQUFJO1lBQ2hDbkIsVUFBVWtCO1FBQ1osRUFBRSxPQUFPRSxLQUFLO1lBQ1pwQixVQUFVO2dCQUNScUIsU0FBUztnQkFDVEMsU0FBUztnQkFDVEMsT0FBT0gsZUFBZUksUUFBUUosSUFBSUUsT0FBTyxHQUFHO1lBQzlDO1FBQ0YsU0FBVTtZQUNSbEIsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQSxNQUFNc0Isa0JBQWtCLENBQUNDO1FBQ3ZCQyxVQUFVQyxTQUFTLENBQUNDLFNBQVMsQ0FBQ0g7SUFDaEM7SUFFQSxxQkFDRSw4REFBQ0k7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBRWIsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0M7NEJBQUdELFdBQVU7c0NBQXdDOzs7Ozs7c0NBR3RELDhEQUFDRTs0QkFBRUYsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs4QkFNL0IsOERBQUMvQyxxREFBSUE7O3NDQUNILDhEQUFDRSwyREFBVUE7c0NBQ1QsNEVBQUNDLDBEQUFTQTswQ0FBQzs7Ozs7Ozs7Ozs7c0NBRWIsOERBQUNGLDREQUFXQTtzQ0FDViw0RUFBQzZDO2dDQUFJQyxXQUFVOzBDQUNaekIsWUFBWTRCLEdBQUcsQ0FBQyxDQUFDQyxNQUFNQyxzQkFDdEIsOERBQUNoRCx5REFBTUE7d0NBRUxpRCxTQUFRO3dDQUNSQyxTQUFTLElBQU05QixhQUFhMkIsS0FBSy9CLEdBQUc7d0NBQ3BDbUMsVUFBVXZDO3dDQUNWK0IsV0FBVTtrREFFViw0RUFBQ0Q7OzhEQUNDLDhEQUFDQTtvREFBSUMsV0FBVTs4REFBZUksS0FBSzVCLElBQUk7Ozs7Ozs4REFDdkMsOERBQUN1QjtvREFBSUMsV0FBVTs7d0RBQ1pJLEtBQUsvQixHQUFHLENBQUNvQyxTQUFTLENBQUMsR0FBRzt3REFBSTs7Ozs7Ozs7Ozs7Ozt1Q0FUMUJKOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBbUJmLDhEQUFDcEQscURBQUlBOztzQ0FDSCw4REFBQ0UsMkRBQVVBO3NDQUNULDRFQUFDQywwREFBU0E7MENBQUM7Ozs7Ozs7Ozs7O3NDQUViLDhEQUFDRiw0REFBV0E7NEJBQUM4QyxXQUFVOzs4Q0FDckIsOERBQUN4Qyw2REFBUUE7b0NBQ1BrRCxPQUFPckM7b0NBQ1BzQyxVQUFVLENBQUNDLElBQU10QyxPQUFPc0MsRUFBRUMsTUFBTSxDQUFDSCxLQUFLO29DQUN0Q0ksYUFBWTtvQ0FDWkMsTUFBTTtvQ0FDTmYsV0FBVTs7Ozs7OzhDQUVaLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ2IsOERBQUMzQyx5REFBTUE7NENBQ0xrRCxTQUFTLElBQU05Qjs0Q0FDZitCLFVBQVV2Qzs0Q0FDVitCLFdBQVU7O2dEQUVUL0Isd0JBQ0MsOERBQUNOLGdIQUFRQTtvREFBQ3FDLFdBQVU7Ozs7O3lFQUVwQiw4REFBQ3BDLDRHQUFJQTtvREFBQ29DLFdBQVU7Ozs7OztnREFFakIvQixVQUFVLG9CQUFvQjs7Ozs7OztzREFFakMsOERBQUNaLHlEQUFNQTs0Q0FDTGlELFNBQVE7NENBQ1JDLFNBQVMsSUFBTWIsZ0JBQWdCckI7NENBQy9CMkIsV0FBVTs7OERBRVYsOERBQUNuQyw0R0FBSUE7b0RBQUNtQyxXQUFVOzs7Ozs7Z0RBQWlCOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O2dCQVF4Q2pDLHdCQUNDLDhEQUFDZCxxREFBSUE7O3NDQUNILDhEQUFDRSwyREFBVUE7c0NBQ1QsNEVBQUNDLDBEQUFTQTtnQ0FBQzRDLFdBQVU7O29DQUNsQmpDLE9BQU9zQixPQUFPLGlCQUNiLDhEQUFDNUIsbUhBQVdBO3dDQUFDdUMsV0FBVTs7Ozs7NkRBRXZCLDhEQUFDdEMsK0dBQU9BO3dDQUFDc0MsV0FBVTs7Ozs7O29DQUNuQjs7Ozs7Ozs7Ozs7O3NDQUlOLDhEQUFDOUMsNERBQVdBO3NDQUNWLDRFQUFDSSx1REFBS0E7Z0NBQUMwQyxXQUFXakMsT0FBT3NCLE9BQU8sR0FBRyxpQ0FBaUM7MENBQ2xFLDRFQUFDOUIsa0VBQWdCQTs4Q0FDZiw0RUFBQ3dDO3dDQUFJQyxXQUFVOzswREFDYiw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQWVqQyxPQUFPdUIsT0FBTzs7Ozs7OzRDQUV6Q3ZCLE9BQU93QixLQUFLLGtCQUNYLDhEQUFDUTtnREFBSUMsV0FBVTs7a0VBQ2IsOERBQUNnQjtrRUFBTzs7Ozs7O29EQUFhO29EQUFFaEMsS0FBS0MsU0FBUyxDQUFDbEIsT0FBT3dCLEtBQUssRUFBRSxNQUFNOzs7Ozs7OzRDQUk3RHhCLE9BQU9tQixJQUFJLElBQUluQixPQUFPbUIsSUFBSSxDQUFDK0IsTUFBTSxHQUFHLG1CQUNuQyw4REFBQ2xCO2dEQUFJQyxXQUFVOztrRUFDYiw4REFBQ2dCO2tFQUFPOzs7Ozs7a0VBQ1IsOERBQUNFO3dEQUFJbEIsV0FBVTtrRUFDWmhCLEtBQUtDLFNBQVMsQ0FBQ2xCLE9BQU9tQixJQUFJLEVBQUUsTUFBTTs7Ozs7Ozs7Ozs7OzRDQUt4Q25CLE9BQU9vRCxRQUFRLEtBQUtDLDJCQUNuQiw4REFBQ2xCO2dEQUFFRixXQUFVOztvREFBVTtvREFBc0JqQyxPQUFPb0QsUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs4QkFVMUUsOERBQUNsRSxxREFBSUE7O3NDQUNILDhEQUFDRSwyREFBVUE7c0NBQ1QsNEVBQUNDLDBEQUFTQTswQ0FBQzs7Ozs7Ozs7Ozs7c0NBRWIsOERBQUNGLDREQUFXQTtzQ0FDViw0RUFBQzZDO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7OzBEQUNDLDhEQUFDRztnREFBRUYsV0FBVTswREFBZ0I7Ozs7OzswREFDN0IsOERBQUNFO2dEQUFFRixXQUFVOzBEQUFlcUIsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDQyxtQkFBbUIsSUFBSTs7Ozs7Ozs7Ozs7O2tEQUVqRSw4REFBQ3hCOzswREFDQyw4REFBQ0c7Z0RBQUVGLFdBQVU7MERBQWdCOzs7Ozs7MERBQzdCLDhEQUFDRTtnREFBRUYsV0FBVTswREFBZXFCLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0UsbUJBQW1CLElBQUk7Ozs7Ozs7Ozs7OztrREFFakUsOERBQUN6Qjs7MERBQ0MsOERBQUNHO2dEQUFFRixXQUFVOzBEQUFnQjs7Ozs7OzBEQUM3Qiw4REFBQ0U7Z0RBQUVGLFdBQVU7MERBQWVxQixPQUFPQSxDQUFDQyxHQUFHLENBQUNHLG1CQUFtQixJQUFJOzs7Ozs7Ozs7Ozs7a0RBRWpFLDhEQUFDMUI7OzBEQUNDLDhEQUFDRztnREFBRUYsV0FBVTswREFBZ0I7Ozs7OzswREFDN0IsOERBQUNFO2dEQUFFRixXQUFVOzBEQUFlcUIsT0FBT0EsQ0FBQ0MsR0FBRyxDQUFDSSxtQkFBbUIsSUFBSTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVEvRTtHQTNQd0I1RDtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvcGFnZXMvZGVidWctZGF0YWJhc2UudHN4P2JiZjciXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdXNlU3RhdGUgfSBmcm9tICdyZWFjdCdcbmltcG9ydCB7IENhcmQsIENhcmRDb250ZW50LCBDYXJkSGVhZGVyLCBDYXJkVGl0bGUgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvY2FyZCdcbmltcG9ydCB7IEJ1dHRvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9idXR0b24nXG5pbXBvcnQgeyBBbGVydCwgQWxlcnREZXNjcmlwdGlvbiB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9hbGVydCdcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RleHRhcmVhJ1xuaW1wb3J0IHtcbiAgQ2hlY2tDaXJjbGUsXG4gIFhDaXJjbGUsXG4gIEFsZXJ0VHJpYW5nbGUsXG4gIERhdGFiYXNlLFxuICBQbGF5LFxuICBDb3B5XG59IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRGVidWdEYXRhYmFzZSgpIHtcbiAgY29uc3QgW3Jlc3VsdCwgc2V0UmVzdWx0XSA9IHVzZVN0YXRlPGFueT4obnVsbClcbiAgY29uc3QgW2xvYWRpbmcsIHNldExvYWRpbmddID0gdXNlU3RhdGUoZmFsc2UpXG4gIGNvbnN0IFtzZXR1cExvYWRpbmcsIHNldFNldHVwTG9hZGluZ10gPSB1c2VTdGF0ZShmYWxzZSlcbiAgY29uc3QgW3NxbCwgc2V0U3FsXSA9IHVzZVN0YXRlKGAtLSDYp9iu2KrYqNin2LEg2KjYs9mK2LdcblNFTEVDVCBOT1coKSBhcyBjdXJyZW50X3RpbWUsICfYp9iu2KrYqNin2LEg2KfZhNin2KrYtdin2YQnIGFzIG1lc3NhZ2U7YClcblxuICBjb25zdCB0ZXN0UXVlcmllcyA9IFtcbiAgICB7XG4gICAgICBuYW1lOiAn2KfYrtiq2KjYp9ixINin2YTYp9iq2LXYp9mEJyxcbiAgICAgIHNxbDogYFNFTEVDVCBOT1coKSBhcyBjdXJyZW50X3RpbWUsICfYp9iu2KrYqNin2LEg2KfZhNin2KrYtdin2YQnIGFzIG1lc3NhZ2U7YFxuICAgIH0sXG4gICAge1xuICAgICAgbmFtZTogJ9mB2K3YtSDYp9mE2KzYr9in2YjZhCDYp9mE2YXZiNis2YjYr9ipJyxcbiAgICAgIHNxbDogYFNFTEVDVCB0YWJsZV9uYW1lIEZST00gaW5mb3JtYXRpb25fc2NoZW1hLnRhYmxlcyBXSEVSRSB0YWJsZV9zY2hlbWEgPSAncHVibGljJyBPUkRFUiBCWSB0YWJsZV9uYW1lO2BcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYpdmG2LTYp9ihINmG2YjYuSDZhdiu2LXYtScsXG4gICAgICBzcWw6IGBETyAkJFxuQkVHSU5cbiAgICBJRiBOT1QgRVhJU1RTIChTRUxFQ1QgMSBGUk9NIHBnX3R5cGUgV0hFUkUgdHlwbmFtZSA9ICd1c2VyX3JvbGUnKSBUSEVOXG4gICAgICAgIENSRUFURSBUWVBFIHVzZXJfcm9sZSBBUyBFTlVNICgnYWRtaW4nLCAnbWFuYWdlcicsICdlbXBsb3llZScsICdjYXNoaWVyJyk7XG4gICAgRU5EIElGO1xuRU5EICQkO2BcbiAgICB9LFxuICAgIHtcbiAgICAgIG5hbWU6ICfYpdmG2LTYp9ihINis2K/ZiNmEINin2YTZgdix2YjYuScsXG4gICAgICBzcWw6IGBDUkVBVEUgVEFCTEUgSUYgTk9UIEVYSVNUUyBicmFuY2hlcyAoXG4gICAgaWQgU0VSSUFMIFBSSU1BUlkgS0VZLFxuICAgIG5hbWUgVkFSQ0hBUigyNTUpIE5PVCBOVUxMLFxuICAgIGFkZHJlc3MgVEVYVCBOT1QgTlVMTCxcbiAgICBwaG9uZSBWQVJDSEFSKDUwKSxcbiAgICBlbWFpbCBWQVJDSEFSKDI1NSksXG4gICAgaXNfYWN0aXZlIEJPT0xFQU4gREVGQVVMVCB0cnVlLFxuICAgIGNyZWF0ZWRfYXQgVElNRVNUQU1QIFdJVEggVElNRSBaT05FIERFRkFVTFQgTk9XKCksXG4gICAgdXBkYXRlZF9hdCBUSU1FU1RBTVAgV0lUSCBUSU1FIFpPTkUgREVGQVVMVCBOT1coKVxuKTtgXG4gICAgfSxcbiAgICB7XG4gICAgICBuYW1lOiAn2KXYr9ix2KfYrCDYqNmK2KfZhtin2Kog2KrYrNix2YrYqNmK2KknLFxuICAgICAgc3FsOiBgSU5TRVJUIElOVE8gYnJhbmNoZXMgKG5hbWUsIGFkZHJlc3MsIHBob25lLCBlbWFpbClcblZBTFVFUyAoJ9in2YTZgdix2Lkg2KfZhNix2KbZitiz2YonLCAn2KfZhNi52YbZiNin2YYg2KfZhNix2KbZitiz2Yog2YTZhNi02LHZg9ipJywgJysyMDEyMzQ1Njc4OTAnLCAnbWFpbkBjb21wYW55LmNvbScpXG5PTiBDT05GTElDVCBETyBOT1RISU5HO2BcbiAgICB9XG4gIF1cblxuICBjb25zdCBleGVjdXRlUXVlcnkgPSBhc3luYyAocXVlcnlUb1J1bj86IHN0cmluZykgPT4ge1xuICAgIHNldExvYWRpbmcodHJ1ZSlcbiAgICBzZXRSZXN1bHQobnVsbClcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3Bvc3RncmVzL3NpbmdsZS1xdWVyeScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nXG4gICAgICAgIH0sXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgICBzcWw6IHF1ZXJ5VG9SdW4gfHwgc3FsXG4gICAgICAgIH0pXG4gICAgICB9KVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpXG4gICAgICBzZXRSZXN1bHQoZGF0YSlcbiAgICB9IGNhdGNoIChlcnIpIHtcbiAgICAgIHNldFJlc3VsdCh7XG4gICAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgICBtZXNzYWdlOiAn2K7Yt9ijINmB2Yog2KfZhNin2KrYtdin2YQg2KjYp9mE2K7Yp9iv2YUnLFxuICAgICAgICBlcnJvcjogZXJyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnIubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJ1xuICAgICAgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSlcbiAgICB9XG4gIH1cblxuICBjb25zdCBzZXR1cFNpbXBsZSA9IGFzeW5jICgpID0+IHtcbiAgICBzZXRTZXR1cExvYWRpbmcodHJ1ZSlcbiAgICBzZXRSZXN1bHQobnVsbClcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3Bvc3RncmVzL3NldHVwLXNpbXBsZScsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCdcbiAgICAgIH0pXG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKClcbiAgICAgIHNldFJlc3VsdChkYXRhKVxuICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgc2V0UmVzdWx0KHtcbiAgICAgICAgc3VjY2VzczogZmFsc2UsXG4gICAgICAgIG1lc3NhZ2U6ICfYrti32KMg2YHZiiDYqtmG2YHZitiwINin2YTYpdi52K/Yp9ivINin2YTZhdio2LPYtycsXG4gICAgICAgIGVycm9yOiBlcnIgaW5zdGFuY2VvZiBFcnJvciA/IGVyci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InXG4gICAgICB9KVxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRTZXR1cExvYWRpbmcoZmFsc2UpXG4gICAgfVxuICB9XG5cbiAgY29uc3QgY29weVRvQ2xpcGJvYXJkID0gKHRleHQ6IHN0cmluZykgPT4ge1xuICAgIG5hdmlnYXRvci5jbGlwYm9hcmQud3JpdGVUZXh0KHRleHQpXG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwibWluLWgtc2NyZWVuIGJnLWdyYXktNTAgcC02XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1heC13LTZ4bCBteC1hdXRvIHNwYWNlLXktNlwiPlxuICAgICAgICB7LyogSGVhZGVyICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyXCI+XG4gICAgICAgICAgPGgxIGNsYXNzTmFtZT1cInRleHQtM3hsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIG1iLTJcIj5cbiAgICAgICAgICAgINiq2LTYrtmK2LUg2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqIFBvc3RncmVTUUxcbiAgICAgICAgICA8L2gxPlxuICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj5cbiAgICAgICAgICAgINin2K7Yqtio2KfYsSDYp9mE2KfYs9iq2LnZhNin2YXYp9iqINiu2LfZiNipINio2K7Yt9mI2Kkg2YTZgdmH2YUg2KfZhNmF2LTZg9mE2KlcbiAgICAgICAgICA8L3A+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBRdWljayBUZXN0cyAqL31cbiAgICAgICAgPENhcmQ+XG4gICAgICAgICAgPENhcmRIZWFkZXI+XG4gICAgICAgICAgICA8Q2FyZFRpdGxlPtin2K7Yqtio2KfYsdin2Kog2LPYsdmK2LnYqTwvQ2FyZFRpdGxlPlxuICAgICAgICAgIDwvQ2FyZEhlYWRlcj5cbiAgICAgICAgICA8Q2FyZENvbnRlbnQ+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgbWQ6Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTRcIj5cbiAgICAgICAgICAgICAge3Rlc3RRdWVyaWVzLm1hcCgodGVzdCwgaW5kZXgpID0+IChcbiAgICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxuICAgICAgICAgICAgICAgICAgdmFyaWFudD1cIm91dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZXhlY3V0ZVF1ZXJ5KHRlc3Quc3FsKX1cbiAgICAgICAgICAgICAgICAgIGRpc2FibGVkPXtsb2FkaW5nfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC1hdXRvIHAtNCB0ZXh0LXJpZ2h0XCJcbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3Rlc3QubmFtZX08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbXQtMVwiPlxuICAgICAgICAgICAgICAgICAgICAgIHt0ZXN0LnNxbC5zdWJzdHJpbmcoMCwgNTApfS4uLlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cblxuICAgICAgICB7LyogQ3VzdG9tIFF1ZXJ5ICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGU+2KfYs9iq2LnZhNin2YUg2YXYrti12LU8L0NhcmRUaXRsZT5cbiAgICAgICAgICA8L0NhcmRIZWFkZXI+XG4gICAgICAgICAgPENhcmRDb250ZW50IGNsYXNzTmFtZT1cInNwYWNlLXktNFwiPlxuICAgICAgICAgICAgPFRleHRhcmVhXG4gICAgICAgICAgICAgIHZhbHVlPXtzcWx9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gc2V0U3FsKGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCLYo9iv2K7ZhCDYp9iz2KrYudmE2KfZhSBTUUwg2YfZhtinLi4uXCJcbiAgICAgICAgICAgICAgcm93cz17OH1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9udC1tb25vIHRleHQtc21cIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgICAgICA8QnV0dG9uXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gZXhlY3V0ZVF1ZXJ5KCl9XG4gICAgICAgICAgICAgICAgZGlzYWJsZWQ9e2xvYWRpbmd9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAge2xvYWRpbmcgPyAoXG4gICAgICAgICAgICAgICAgICA8RGF0YWJhc2UgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yIGFuaW1hdGUtcHVsc2VcIiAvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8UGxheSBjbGFzc05hbWU9XCJoLTQgdy00IG1yLTJcIiAvPlxuICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAge2xvYWRpbmcgPyAn2KzYp9ix2Yog2KfZhNiq2YbZgdmK2LAuLi4nIDogJ9iq2YbZgdmK2LAg2KfZhNin2LPYqti52YTYp9mFJ31cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZVwiXG4gICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gY29weVRvQ2xpcGJvYXJkKHNxbCl9XG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXJcIlxuICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgPENvcHkgY2xhc3NOYW1lPVwiaC00IHctNCBtci0yXCIgLz5cbiAgICAgICAgICAgICAgICDZhtiz2K5cbiAgICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L0NhcmRDb250ZW50PlxuICAgICAgICA8L0NhcmQ+XG5cbiAgICAgICAgey8qIFJlc3VsdCAqL31cbiAgICAgICAge3Jlc3VsdCAmJiAoXG4gICAgICAgICAgPENhcmQ+XG4gICAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgICAgPENhcmRUaXRsZSBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlclwiPlxuICAgICAgICAgICAgICAgIHtyZXN1bHQuc3VjY2VzcyA/IChcbiAgICAgICAgICAgICAgICAgIDxDaGVja0NpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1ncmVlbi02MDBcIiAvPlxuICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICA8WENpcmNsZSBjbGFzc05hbWU9XCJoLTUgdy01IG1yLTIgdGV4dC1yZWQtNjAwXCIgLz5cbiAgICAgICAgICAgICAgICApfVxuICAgICAgICAgICAgICAgINmG2KrZitis2Kkg2KfZhNin2LPYqti52YTYp9mFXG4gICAgICAgICAgICAgIDwvQ2FyZFRpdGxlPlxuICAgICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgICAgPENhcmRDb250ZW50PlxuICAgICAgICAgICAgICA8QWxlcnQgY2xhc3NOYW1lPXtyZXN1bHQuc3VjY2VzcyA/ICdib3JkZXItZ3JlZW4tMjAwIGJnLWdyZWVuLTUwJyA6ICdib3JkZXItcmVkLTIwMCBiZy1yZWQtNTAnfT5cbiAgICAgICAgICAgICAgICA8QWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0yXCI+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3Jlc3VsdC5tZXNzYWdlfTwvcD5cblxuICAgICAgICAgICAgICAgICAgICB7cmVzdWx0LmVycm9yICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXJlZC0xMDAgcC0zIHJvdW5kZWQgdGV4dC1zbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHN0cm9uZz7Yrti32KM6PC9zdHJvbmc+IHtKU09OLnN0cmluZ2lmeShyZXN1bHQuZXJyb3IsIG51bGwsIDIpfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICApfVxuXG4gICAgICAgICAgICAgICAgICAgIHtyZXN1bHQuZGF0YSAmJiByZXN1bHQuZGF0YS5sZW5ndGggPiAwICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLWdyZWVuLTEwMCBwLTMgcm91bmRlZCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8c3Ryb25nPtin2YTYqNmK2KfZhtin2Ko6PC9zdHJvbmc+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cHJlIGNsYXNzTmFtZT1cIm10LTIgb3ZlcmZsb3ctYXV0b1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICB7SlNPTi5zdHJpbmdpZnkocmVzdWx0LmRhdGEsIG51bGwsIDIpfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9wcmU+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICl9XG5cbiAgICAgICAgICAgICAgICAgICAge3Jlc3VsdC5yb3dDb3VudCAhPT0gdW5kZWZpbmVkICYmIChcbiAgICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+2LnYr9ivINin2YTYtdmB2YjZgSDYp9mE2YXYqtij2KvYsdipOiB7cmVzdWx0LnJvd0NvdW50fTwvcD5cbiAgICAgICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvQWxlcnREZXNjcmlwdGlvbj5cbiAgICAgICAgICAgICAgPC9BbGVydD5cbiAgICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgICAgPC9DYXJkPlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBEYXRhYmFzZSBJbmZvICovfVxuICAgICAgICA8Q2FyZD5cbiAgICAgICAgICA8Q2FyZEhlYWRlcj5cbiAgICAgICAgICAgIDxDYXJkVGl0bGU+2YXYudmE2YjZhdin2Kog2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqPC9DYXJkVGl0bGU+XG4gICAgICAgICAgPC9DYXJkSGVhZGVyPlxuICAgICAgICAgIDxDYXJkQ29udGVudD5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMiBtZDpncmlkLWNvbHMtNCBnYXAtNCB0ZXh0LXNtXCI+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPtin2YTZhdi22YrZgTwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19EQl9IT1NUIHx8ICdsb2NhbGhvc3QnfTwvcD5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMFwiPtin2YTZhdmG2YHYsDwvcD5cbiAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJmb250LW1lZGl1bVwiPntwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19EQl9QT1JUIHx8ICc1NDMyJ308L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7Zgtin2LnYr9ipINin2YTYqNmK2KfZhtin2Ko8L3A+XG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1tZWRpdW1cIj57cHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfREJfTkFNRSB8fCAnVmVyb19FUlBfQUJBJ308L3A+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDBcIj7Yp9mE2YXYs9iq2K7Yr9mFPC9wPlxuICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e3Byb2Nlc3MuZW52Lk5FWFRfUFVCTElDX0RCX1VTRVIgfHwgJ29wZW5wZyd9PC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgIDwvQ2FyZENvbnRlbnQ+XG4gICAgICAgIDwvQ2FyZD5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApXG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJDYXJkIiwiQ2FyZENvbnRlbnQiLCJDYXJkSGVhZGVyIiwiQ2FyZFRpdGxlIiwiQnV0dG9uIiwiQWxlcnQiLCJBbGVydERlc2NyaXB0aW9uIiwiVGV4dGFyZWEiLCJDaGVja0NpcmNsZSIsIlhDaXJjbGUiLCJEYXRhYmFzZSIsIlBsYXkiLCJDb3B5IiwiRGVidWdEYXRhYmFzZSIsInJlc3VsdCIsInNldFJlc3VsdCIsImxvYWRpbmciLCJzZXRMb2FkaW5nIiwic2V0dXBMb2FkaW5nIiwic2V0U2V0dXBMb2FkaW5nIiwic3FsIiwic2V0U3FsIiwidGVzdFF1ZXJpZXMiLCJuYW1lIiwiZXhlY3V0ZVF1ZXJ5IiwicXVlcnlUb1J1biIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIkpTT04iLCJzdHJpbmdpZnkiLCJkYXRhIiwianNvbiIsImVyciIsInN1Y2Nlc3MiLCJtZXNzYWdlIiwiZXJyb3IiLCJFcnJvciIsInNldHVwU2ltcGxlIiwiY29weVRvQ2xpcGJvYXJkIiwidGV4dCIsIm5hdmlnYXRvciIsImNsaXBib2FyZCIsIndyaXRlVGV4dCIsImRpdiIsImNsYXNzTmFtZSIsImgxIiwicCIsIm1hcCIsInRlc3QiLCJpbmRleCIsInZhcmlhbnQiLCJvbkNsaWNrIiwiZGlzYWJsZWQiLCJzdWJzdHJpbmciLCJ2YWx1ZSIsIm9uQ2hhbmdlIiwiZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwicm93cyIsInN0cm9uZyIsImxlbmd0aCIsInByZSIsInJvd0NvdW50IiwidW5kZWZpbmVkIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0RCX0hPU1QiLCJORVhUX1BVQkxJQ19EQl9QT1JUIiwiTkVYVF9QVUJMSUNfREJfTkFNRSIsIk5FWFRfUFVCTElDX0RCX1VTRVIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./src/pages/debug-database.tsx\n"));

/***/ })

});