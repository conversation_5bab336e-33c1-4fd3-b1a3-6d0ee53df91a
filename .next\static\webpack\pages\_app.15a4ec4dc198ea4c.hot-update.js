"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: function() { return /* binding */ PERMISSIONS; },\n/* harmony export */   PERMISSIONS_ORGANIZED: function() { return /* binding */ PERMISSIONS_ORGANIZED; },\n/* harmony export */   ROLE_PERMISSIONS: function() { return /* binding */ ROLE_PERMISSIONS; },\n/* harmony export */   getCurrentUser: function() { return /* binding */ getCurrentUser; },\n/* harmony export */   hasAllPermissions: function() { return /* binding */ hasAllPermissions; },\n/* harmony export */   hasAnyPermission: function() { return /* binding */ hasAnyPermission; },\n/* harmony export */   hasPermission: function() { return /* binding */ hasPermission; },\n/* harmony export */   signIn: function() { return /* binding */ signIn; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; }\n/* harmony export */ });\n// Mock user type for demo\nconst PERMISSIONS = {\n    // User Management\n    USERS_VIEW: \"users:view\",\n    USERS_CREATE: \"users:create\",\n    USERS_EDIT: \"users:edit\",\n    USERS_DELETE: \"users:delete\",\n    // Branch Management\n    BRANCHES_VIEW: \"branches:view\",\n    BRANCHES_CREATE: \"branches:create\",\n    BRANCHES_EDIT: \"branches:edit\",\n    BRANCHES_DELETE: \"branches:delete\",\n    // Warehouse Management\n    WAREHOUSES_VIEW: \"warehouses:view\",\n    WAREHOUSES_CREATE: \"warehouses:create\",\n    WAREHOUSES_EDIT: \"warehouses:edit\",\n    WAREHOUSES_DELETE: \"warehouses:delete\",\n    // Product Management\n    PRODUCTS_VIEW: \"products:view\",\n    PRODUCTS_CREATE: \"products:create\",\n    PRODUCTS_EDIT: \"products:edit\",\n    PRODUCTS_DELETE: \"products:delete\",\n    // Sales\n    SALES_VIEW: \"sales:view\",\n    SALES_CREATE: \"sales:create\",\n    SALES_EDIT: \"sales:edit\",\n    SALES_DELETE: \"sales:delete\",\n    // Purchases\n    PURCHASES_VIEW: \"purchases:view\",\n    PURCHASES_CREATE: \"purchases:create\",\n    PURCHASES_EDIT: \"purchases:edit\",\n    PURCHASES_DELETE: \"purchases:delete\",\n    PURCHASES_APPROVE: \"purchases:approve\",\n    PURCHASES_RECEIVE: \"purchases:receive\",\n    PURCHASES_PAY: \"purchases:pay\",\n    PURCHASES_CANCEL: \"purchases:cancel\",\n    PURCHASES_PROCESS: \"purchases:process\",\n    // POS\n    POS_ACCESS: \"pos:access\",\n    POS_CLOSE_DAY: \"pos:close_day\",\n    // Cash Registers\n    CASH_REGISTERS_VIEW: \"cash_registers:view\",\n    CASH_REGISTERS_CREATE: \"cash_registers:create\",\n    CASH_REGISTERS_EDIT: \"cash_registers:edit\",\n    CASH_REGISTERS_DELETE: \"cash_registers:delete\",\n    // Accounting\n    ACCOUNTING_VIEW: \"accounting:view\",\n    ACCOUNTING_EDIT: \"accounting:edit\",\n    ACCOUNTING_MANAGE: \"accounting:manage\",\n    ACCOUNTING_CREATE: \"accounting:create\",\n    ACCOUNTING_DELETE: \"accounting:delete\",\n    // Reports\n    REPORTS_VIEW: \"reports:view\",\n    REPORTS_EXPORT: \"reports:export\"\n};\n// Organized permissions for easier use\nconst PERMISSIONS_ORGANIZED = {\n    SALES: {\n        VIEW: PERMISSIONS.SALES_VIEW,\n        CREATE: PERMISSIONS.SALES_CREATE,\n        EDIT: PERMISSIONS.SALES_EDIT,\n        DELETE: PERMISSIONS.SALES_DELETE\n    },\n    PURCHASES: {\n        VIEW: PERMISSIONS.PURCHASES_VIEW,\n        CREATE: PERMISSIONS.PURCHASES_CREATE,\n        EDIT: PERMISSIONS.PURCHASES_EDIT,\n        DELETE: PERMISSIONS.PURCHASES_DELETE,\n        APPROVE: PERMISSIONS.PURCHASES_APPROVE,\n        RECEIVE: PERMISSIONS.PURCHASES_RECEIVE,\n        PAY: PERMISSIONS.PURCHASES_PAY,\n        CANCEL: PERMISSIONS.PURCHASES_CANCEL,\n        PROCESS: PERMISSIONS.PURCHASES_PROCESS\n    }\n};\nconst ROLE_PERMISSIONS = {\n    admin: [\n        // جميع الصلاحيات للمدير\n        ...Object.values(PERMISSIONS)\n    ],\n    manager: [\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    employee: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.REPORTS_VIEW\n    ],\n    cashier: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.CASH_REGISTERS_VIEW\n    ]\n};\nasync function signIn(email, password) {\n    // Mock authentication for demo\n    await new Promise((resolve)=>setTimeout(resolve, 1000));\n    if (email && password) {\n        return {\n            user: {\n                id: \"1\",\n                email\n            }\n        };\n    }\n    throw new Error(\"Invalid credentials\");\n}\nasync function signOut() {\n    // Mock sign out\n    await new Promise((resolve)=>setTimeout(resolve, 500));\n}\nasync function getCurrentUser() {\n    // Mock user for demo - تأكد من أن جميع الصلاحيات متضمنة\n    const adminPermissions = [\n        // User Management\n        \"users:view\",\n        \"users:create\",\n        \"users:edit\",\n        \"users:delete\",\n        // Branch Management\n        \"branches:view\",\n        \"branches:create\",\n        \"branches:edit\",\n        \"branches:delete\",\n        // Warehouse Management\n        \"warehouses:view\",\n        \"warehouses:create\",\n        \"warehouses:edit\",\n        \"warehouses:delete\",\n        // Product Management\n        \"products:view\",\n        \"products:create\",\n        \"products:edit\",\n        \"products:delete\",\n        // Sales\n        \"sales:view\",\n        \"sales:create\",\n        \"sales:edit\",\n        \"sales:delete\",\n        // Purchases\n        \"purchases:view\",\n        \"purchases:create\",\n        \"purchases:edit\",\n        \"purchases:delete\",\n        \"purchases:approve\",\n        \"purchases:receive\",\n        \"purchases:pay\",\n        \"purchases:cancel\",\n        \"purchases:process\",\n        // POS\n        \"pos:access\",\n        \"pos:close_day\",\n        // Cash Registers\n        \"cash_registers:view\",\n        \"cash_registers:create\",\n        \"cash_registers:edit\",\n        \"cash_registers:delete\",\n        // Accounting\n        \"accounting:view\",\n        \"accounting:edit\",\n        \"accounting:manage\",\n        \"accounting:create\",\n        \"accounting:delete\",\n        // Reports\n        \"reports:view\",\n        \"reports:export\"\n    ];\n    return {\n        id: \"1\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام\",\n        role: \"admin\",\n        branch_id: \"1\",\n        warehouse_id: \"1\",\n        pos_id: \"1\",\n        is_active: true,\n        created_at: \"2024-01-01\",\n        updated_at: \"2024-01-01\",\n        permissions: adminPermissions\n    };\n}\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    return user.permissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    return permissions.some((permission)=>user.permissions.includes(permission));\n}\nfunction hasAllPermissions(user, permissions) {\n    if (!user) return false;\n    return permissions.every((permission)=>user.permissions.includes(permission));\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/auth.ts\n"));

/***/ })

});