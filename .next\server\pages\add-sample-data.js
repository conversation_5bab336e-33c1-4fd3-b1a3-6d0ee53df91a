/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/add-sample-data";
exports.ids = ["pages/add-sample-data"];
exports.modules = {

/***/ "__barrel_optimize__?names=BarChart3,CheckCircle,Database,Package,ShoppingCart,Truck,Users,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js":
/*!****************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=BarChart3,CheckCircle,Database,Package,ShoppingCart,Truck,Users,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BarChart3: () => (/* reexport safe */ _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   CheckCircle: () => (/* reexport safe */ _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   Database: () => (/* reexport safe */ _icons_database_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Package: () => (/* reexport safe */ _icons_package_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   ShoppingCart: () => (/* reexport safe */ _icons_shopping_cart_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   Truck: () => (/* reexport safe */ _icons_truck_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Users: () => (/* reexport safe */ _icons_users_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   XCircle: () => (/* reexport safe */ _icons_x_circle_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_bar_chart_3_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/bar-chart-3.js */ \"./node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _icons_check_circle_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/check-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _icons_database_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/database.js */ \"./node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _icons_package_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/package.js */ \"./node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _icons_shopping_cart_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/shopping-cart.js */ \"./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _icons_truck_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/truck.js */ \"./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _icons_users_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/users.js */ \"./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _icons_x_circle_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/x-circle.js */ \"./node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiX19iYXJyZWxfb3B0aW1pemVfXz9uYW1lcz1CYXJDaGFydDMsQ2hlY2tDaXJjbGUsRGF0YWJhc2UsUGFja2FnZSxTaG9wcGluZ0NhcnQsVHJ1Y2ssVXNlcnMsWENpcmNsZSE9IS4vbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9sdWNpZGUtcmVhY3QuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDNkQ7QUFDRztBQUNQO0FBQ0Y7QUFDVztBQUNmO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9idXNpbmVzcy1tYW5hZ2VtZW50LXN5c3RlbS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vbHVjaWRlLXJlYWN0LmpzPzM1MTciXSwic291cmNlc0NvbnRlbnQiOlsiXG5leHBvcnQgeyBkZWZhdWx0IGFzIEJhckNoYXJ0MyB9IGZyb20gXCIuL2ljb25zL2Jhci1jaGFydC0zLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgQ2hlY2tDaXJjbGUgfSBmcm9tIFwiLi9pY29ucy9jaGVjay1jaXJjbGUuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBEYXRhYmFzZSB9IGZyb20gXCIuL2ljb25zL2RhdGFiYXNlLmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgUGFja2FnZSB9IGZyb20gXCIuL2ljb25zL3BhY2thZ2UuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBTaG9wcGluZ0NhcnQgfSBmcm9tIFwiLi9pY29ucy9zaG9wcGluZy1jYXJ0LmpzXCJcbmV4cG9ydCB7IGRlZmF1bHQgYXMgVHJ1Y2sgfSBmcm9tIFwiLi9pY29ucy90cnVjay5qc1wiXG5leHBvcnQgeyBkZWZhdWx0IGFzIFVzZXJzIH0gZnJvbSBcIi4vaWNvbnMvdXNlcnMuanNcIlxuZXhwb3J0IHsgZGVmYXVsdCBhcyBYQ2lyY2xlIH0gZnJvbSBcIi4vaWNvbnMveC1jaXJjbGUuanNcIiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///__barrel_optimize__?names=BarChart3,CheckCircle,Database,Package,ShoppingCart,Truck,Users,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadd-sample-data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cadd-sample-data.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadd-sample-data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cadd-sample-data.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./src/pages/_app.tsx\");\n/* harmony import */ var _src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src\\pages\\add-sample-data.tsx */ \"./src/pages/add-sample-data.tsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/add-sample-data\",\n        pathname: \"/add-sample-data\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _src_pages_add_sample_data_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadd-sample-data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cadd-sample-data.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./src/components/ui/alert.tsx":
/*!*************************************!*\
  !*** ./src/components/ui/alert.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Alert: () => (/* binding */ Alert),\n/* harmony export */   AlertDescription: () => (/* binding */ AlertDescription),\n/* harmony export */   AlertTitle: () => (/* binding */ AlertTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__]);\n([class_variance_authority__WEBPACK_IMPORTED_MODULE_2__, _lib_utils__WEBPACK_IMPORTED_MODULE_3__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\nconst alertVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\", {\n    variants: {\n        variant: {\n            default: \"bg-background text-foreground\",\n            destructive: \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\"\n    }\n});\nconst Alert = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        role: \"alert\",\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(alertVariants({\n            variant\n        }), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 25,\n        columnNumber: 3\n    }, undefined));\nAlert.displayName = \"Alert\";\nconst AlertTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h5\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"mb-1 font-medium leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 38,\n        columnNumber: 3\n    }, undefined));\nAlertTitle.displayName = \"AlertTitle\";\nconst AlertDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"text-sm [&_p]:leading-relaxed\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\alert.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nAlertDescription.displayName = \"AlertDescription\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/alert.tsx\n");

/***/ }),

/***/ "./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-slot */ \"@radix-ui/react-slot\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! class-variance-authority */ \"class-variance-authority\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__]);\n([_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__, class_variance_authority__WEBPACK_IMPORTED_MODULE_3__, _lib_utils__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_3__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_2__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_4__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 45,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/button.tsx\n");

/***/ }),

/***/ "./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"./src/lib/utils.ts\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_lib_utils__WEBPACK_IMPORTED_MODULE_2__]);\n_lib_utils__WEBPACK_IMPORTED_MODULE_2__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 8,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 23,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 35,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 50,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 62,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 70,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/components/ui/card.tsx\n");

/***/ }),

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// دالة للحصول على صلاحيات الدور\nconst getRolePermissions = (role)=>{\n    return _lib_auth__WEBPACK_IMPORTED_MODULE_2__.ROLE_PERMISSIONS[role] || [];\n};\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تحميل بيانات المستخدم من localStorage\n    const loadUserFromStorage = ()=>{\n        try {\n            const savedUser = localStorage.getItem(\"user\");\n            if (savedUser) {\n                return JSON.parse(savedUser);\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error loading user from storage:\", error);\n            return null;\n        }\n    };\n    const refreshUser = async ()=>{\n        const savedUser = loadUserFromStorage();\n        if (savedUser && !savedUser.permissions) {\n            // إضافة الصلاحيات إذا لم تكن موجودة\n            const permissions = getRolePermissions(savedUser.role);\n            const userWithPermissions = {\n                ...savedUser,\n                permissions\n            };\n            localStorage.setItem(\"user\", JSON.stringify(userWithPermissions));\n            setUser(userWithPermissions);\n        } else {\n            setUser(savedUser);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/postgres/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                const userData = result.user;\n                // إضافة الصلاحيات بناءً على الدور\n                const permissions = getRolePermissions(userData.role);\n                const userWithPermissions = {\n                    ...userData,\n                    permissions\n                };\n                localStorage.setItem(\"user\", JSON.stringify(userWithPermissions));\n                setUser(userWithPermissions);\n            } else {\n                throw new Error(result.message || \"فشل في تسجيل الدخول\");\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            localStorage.removeItem(\"user\");\n            setUser(null);\n        } catch (error) {\n            console.error(\"Sign out error:\", error);\n            throw error;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل المستخدم من localStorage عند بدء التطبيق\n        const initializeAuth = async ()=>{\n            try {\n                await refreshUser();\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 147,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvaG9va3MvdXNlQXV0aC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFFNkU7QUFDaEM7QUF1QjdDLE1BQU1NLDRCQUFjSCxvREFBYUEsQ0FBOEJJO0FBRXhELFNBQVNDO0lBQ2QsTUFBTUMsVUFBVUwsaURBQVVBLENBQUNFO0lBQzNCLElBQUlHLFlBQVlGLFdBQVc7UUFDekIsTUFBTSxJQUFJRyxNQUFNO0lBQ2xCO0lBQ0EsT0FBT0Q7QUFDVDtBQUVBLGdDQUFnQztBQUNoQyxNQUFNRSxxQkFBcUIsQ0FBQ0M7SUFDMUIsT0FBT1AsdURBQWdCLENBQUNPLEtBQXNDLElBQUksRUFBRTtBQUN0RTtBQUVPLFNBQVNDLGFBQWEsRUFBRUMsUUFBUSxFQUFpQztJQUN0RSxNQUFNLENBQUNDLE1BQU1DLFFBQVEsR0FBR2YsK0NBQVFBLENBQWtCO0lBQ2xELE1BQU0sQ0FBQ2dCLFNBQVNDLFdBQVcsR0FBR2pCLCtDQUFRQSxDQUFDO0lBRXZDLHdDQUF3QztJQUN4QyxNQUFNa0Isc0JBQXNCO1FBQzFCLElBQUk7WUFDRixNQUFNQyxZQUFZQyxhQUFhQyxPQUFPLENBQUM7WUFDdkMsSUFBSUYsV0FBVztnQkFDYixPQUFPRyxLQUFLQyxLQUFLLENBQUNKO1lBQ3BCO1lBQ0EsT0FBTztRQUNULEVBQUUsT0FBT0ssT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsb0NBQW9DQTtZQUNsRCxPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1FLGNBQWM7UUFDbEIsTUFBTVAsWUFBWUQ7UUFDbEIsSUFBSUMsYUFBYSxDQUFDQSxVQUFVUSxXQUFXLEVBQUU7WUFDdkMsb0NBQW9DO1lBQ3BDLE1BQU1BLGNBQWNqQixtQkFBbUJTLFVBQVVSLElBQUk7WUFDckQsTUFBTWlCLHNCQUFzQjtnQkFDMUIsR0FBR1QsU0FBUztnQkFDWlE7WUFDRjtZQUNBUCxhQUFhUyxPQUFPLENBQUMsUUFBUVAsS0FBS1EsU0FBUyxDQUFDRjtZQUM1Q2IsUUFBUWE7UUFDVixPQUFPO1lBQ0xiLFFBQVFJO1FBQ1Y7SUFDRjtJQUVBLE1BQU1ZLFNBQVMsT0FBT0MsT0FBZUM7UUFDbkNoQixXQUFXO1FBQ1gsSUFBSTtZQUNGLE1BQU1pQixXQUFXLE1BQU1DLE1BQU0sdUJBQXVCO2dCQUNsREMsUUFBUTtnQkFDUkMsU0FBUztvQkFDUCxnQkFBZ0I7Z0JBQ2xCO2dCQUNBQyxNQUFNaEIsS0FBS1EsU0FBUyxDQUFDO29CQUFFRTtvQkFBT0M7Z0JBQVM7WUFDekM7WUFFQSxNQUFNTSxTQUFTLE1BQU1MLFNBQVNNLElBQUk7WUFFbEMsSUFBSUQsT0FBT0UsT0FBTyxFQUFFO2dCQUNsQixNQUFNQyxXQUFXSCxPQUFPekIsSUFBSTtnQkFFNUIsa0NBQWtDO2dCQUNsQyxNQUFNYSxjQUFjakIsbUJBQW1CZ0MsU0FBUy9CLElBQUk7Z0JBQ3BELE1BQU1pQixzQkFBc0I7b0JBQzFCLEdBQUdjLFFBQVE7b0JBQ1hmO2dCQUNGO2dCQUVBUCxhQUFhUyxPQUFPLENBQUMsUUFBUVAsS0FBS1EsU0FBUyxDQUFDRjtnQkFDNUNiLFFBQVFhO1lBQ1YsT0FBTztnQkFDTCxNQUFNLElBQUluQixNQUFNOEIsT0FBT0ksT0FBTyxJQUFJO1lBQ3BDO1FBQ0YsRUFBRSxPQUFPbkIsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsa0JBQWtCQTtZQUNoQyxNQUFNQTtRQUNSLFNBQVU7WUFDUlAsV0FBVztRQUNiO0lBQ0Y7SUFFQSxNQUFNMkIsVUFBVTtRQUNkLElBQUk7WUFDRnhCLGFBQWF5QixVQUFVLENBQUM7WUFDeEI5QixRQUFRO1FBQ1YsRUFBRSxPQUFPUyxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyxtQkFBbUJBO1lBQ2pDLE1BQU1BO1FBQ1I7SUFDRjtJQUVBdkIsZ0RBQVNBLENBQUM7UUFDUixpREFBaUQ7UUFDakQsTUFBTTZDLGlCQUFpQjtZQUNyQixJQUFJO2dCQUNGLE1BQU1wQjtZQUNSLEVBQUUsT0FBT0YsT0FBTztnQkFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7Z0JBQzFDVCxRQUFRO1lBQ1YsU0FBVTtnQkFDUkUsV0FBVztZQUNiO1FBQ0Y7UUFFQTZCO0lBQ0YsR0FBRyxFQUFFO0lBRUwsTUFBTUMsUUFBUTtRQUNaakM7UUFDQUU7UUFDQWU7UUFDQWE7UUFDQWxCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ3JCLFlBQVkyQyxRQUFRO1FBQUNELE9BQU9BO2tCQUMxQmxDOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL2hvb2tzL3VzZUF1dGgudHN4PzVmYWYiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCBSZWFjdCwgeyB1c2VTdGF0ZSwgdXNlRWZmZWN0LCBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0IH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyBST0xFX1BFUk1JU1NJT05TIH0gZnJvbSAnQC9saWIvYXV0aCdcblxuaW50ZXJmYWNlIEF1dGhVc2VyIHtcbiAgaWQ6IG51bWJlclxuICBlbWFpbDogc3RyaW5nXG4gIHVzZXJuYW1lOiBzdHJpbmdcbiAgZnVsbF9uYW1lOiBzdHJpbmdcbiAgcm9sZTogJ2FkbWluJyB8ICdtYW5hZ2VyJyB8ICdlbXBsb3llZScgfCAnY2FzaGllcidcbiAgaXNfYWN0aXZlOiBib29sZWFuXG4gIGJyYW5jaF9pZD86IG51bWJlclxuICB3YXJlaG91c2VfaWQ/OiBudW1iZXJcbiAgcG9zX2lkPzogbnVtYmVyXG4gIHBlcm1pc3Npb25zOiBzdHJpbmdbXVxufVxuXG5pbnRlcmZhY2UgQXV0aENvbnRleHRUeXBlIHtcbiAgdXNlcjogQXV0aFVzZXIgfCBudWxsXG4gIGxvYWRpbmc6IGJvb2xlYW5cbiAgc2lnbkluOiAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4gUHJvbWlzZTx2b2lkPlxuICBzaWduT3V0OiAoKSA9PiBQcm9taXNlPHZvaWQ+XG4gIHJlZnJlc2hVc2VyOiAoKSA9PiBQcm9taXNlPHZvaWQ+XG59XG5cbmNvbnN0IEF1dGhDb250ZXh0ID0gY3JlYXRlQ29udGV4dDxBdXRoQ29udGV4dFR5cGUgfCB1bmRlZmluZWQ+KHVuZGVmaW5lZClcblxuZXhwb3J0IGZ1bmN0aW9uIHVzZUF1dGgoKSB7XG4gIGNvbnN0IGNvbnRleHQgPSB1c2VDb250ZXh0KEF1dGhDb250ZXh0KVxuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJylcbiAgfVxuICByZXR1cm4gY29udGV4dFxufVxuXG4vLyDYr9in2YTYqSDZhNmE2K3YtdmI2YQg2LnZhNmJINi12YTYp9it2YrYp9iqINin2YTYr9mI2LFcbmNvbnN0IGdldFJvbGVQZXJtaXNzaW9ucyA9IChyb2xlOiBzdHJpbmcpOiBzdHJpbmdbXSA9PiB7XG4gIHJldHVybiBST0xFX1BFUk1JU1NJT05TW3JvbGUgYXMga2V5b2YgdHlwZW9mIFJPTEVfUEVSTUlTU0lPTlNdIHx8IFtdXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiB7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfSkge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxBdXRoVXNlciB8IG51bGw+KG51bGwpXG4gIGNvbnN0IFtsb2FkaW5nLCBzZXRMb2FkaW5nXSA9IHVzZVN0YXRlKHRydWUpXG5cbiAgLy8g2KrYrdmF2YrZhCDYqNmK2KfZhtin2Kog2KfZhNmF2LPYqtiu2K/ZhSDZhdmGIGxvY2FsU3RvcmFnZVxuICBjb25zdCBsb2FkVXNlckZyb21TdG9yYWdlID0gKCk6IEF1dGhVc2VyIHwgbnVsbCA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHNhdmVkVXNlciA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd1c2VyJylcbiAgICAgIGlmIChzYXZlZFVzZXIpIHtcbiAgICAgICAgcmV0dXJuIEpTT04ucGFyc2Uoc2F2ZWRVc2VyKVxuICAgICAgfVxuICAgICAgcmV0dXJuIG51bGxcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgbG9hZGluZyB1c2VyIGZyb20gc3RvcmFnZTonLCBlcnJvcilcbiAgICAgIHJldHVybiBudWxsXG4gICAgfVxuICB9XG5cbiAgY29uc3QgcmVmcmVzaFVzZXIgPSBhc3luYyAoKSA9PiB7XG4gICAgY29uc3Qgc2F2ZWRVc2VyID0gbG9hZFVzZXJGcm9tU3RvcmFnZSgpXG4gICAgaWYgKHNhdmVkVXNlciAmJiAhc2F2ZWRVc2VyLnBlcm1pc3Npb25zKSB7XG4gICAgICAvLyDYpdi22KfZgdipINin2YTYtdmE2KfYrdmK2KfYqiDYpdiw2Kcg2YTZhSDYqtmD2YYg2YXZiNis2YjYr9ipXG4gICAgICBjb25zdCBwZXJtaXNzaW9ucyA9IGdldFJvbGVQZXJtaXNzaW9ucyhzYXZlZFVzZXIucm9sZSlcbiAgICAgIGNvbnN0IHVzZXJXaXRoUGVybWlzc2lvbnMgPSB7XG4gICAgICAgIC4uLnNhdmVkVXNlcixcbiAgICAgICAgcGVybWlzc2lvbnNcbiAgICAgIH1cbiAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCd1c2VyJywgSlNPTi5zdHJpbmdpZnkodXNlcldpdGhQZXJtaXNzaW9ucykpXG4gICAgICBzZXRVc2VyKHVzZXJXaXRoUGVybWlzc2lvbnMpXG4gICAgfSBlbHNlIHtcbiAgICAgIHNldFVzZXIoc2F2ZWRVc2VyKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHNpZ25JbiA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiB7XG4gICAgc2V0TG9hZGluZyh0cnVlKVxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL3Bvc3RncmVzL2xvZ2luJywge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbidcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBlbWFpbCwgcGFzc3dvcmQgfSlcbiAgICAgIH0pXG5cbiAgICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuXG4gICAgICBpZiAocmVzdWx0LnN1Y2Nlc3MpIHtcbiAgICAgICAgY29uc3QgdXNlckRhdGEgPSByZXN1bHQudXNlclxuXG4gICAgICAgIC8vINil2LbYp9mB2Kkg2KfZhNi12YTYp9it2YrYp9iqINio2YbYp9ih2Ysg2LnZhNmJINin2YTYr9mI2LFcbiAgICAgICAgY29uc3QgcGVybWlzc2lvbnMgPSBnZXRSb2xlUGVybWlzc2lvbnModXNlckRhdGEucm9sZSlcbiAgICAgICAgY29uc3QgdXNlcldpdGhQZXJtaXNzaW9ucyA9IHtcbiAgICAgICAgICAuLi51c2VyRGF0YSxcbiAgICAgICAgICBwZXJtaXNzaW9uc1xuICAgICAgICB9XG5cbiAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oJ3VzZXInLCBKU09OLnN0cmluZ2lmeSh1c2VyV2l0aFBlcm1pc3Npb25zKSlcbiAgICAgICAgc2V0VXNlcih1c2VyV2l0aFBlcm1pc3Npb25zKVxuICAgICAgfSBlbHNlIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKHJlc3VsdC5tZXNzYWdlIHx8ICfZgdi02YQg2YHZiiDYqtiz2KzZitmEINin2YTYr9iu2YjZhCcpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1NpZ24gaW4gZXJyb3I6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHNpZ25PdXQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGxvY2FsU3RvcmFnZS5yZW1vdmVJdGVtKCd1c2VyJylcbiAgICAgIHNldFVzZXIobnVsbClcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignU2lnbiBvdXQgZXJyb3I6JywgZXJyb3IpXG4gICAgICB0aHJvdyBlcnJvclxuICAgIH1cbiAgfVxuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgLy8g2KrYrdmF2YrZhCDYp9mE2YXYs9iq2K7Yr9mFINmF2YYgbG9jYWxTdG9yYWdlINi52YbYryDYqNiv2KEg2KfZhNiq2LfYqNmK2YJcbiAgICBjb25zdCBpbml0aWFsaXplQXV0aCA9IGFzeW5jICgpID0+IHtcbiAgICAgIHRyeSB7XG4gICAgICAgIGF3YWl0IHJlZnJlc2hVc2VyKClcbiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGluaXRpYWxpemluZyBhdXRoOicsIGVycm9yKVxuICAgICAgICBzZXRVc2VyKG51bGwpXG4gICAgICB9IGZpbmFsbHkge1xuICAgICAgICBzZXRMb2FkaW5nKGZhbHNlKVxuICAgICAgfVxuICAgIH1cblxuICAgIGluaXRpYWxpemVBdXRoKClcbiAgfSwgW10pXG5cbiAgY29uc3QgdmFsdWUgPSB7XG4gICAgdXNlcixcbiAgICBsb2FkaW5nLFxuICAgIHNpZ25JbixcbiAgICBzaWduT3V0LFxuICAgIHJlZnJlc2hVc2VyLFxuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8QXV0aENvbnRleHQuUHJvdmlkZXIgdmFsdWU9e3ZhbHVlfT5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L0F1dGhDb250ZXh0LlByb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJ1c2VTdGF0ZSIsInVzZUVmZmVjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwiUk9MRV9QRVJNSVNTSU9OUyIsIkF1dGhDb250ZXh0IiwidW5kZWZpbmVkIiwidXNlQXV0aCIsImNvbnRleHQiLCJFcnJvciIsImdldFJvbGVQZXJtaXNzaW9ucyIsInJvbGUiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzZXRVc2VyIiwibG9hZGluZyIsInNldExvYWRpbmciLCJsb2FkVXNlckZyb21TdG9yYWdlIiwic2F2ZWRVc2VyIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsInJlZnJlc2hVc2VyIiwicGVybWlzc2lvbnMiLCJ1c2VyV2l0aFBlcm1pc3Npb25zIiwic2V0SXRlbSIsInN0cmluZ2lmeSIsInNpZ25JbiIsImVtYWlsIiwicGFzc3dvcmQiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJyZXN1bHQiLCJqc29uIiwic3VjY2VzcyIsInVzZXJEYXRhIiwibWVzc2FnZSIsInNpZ25PdXQiLCJyZW1vdmVJdGVtIiwiaW5pdGlhbGl6ZUF1dGgiLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n");

/***/ }),

/***/ "./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   PERMISSIONS_ORGANIZED: () => (/* binding */ PERMISSIONS_ORGANIZED),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n// Mock user type for demo\nconst PERMISSIONS = {\n    // User Management\n    USERS_VIEW: \"users:view\",\n    USERS_CREATE: \"users:create\",\n    USERS_EDIT: \"users:edit\",\n    USERS_DELETE: \"users:delete\",\n    // Branch Management\n    BRANCHES_VIEW: \"branches:view\",\n    BRANCHES_CREATE: \"branches:create\",\n    BRANCHES_EDIT: \"branches:edit\",\n    BRANCHES_DELETE: \"branches:delete\",\n    // Warehouse Management\n    WAREHOUSES_VIEW: \"warehouses:view\",\n    WAREHOUSES_CREATE: \"warehouses:create\",\n    WAREHOUSES_EDIT: \"warehouses:edit\",\n    WAREHOUSES_DELETE: \"warehouses:delete\",\n    // Product Management\n    PRODUCTS_VIEW: \"products:view\",\n    PRODUCTS_CREATE: \"products:create\",\n    PRODUCTS_EDIT: \"products:edit\",\n    PRODUCTS_DELETE: \"products:delete\",\n    // Sales\n    SALES_VIEW: \"sales:view\",\n    SALES_CREATE: \"sales:create\",\n    SALES_EDIT: \"sales:edit\",\n    SALES_DELETE: \"sales:delete\",\n    // Purchases\n    PURCHASES_VIEW: \"purchases:view\",\n    PURCHASES_CREATE: \"purchases:create\",\n    PURCHASES_EDIT: \"purchases:edit\",\n    PURCHASES_DELETE: \"purchases:delete\",\n    PURCHASES_APPROVE: \"purchases:approve\",\n    PURCHASES_RECEIVE: \"purchases:receive\",\n    PURCHASES_PAY: \"purchases:pay\",\n    PURCHASES_CANCEL: \"purchases:cancel\",\n    PURCHASES_PROCESS: \"purchases:process\",\n    // POS\n    POS_ACCESS: \"pos:access\",\n    POS_CLOSE_DAY: \"pos:close_day\",\n    // Cash Registers\n    CASH_REGISTERS_VIEW: \"cash_registers:view\",\n    CASH_REGISTERS_CREATE: \"cash_registers:create\",\n    CASH_REGISTERS_EDIT: \"cash_registers:edit\",\n    CASH_REGISTERS_DELETE: \"cash_registers:delete\",\n    // Accounting\n    ACCOUNTING_VIEW: \"accounting:view\",\n    ACCOUNTING_EDIT: \"accounting:edit\",\n    ACCOUNTING_MANAGE: \"accounting:manage\",\n    ACCOUNTING_CREATE: \"accounting:create\",\n    ACCOUNTING_DELETE: \"accounting:delete\",\n    // Reports\n    REPORTS_VIEW: \"reports:view\",\n    REPORTS_EXPORT: \"reports:export\"\n};\n// Organized permissions for easier use\nconst PERMISSIONS_ORGANIZED = {\n    SALES: {\n        VIEW: PERMISSIONS.SALES_VIEW,\n        CREATE: PERMISSIONS.SALES_CREATE,\n        EDIT: PERMISSIONS.SALES_EDIT,\n        DELETE: PERMISSIONS.SALES_DELETE\n    },\n    PURCHASES: {\n        VIEW: PERMISSIONS.PURCHASES_VIEW,\n        CREATE: PERMISSIONS.PURCHASES_CREATE,\n        EDIT: PERMISSIONS.PURCHASES_EDIT,\n        DELETE: PERMISSIONS.PURCHASES_DELETE,\n        APPROVE: PERMISSIONS.PURCHASES_APPROVE,\n        RECEIVE: PERMISSIONS.PURCHASES_RECEIVE,\n        PAY: PERMISSIONS.PURCHASES_PAY,\n        CANCEL: PERMISSIONS.PURCHASES_CANCEL,\n        PROCESS: PERMISSIONS.PURCHASES_PROCESS\n    }\n};\nconst ROLE_PERMISSIONS = {\n    admin: [\n        // User Management\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.USERS_CREATE,\n        PERMISSIONS.USERS_EDIT,\n        PERMISSIONS.USERS_DELETE,\n        // Branch Management\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.BRANCHES_DELETE,\n        // Warehouse Management\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.WAREHOUSES_DELETE,\n        // Product Management\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.PRODUCTS_DELETE,\n        // Sales\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.SALES_DELETE,\n        // Purchases\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_DELETE,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        // POS\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.POS_CLOSE_DAY,\n        // Cash Registers\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.CASH_REGISTERS_DELETE,\n        // Accounting\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        // Reports\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    manager: [\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    employee: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.REPORTS_VIEW\n    ],\n    cashier: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.CASH_REGISTERS_VIEW\n    ]\n};\nasync function signIn(email, password) {\n    // Mock authentication for demo\n    await new Promise((resolve)=>setTimeout(resolve, 1000));\n    if (email && password) {\n        return {\n            user: {\n                id: \"1\",\n                email\n            }\n        };\n    }\n    throw new Error(\"Invalid credentials\");\n}\nasync function signOut() {\n    // Mock sign out\n    await new Promise((resolve)=>setTimeout(resolve, 500));\n}\nasync function getCurrentUser() {\n    // Mock user for demo - تأكد من أن جميع الصلاحيات متضمنة\n    const adminPermissions = [\n        // User Management\n        \"users:view\",\n        \"users:create\",\n        \"users:edit\",\n        \"users:delete\",\n        // Branch Management\n        \"branches:view\",\n        \"branches:create\",\n        \"branches:edit\",\n        \"branches:delete\",\n        // Warehouse Management\n        \"warehouses:view\",\n        \"warehouses:create\",\n        \"warehouses:edit\",\n        \"warehouses:delete\",\n        // Product Management\n        \"products:view\",\n        \"products:create\",\n        \"products:edit\",\n        \"products:delete\",\n        // Sales\n        \"sales:view\",\n        \"sales:create\",\n        \"sales:edit\",\n        \"sales:delete\",\n        // Purchases\n        \"purchases:view\",\n        \"purchases:create\",\n        \"purchases:edit\",\n        \"purchases:delete\",\n        \"purchases:approve\",\n        \"purchases:receive\",\n        \"purchases:pay\",\n        \"purchases:cancel\",\n        \"purchases:process\",\n        // POS\n        \"pos:access\",\n        \"pos:close_day\",\n        // Cash Registers\n        \"cash_registers:view\",\n        \"cash_registers:create\",\n        \"cash_registers:edit\",\n        \"cash_registers:delete\",\n        // Accounting\n        \"accounting:view\",\n        \"accounting:edit\",\n        \"accounting:manage\",\n        \"accounting:create\",\n        \"accounting:delete\",\n        // Reports\n        \"reports:view\",\n        \"reports:export\"\n    ];\n    return {\n        id: \"1\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام\",\n        role: \"admin\",\n        branch_id: \"1\",\n        warehouse_id: \"1\",\n        pos_id: \"1\",\n        is_active: true,\n        created_at: \"2024-01-01\",\n        updated_at: \"2024-01-01\",\n        permissions: adminPermissions\n    };\n}\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    return user.permissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    return permissions.some((permission)=>user.permissions.includes(permission));\n}\nfunction hasAllPermissions(user, permissions) {\n    if (!user) return false;\n    return permissions.every((permission)=>user.permissions.includes(permission));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvbGliL2F1dGgudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsMEJBQTBCO0FBZ0JuQixNQUFNQSxjQUFjO0lBQ3pCLGtCQUFrQjtJQUNsQkMsWUFBWTtJQUNaQyxjQUFjO0lBQ2RDLFlBQVk7SUFDWkMsY0FBYztJQUVkLG9CQUFvQjtJQUNwQkMsZUFBZTtJQUNmQyxpQkFBaUI7SUFDakJDLGVBQWU7SUFDZkMsaUJBQWlCO0lBRWpCLHVCQUF1QjtJQUN2QkMsaUJBQWlCO0lBQ2pCQyxtQkFBbUI7SUFDbkJDLGlCQUFpQjtJQUNqQkMsbUJBQW1CO0lBRW5CLHFCQUFxQjtJQUNyQkMsZUFBZTtJQUNmQyxpQkFBaUI7SUFDakJDLGVBQWU7SUFDZkMsaUJBQWlCO0lBRWpCLFFBQVE7SUFDUkMsWUFBWTtJQUNaQyxjQUFjO0lBQ2RDLFlBQVk7SUFDWkMsY0FBYztJQUVkLFlBQVk7SUFDWkMsZ0JBQWdCO0lBQ2hCQyxrQkFBa0I7SUFDbEJDLGdCQUFnQjtJQUNoQkMsa0JBQWtCO0lBQ2xCQyxtQkFBbUI7SUFDbkJDLG1CQUFtQjtJQUNuQkMsZUFBZTtJQUNmQyxrQkFBa0I7SUFDbEJDLG1CQUFtQjtJQUVuQixNQUFNO0lBQ05DLFlBQVk7SUFDWkMsZUFBZTtJQUVmLGlCQUFpQjtJQUNqQkMscUJBQXFCO0lBQ3JCQyx1QkFBdUI7SUFDdkJDLHFCQUFxQjtJQUNyQkMsdUJBQXVCO0lBRXZCLGFBQWE7SUFDYkMsaUJBQWlCO0lBQ2pCQyxpQkFBaUI7SUFDakJDLG1CQUFtQjtJQUNuQkMsbUJBQW1CO0lBQ25CQyxtQkFBbUI7SUFFbkIsVUFBVTtJQUNWQyxjQUFjO0lBQ2RDLGdCQUFnQjtBQUNsQixFQUFVO0FBRVYsdUNBQXVDO0FBQ2hDLE1BQU1DLHdCQUF3QjtJQUNuQ0MsT0FBTztRQUNMQyxNQUFNN0MsWUFBWWlCLFVBQVU7UUFDNUI2QixRQUFROUMsWUFBWWtCLFlBQVk7UUFDaEM2QixNQUFNL0MsWUFBWW1CLFVBQVU7UUFDNUI2QixRQUFRaEQsWUFBWW9CLFlBQVk7SUFDbEM7SUFDQTZCLFdBQVc7UUFDVEosTUFBTTdDLFlBQVlxQixjQUFjO1FBQ2hDeUIsUUFBUTlDLFlBQVlzQixnQkFBZ0I7UUFDcEN5QixNQUFNL0MsWUFBWXVCLGNBQWM7UUFDaEN5QixRQUFRaEQsWUFBWXdCLGdCQUFnQjtRQUNwQzBCLFNBQVNsRCxZQUFZeUIsaUJBQWlCO1FBQ3RDMEIsU0FBU25ELFlBQVkwQixpQkFBaUI7UUFDdEMwQixLQUFLcEQsWUFBWTJCLGFBQWE7UUFDOUIwQixRQUFRckQsWUFBWTRCLGdCQUFnQjtRQUNwQzBCLFNBQVN0RCxZQUFZNkIsaUJBQWlCO0lBQ3hDO0FBQ0YsRUFBQztBQUVNLE1BQU0wQixtQkFBbUI7SUFDOUJDLE9BQU87UUFDTCxrQkFBa0I7UUFDbEJ4RCxZQUFZQyxVQUFVO1FBQ3RCRCxZQUFZRSxZQUFZO1FBQ3hCRixZQUFZRyxVQUFVO1FBQ3RCSCxZQUFZSSxZQUFZO1FBQ3hCLG9CQUFvQjtRQUNwQkosWUFBWUssYUFBYTtRQUN6QkwsWUFBWU0sZUFBZTtRQUMzQk4sWUFBWU8sYUFBYTtRQUN6QlAsWUFBWVEsZUFBZTtRQUMzQix1QkFBdUI7UUFDdkJSLFlBQVlTLGVBQWU7UUFDM0JULFlBQVlVLGlCQUFpQjtRQUM3QlYsWUFBWVcsZUFBZTtRQUMzQlgsWUFBWVksaUJBQWlCO1FBQzdCLHFCQUFxQjtRQUNyQlosWUFBWWEsYUFBYTtRQUN6QmIsWUFBWWMsZUFBZTtRQUMzQmQsWUFBWWUsYUFBYTtRQUN6QmYsWUFBWWdCLGVBQWU7UUFDM0IsUUFBUTtRQUNSaEIsWUFBWWlCLFVBQVU7UUFDdEJqQixZQUFZa0IsWUFBWTtRQUN4QmxCLFlBQVltQixVQUFVO1FBQ3RCbkIsWUFBWW9CLFlBQVk7UUFDeEIsWUFBWTtRQUNacEIsWUFBWXFCLGNBQWM7UUFDMUJyQixZQUFZc0IsZ0JBQWdCO1FBQzVCdEIsWUFBWXVCLGNBQWM7UUFDMUJ2QixZQUFZd0IsZ0JBQWdCO1FBQzVCeEIsWUFBWXlCLGlCQUFpQjtRQUM3QnpCLFlBQVkwQixpQkFBaUI7UUFDN0IxQixZQUFZMkIsYUFBYTtRQUN6QjNCLFlBQVk0QixnQkFBZ0I7UUFDNUI1QixZQUFZNkIsaUJBQWlCO1FBQzdCLE1BQU07UUFDTjdCLFlBQVk4QixVQUFVO1FBQ3RCOUIsWUFBWStCLGFBQWE7UUFDekIsaUJBQWlCO1FBQ2pCL0IsWUFBWWdDLG1CQUFtQjtRQUMvQmhDLFlBQVlpQyxxQkFBcUI7UUFDakNqQyxZQUFZa0MsbUJBQW1CO1FBQy9CbEMsWUFBWW1DLHFCQUFxQjtRQUNqQyxhQUFhO1FBQ2JuQyxZQUFZb0MsZUFBZTtRQUMzQnBDLFlBQVlxQyxlQUFlO1FBQzNCckMsWUFBWXNDLGlCQUFpQjtRQUM3QnRDLFlBQVl1QyxpQkFBaUI7UUFDN0J2QyxZQUFZd0MsaUJBQWlCO1FBQzdCLFVBQVU7UUFDVnhDLFlBQVl5QyxZQUFZO1FBQ3hCekMsWUFBWTBDLGNBQWM7S0FDM0I7SUFDRGUsU0FBUztRQUNQekQsWUFBWUMsVUFBVTtRQUN0QkQsWUFBWUssYUFBYTtRQUN6QkwsWUFBWU0sZUFBZTtRQUMzQk4sWUFBWU8sYUFBYTtRQUN6QlAsWUFBWVMsZUFBZTtRQUMzQlQsWUFBWVUsaUJBQWlCO1FBQzdCVixZQUFZVyxlQUFlO1FBQzNCWCxZQUFZZ0MsbUJBQW1CO1FBQy9CaEMsWUFBWWlDLHFCQUFxQjtRQUNqQ2pDLFlBQVlrQyxtQkFBbUI7UUFDL0JsQyxZQUFZYSxhQUFhO1FBQ3pCYixZQUFZYyxlQUFlO1FBQzNCZCxZQUFZZSxhQUFhO1FBQ3pCZixZQUFZaUIsVUFBVTtRQUN0QmpCLFlBQVlrQixZQUFZO1FBQ3hCbEIsWUFBWW1CLFVBQVU7UUFDdEJuQixZQUFZcUIsY0FBYztRQUMxQnJCLFlBQVlzQixnQkFBZ0I7UUFDNUJ0QixZQUFZdUIsY0FBYztRQUMxQnZCLFlBQVl5QixpQkFBaUI7UUFDN0J6QixZQUFZMEIsaUJBQWlCO1FBQzdCMUIsWUFBWTJCLGFBQWE7UUFDekIzQixZQUFZNEIsZ0JBQWdCO1FBQzVCNUIsWUFBWTZCLGlCQUFpQjtRQUM3QjdCLFlBQVlvQyxlQUFlO1FBQzNCcEMsWUFBWXFDLGVBQWU7UUFDM0JyQyxZQUFZc0MsaUJBQWlCO1FBQzdCdEMsWUFBWXVDLGlCQUFpQjtRQUM3QnZDLFlBQVl3QyxpQkFBaUI7UUFDN0J4QyxZQUFZeUMsWUFBWTtRQUN4QnpDLFlBQVkwQyxjQUFjO0tBQzNCO0lBQ0RnQixVQUFVO1FBQ1IxRCxZQUFZYSxhQUFhO1FBQ3pCYixZQUFZaUIsVUFBVTtRQUN0QmpCLFlBQVlrQixZQUFZO1FBQ3hCbEIsWUFBWXFCLGNBQWM7UUFDMUJyQixZQUFZc0IsZ0JBQWdCO1FBQzVCdEIsWUFBWXlDLFlBQVk7S0FDekI7SUFDRGtCLFNBQVM7UUFDUDNELFlBQVlhLGFBQWE7UUFDekJiLFlBQVlpQixVQUFVO1FBQ3RCakIsWUFBWWtCLFlBQVk7UUFDeEJsQixZQUFZOEIsVUFBVTtRQUN0QjlCLFlBQVlnQyxtQkFBbUI7S0FDaEM7QUFDSCxFQUFDO0FBRU0sZUFBZTRCLE9BQU9DLEtBQWEsRUFBRUMsUUFBZ0I7SUFDMUQsK0JBQStCO0lBQy9CLE1BQU0sSUFBSUMsUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztJQUVqRCxJQUFJSCxTQUFTQyxVQUFVO1FBQ3JCLE9BQU87WUFBRUksTUFBTTtnQkFBRUMsSUFBSTtnQkFBS047WUFBTTtRQUFFO0lBQ3BDO0lBRUEsTUFBTSxJQUFJTyxNQUFNO0FBQ2xCO0FBRU8sZUFBZUM7SUFDcEIsZ0JBQWdCO0lBQ2hCLE1BQU0sSUFBSU4sUUFBUUMsQ0FBQUEsVUFBV0MsV0FBV0QsU0FBUztBQUNuRDtBQUVPLGVBQWVNO0lBQ3BCLHdEQUF3RDtJQUN4RCxNQUFNQyxtQkFBbUI7UUFDdkIsa0JBQWtCO1FBQ2xCO1FBQWM7UUFBZ0I7UUFBYztRQUM1QyxvQkFBb0I7UUFDcEI7UUFBaUI7UUFBbUI7UUFBaUI7UUFDckQsdUJBQXVCO1FBQ3ZCO1FBQW1CO1FBQXFCO1FBQW1CO1FBQzNELHFCQUFxQjtRQUNyQjtRQUFpQjtRQUFtQjtRQUFpQjtRQUNyRCxRQUFRO1FBQ1I7UUFBYztRQUFnQjtRQUFjO1FBQzVDLFlBQVk7UUFDWjtRQUFrQjtRQUFvQjtRQUFrQjtRQUN4RDtRQUFxQjtRQUFxQjtRQUFpQjtRQUFvQjtRQUMvRSxNQUFNO1FBQ047UUFBYztRQUNkLGlCQUFpQjtRQUNqQjtRQUF1QjtRQUF5QjtRQUF1QjtRQUN2RSxhQUFhO1FBQ2I7UUFBbUI7UUFBbUI7UUFBcUI7UUFBcUI7UUFDaEYsVUFBVTtRQUNWO1FBQWdCO0tBQ2pCO0lBRUQsT0FBTztRQUNMSixJQUFJO1FBQ0pOLE9BQU87UUFDUFcsVUFBVTtRQUNWQyxXQUFXO1FBQ1hDLE1BQU07UUFDTkMsV0FBVztRQUNYQyxjQUFjO1FBQ2RDLFFBQVE7UUFDUkMsV0FBVztRQUNYQyxZQUFZO1FBQ1pDLFlBQVk7UUFDWkMsYUFBYVY7SUFDZjtBQUNGO0FBRU8sU0FBU1csY0FBY2hCLElBQXFCLEVBQUVpQixVQUFrQjtJQUNyRSxJQUFJLENBQUNqQixNQUFNLE9BQU87SUFDbEIsT0FBT0EsS0FBS2UsV0FBVyxDQUFDRyxRQUFRLENBQUNEO0FBQ25DO0FBRU8sU0FBU0UsaUJBQWlCbkIsSUFBcUIsRUFBRWUsV0FBcUI7SUFDM0UsSUFBSSxDQUFDZixNQUFNLE9BQU87SUFDbEIsT0FBT2UsWUFBWUssSUFBSSxDQUFDSCxDQUFBQSxhQUFjakIsS0FBS2UsV0FBVyxDQUFDRyxRQUFRLENBQUNEO0FBQ2xFO0FBRU8sU0FBU0ksa0JBQWtCckIsSUFBcUIsRUFBRWUsV0FBcUI7SUFDNUUsSUFBSSxDQUFDZixNQUFNLE9BQU87SUFDbEIsT0FBT2UsWUFBWU8sS0FBSyxDQUFDTCxDQUFBQSxhQUFjakIsS0FBS2UsV0FBVyxDQUFDRyxRQUFRLENBQUNEO0FBQ25FIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vLi9zcmMvbGliL2F1dGgudHM/NjY5MiJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBNb2NrIHVzZXIgdHlwZSBmb3IgZGVtb1xuZXhwb3J0IGludGVyZmFjZSBBdXRoVXNlciB7XG4gIGlkOiBzdHJpbmdcbiAgZW1haWw6IHN0cmluZ1xuICB1c2VybmFtZTogc3RyaW5nXG4gIGZ1bGxfbmFtZTogc3RyaW5nXG4gIHJvbGU6ICdhZG1pbicgfCAnbWFuYWdlcicgfCAnZW1wbG95ZWUnIHwgJ2Nhc2hpZXInXG4gIGJyYW5jaF9pZDogc3RyaW5nIHwgbnVsbFxuICB3YXJlaG91c2VfaWQ6IHN0cmluZyB8IG51bGxcbiAgcG9zX2lkOiBzdHJpbmcgfCBudWxsXG4gIGlzX2FjdGl2ZTogYm9vbGVhblxuICBjcmVhdGVkX2F0OiBzdHJpbmdcbiAgdXBkYXRlZF9hdDogc3RyaW5nXG4gIHBlcm1pc3Npb25zOiBzdHJpbmdbXVxufVxuXG5leHBvcnQgY29uc3QgUEVSTUlTU0lPTlMgPSB7XG4gIC8vIFVzZXIgTWFuYWdlbWVudFxuICBVU0VSU19WSUVXOiAndXNlcnM6dmlldycsXG4gIFVTRVJTX0NSRUFURTogJ3VzZXJzOmNyZWF0ZScsXG4gIFVTRVJTX0VESVQ6ICd1c2VyczplZGl0JyxcbiAgVVNFUlNfREVMRVRFOiAndXNlcnM6ZGVsZXRlJyxcblxuICAvLyBCcmFuY2ggTWFuYWdlbWVudFxuICBCUkFOQ0hFU19WSUVXOiAnYnJhbmNoZXM6dmlldycsXG4gIEJSQU5DSEVTX0NSRUFURTogJ2JyYW5jaGVzOmNyZWF0ZScsXG4gIEJSQU5DSEVTX0VESVQ6ICdicmFuY2hlczplZGl0JyxcbiAgQlJBTkNIRVNfREVMRVRFOiAnYnJhbmNoZXM6ZGVsZXRlJyxcblxuICAvLyBXYXJlaG91c2UgTWFuYWdlbWVudFxuICBXQVJFSE9VU0VTX1ZJRVc6ICd3YXJlaG91c2VzOnZpZXcnLFxuICBXQVJFSE9VU0VTX0NSRUFURTogJ3dhcmVob3VzZXM6Y3JlYXRlJyxcbiAgV0FSRUhPVVNFU19FRElUOiAnd2FyZWhvdXNlczplZGl0JyxcbiAgV0FSRUhPVVNFU19ERUxFVEU6ICd3YXJlaG91c2VzOmRlbGV0ZScsXG5cbiAgLy8gUHJvZHVjdCBNYW5hZ2VtZW50XG4gIFBST0RVQ1RTX1ZJRVc6ICdwcm9kdWN0czp2aWV3JyxcbiAgUFJPRFVDVFNfQ1JFQVRFOiAncHJvZHVjdHM6Y3JlYXRlJyxcbiAgUFJPRFVDVFNfRURJVDogJ3Byb2R1Y3RzOmVkaXQnLFxuICBQUk9EVUNUU19ERUxFVEU6ICdwcm9kdWN0czpkZWxldGUnLFxuXG4gIC8vIFNhbGVzXG4gIFNBTEVTX1ZJRVc6ICdzYWxlczp2aWV3JyxcbiAgU0FMRVNfQ1JFQVRFOiAnc2FsZXM6Y3JlYXRlJyxcbiAgU0FMRVNfRURJVDogJ3NhbGVzOmVkaXQnLFxuICBTQUxFU19ERUxFVEU6ICdzYWxlczpkZWxldGUnLFxuXG4gIC8vIFB1cmNoYXNlc1xuICBQVVJDSEFTRVNfVklFVzogJ3B1cmNoYXNlczp2aWV3JyxcbiAgUFVSQ0hBU0VTX0NSRUFURTogJ3B1cmNoYXNlczpjcmVhdGUnLFxuICBQVVJDSEFTRVNfRURJVDogJ3B1cmNoYXNlczplZGl0JyxcbiAgUFVSQ0hBU0VTX0RFTEVURTogJ3B1cmNoYXNlczpkZWxldGUnLFxuICBQVVJDSEFTRVNfQVBQUk9WRTogJ3B1cmNoYXNlczphcHByb3ZlJyxcbiAgUFVSQ0hBU0VTX1JFQ0VJVkU6ICdwdXJjaGFzZXM6cmVjZWl2ZScsXG4gIFBVUkNIQVNFU19QQVk6ICdwdXJjaGFzZXM6cGF5JyxcbiAgUFVSQ0hBU0VTX0NBTkNFTDogJ3B1cmNoYXNlczpjYW5jZWwnLFxuICBQVVJDSEFTRVNfUFJPQ0VTUzogJ3B1cmNoYXNlczpwcm9jZXNzJyxcblxuICAvLyBQT1NcbiAgUE9TX0FDQ0VTUzogJ3BvczphY2Nlc3MnLFxuICBQT1NfQ0xPU0VfREFZOiAncG9zOmNsb3NlX2RheScsXG5cbiAgLy8gQ2FzaCBSZWdpc3RlcnNcbiAgQ0FTSF9SRUdJU1RFUlNfVklFVzogJ2Nhc2hfcmVnaXN0ZXJzOnZpZXcnLFxuICBDQVNIX1JFR0lTVEVSU19DUkVBVEU6ICdjYXNoX3JlZ2lzdGVyczpjcmVhdGUnLFxuICBDQVNIX1JFR0lTVEVSU19FRElUOiAnY2FzaF9yZWdpc3RlcnM6ZWRpdCcsXG4gIENBU0hfUkVHSVNURVJTX0RFTEVURTogJ2Nhc2hfcmVnaXN0ZXJzOmRlbGV0ZScsXG5cbiAgLy8gQWNjb3VudGluZ1xuICBBQ0NPVU5USU5HX1ZJRVc6ICdhY2NvdW50aW5nOnZpZXcnLFxuICBBQ0NPVU5USU5HX0VESVQ6ICdhY2NvdW50aW5nOmVkaXQnLFxuICBBQ0NPVU5USU5HX01BTkFHRTogJ2FjY291bnRpbmc6bWFuYWdlJyxcbiAgQUNDT1VOVElOR19DUkVBVEU6ICdhY2NvdW50aW5nOmNyZWF0ZScsXG4gIEFDQ09VTlRJTkdfREVMRVRFOiAnYWNjb3VudGluZzpkZWxldGUnLFxuXG4gIC8vIFJlcG9ydHNcbiAgUkVQT1JUU19WSUVXOiAncmVwb3J0czp2aWV3JyxcbiAgUkVQT1JUU19FWFBPUlQ6ICdyZXBvcnRzOmV4cG9ydCcsXG59IGFzIGNvbnN0XG5cbi8vIE9yZ2FuaXplZCBwZXJtaXNzaW9ucyBmb3IgZWFzaWVyIHVzZVxuZXhwb3J0IGNvbnN0IFBFUk1JU1NJT05TX09SR0FOSVpFRCA9IHtcbiAgU0FMRVM6IHtcbiAgICBWSUVXOiBQRVJNSVNTSU9OUy5TQUxFU19WSUVXLFxuICAgIENSRUFURTogUEVSTUlTU0lPTlMuU0FMRVNfQ1JFQVRFLFxuICAgIEVESVQ6IFBFUk1JU1NJT05TLlNBTEVTX0VESVQsXG4gICAgREVMRVRFOiBQRVJNSVNTSU9OUy5TQUxFU19ERUxFVEUsXG4gIH0sXG4gIFBVUkNIQVNFUzoge1xuICAgIFZJRVc6IFBFUk1JU1NJT05TLlBVUkNIQVNFU19WSUVXLFxuICAgIENSRUFURTogUEVSTUlTU0lPTlMuUFVSQ0hBU0VTX0NSRUFURSxcbiAgICBFRElUOiBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfRURJVCxcbiAgICBERUxFVEU6IFBFUk1JU1NJT05TLlBVUkNIQVNFU19ERUxFVEUsXG4gICAgQVBQUk9WRTogUEVSTUlTU0lPTlMuUFVSQ0hBU0VTX0FQUFJPVkUsXG4gICAgUkVDRUlWRTogUEVSTUlTU0lPTlMuUFVSQ0hBU0VTX1JFQ0VJVkUsXG4gICAgUEFZOiBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfUEFZLFxuICAgIENBTkNFTDogUEVSTUlTU0lPTlMuUFVSQ0hBU0VTX0NBTkNFTCxcbiAgICBQUk9DRVNTOiBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfUFJPQ0VTUyxcbiAgfSxcbn1cblxuZXhwb3J0IGNvbnN0IFJPTEVfUEVSTUlTU0lPTlMgPSB7XG4gIGFkbWluOiBbXG4gICAgLy8gVXNlciBNYW5hZ2VtZW50XG4gICAgUEVSTUlTU0lPTlMuVVNFUlNfVklFVyxcbiAgICBQRVJNSVNTSU9OUy5VU0VSU19DUkVBVEUsXG4gICAgUEVSTUlTU0lPTlMuVVNFUlNfRURJVCxcbiAgICBQRVJNSVNTSU9OUy5VU0VSU19ERUxFVEUsXG4gICAgLy8gQnJhbmNoIE1hbmFnZW1lbnRcbiAgICBQRVJNSVNTSU9OUy5CUkFOQ0hFU19WSUVXLFxuICAgIFBFUk1JU1NJT05TLkJSQU5DSEVTX0NSRUFURSxcbiAgICBQRVJNSVNTSU9OUy5CUkFOQ0hFU19FRElULFxuICAgIFBFUk1JU1NJT05TLkJSQU5DSEVTX0RFTEVURSxcbiAgICAvLyBXYXJlaG91c2UgTWFuYWdlbWVudFxuICAgIFBFUk1JU1NJT05TLldBUkVIT1VTRVNfVklFVyxcbiAgICBQRVJNSVNTSU9OUy5XQVJFSE9VU0VTX0NSRUFURSxcbiAgICBQRVJNSVNTSU9OUy5XQVJFSE9VU0VTX0VESVQsXG4gICAgUEVSTUlTU0lPTlMuV0FSRUhPVVNFU19ERUxFVEUsXG4gICAgLy8gUHJvZHVjdCBNYW5hZ2VtZW50XG4gICAgUEVSTUlTU0lPTlMuUFJPRFVDVFNfVklFVyxcbiAgICBQRVJNSVNTSU9OUy5QUk9EVUNUU19DUkVBVEUsXG4gICAgUEVSTUlTU0lPTlMuUFJPRFVDVFNfRURJVCxcbiAgICBQRVJNSVNTSU9OUy5QUk9EVUNUU19ERUxFVEUsXG4gICAgLy8gU2FsZXNcbiAgICBQRVJNSVNTSU9OUy5TQUxFU19WSUVXLFxuICAgIFBFUk1JU1NJT05TLlNBTEVTX0NSRUFURSxcbiAgICBQRVJNSVNTSU9OUy5TQUxFU19FRElULFxuICAgIFBFUk1JU1NJT05TLlNBTEVTX0RFTEVURSxcbiAgICAvLyBQdXJjaGFzZXNcbiAgICBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfVklFVyxcbiAgICBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19FRElULFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19ERUxFVEUsXG4gICAgUEVSTUlTU0lPTlMuUFVSQ0hBU0VTX0FQUFJPVkUsXG4gICAgUEVSTUlTU0lPTlMuUFVSQ0hBU0VTX1JFQ0VJVkUsXG4gICAgUEVSTUlTU0lPTlMuUFVSQ0hBU0VTX1BBWSxcbiAgICBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfQ0FOQ0VMLFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19QUk9DRVNTLFxuICAgIC8vIFBPU1xuICAgIFBFUk1JU1NJT05TLlBPU19BQ0NFU1MsXG4gICAgUEVSTUlTU0lPTlMuUE9TX0NMT1NFX0RBWSxcbiAgICAvLyBDYXNoIFJlZ2lzdGVyc1xuICAgIFBFUk1JU1NJT05TLkNBU0hfUkVHSVNURVJTX1ZJRVcsXG4gICAgUEVSTUlTU0lPTlMuQ0FTSF9SRUdJU1RFUlNfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLkNBU0hfUkVHSVNURVJTX0VESVQsXG4gICAgUEVSTUlTU0lPTlMuQ0FTSF9SRUdJU1RFUlNfREVMRVRFLFxuICAgIC8vIEFjY291bnRpbmdcbiAgICBQRVJNSVNTSU9OUy5BQ0NPVU5USU5HX1ZJRVcsXG4gICAgUEVSTUlTU0lPTlMuQUNDT1VOVElOR19FRElULFxuICAgIFBFUk1JU1NJT05TLkFDQ09VTlRJTkdfTUFOQUdFLFxuICAgIFBFUk1JU1NJT05TLkFDQ09VTlRJTkdfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLkFDQ09VTlRJTkdfREVMRVRFLFxuICAgIC8vIFJlcG9ydHNcbiAgICBQRVJNSVNTSU9OUy5SRVBPUlRTX1ZJRVcsXG4gICAgUEVSTUlTU0lPTlMuUkVQT1JUU19FWFBPUlQsXG4gIF0sXG4gIG1hbmFnZXI6IFtcbiAgICBQRVJNSVNTSU9OUy5VU0VSU19WSUVXLFxuICAgIFBFUk1JU1NJT05TLkJSQU5DSEVTX1ZJRVcsXG4gICAgUEVSTUlTU0lPTlMuQlJBTkNIRVNfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLkJSQU5DSEVTX0VESVQsXG4gICAgUEVSTUlTU0lPTlMuV0FSRUhPVVNFU19WSUVXLFxuICAgIFBFUk1JU1NJT05TLldBUkVIT1VTRVNfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLldBUkVIT1VTRVNfRURJVCxcbiAgICBQRVJNSVNTSU9OUy5DQVNIX1JFR0lTVEVSU19WSUVXLFxuICAgIFBFUk1JU1NJT05TLkNBU0hfUkVHSVNURVJTX0NSRUFURSxcbiAgICBQRVJNSVNTSU9OUy5DQVNIX1JFR0lTVEVSU19FRElULFxuICAgIFBFUk1JU1NJT05TLlBST0RVQ1RTX1ZJRVcsXG4gICAgUEVSTUlTU0lPTlMuUFJPRFVDVFNfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLlBST0RVQ1RTX0VESVQsXG4gICAgUEVSTUlTU0lPTlMuU0FMRVNfVklFVyxcbiAgICBQRVJNSVNTSU9OUy5TQUxFU19DUkVBVEUsXG4gICAgUEVSTUlTU0lPTlMuU0FMRVNfRURJVCxcbiAgICBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfVklFVyxcbiAgICBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19FRElULFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19BUFBST1ZFLFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19SRUNFSVZFLFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19QQVksXG4gICAgUEVSTUlTU0lPTlMuUFVSQ0hBU0VTX0NBTkNFTCxcbiAgICBQRVJNSVNTSU9OUy5QVVJDSEFTRVNfUFJPQ0VTUyxcbiAgICBQRVJNSVNTSU9OUy5BQ0NPVU5USU5HX1ZJRVcsXG4gICAgUEVSTUlTU0lPTlMuQUNDT1VOVElOR19FRElULFxuICAgIFBFUk1JU1NJT05TLkFDQ09VTlRJTkdfTUFOQUdFLFxuICAgIFBFUk1JU1NJT05TLkFDQ09VTlRJTkdfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLkFDQ09VTlRJTkdfREVMRVRFLFxuICAgIFBFUk1JU1NJT05TLlJFUE9SVFNfVklFVyxcbiAgICBQRVJNSVNTSU9OUy5SRVBPUlRTX0VYUE9SVCxcbiAgXSxcbiAgZW1wbG95ZWU6IFtcbiAgICBQRVJNSVNTSU9OUy5QUk9EVUNUU19WSUVXLFxuICAgIFBFUk1JU1NJT05TLlNBTEVTX1ZJRVcsXG4gICAgUEVSTUlTU0lPTlMuU0FMRVNfQ1JFQVRFLFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19WSUVXLFxuICAgIFBFUk1JU1NJT05TLlBVUkNIQVNFU19DUkVBVEUsXG4gICAgUEVSTUlTU0lPTlMuUkVQT1JUU19WSUVXLFxuICBdLFxuICBjYXNoaWVyOiBbXG4gICAgUEVSTUlTU0lPTlMuUFJPRFVDVFNfVklFVyxcbiAgICBQRVJNSVNTSU9OUy5TQUxFU19WSUVXLFxuICAgIFBFUk1JU1NJT05TLlNBTEVTX0NSRUFURSxcbiAgICBQRVJNSVNTSU9OUy5QT1NfQUNDRVNTLFxuICAgIFBFUk1JU1NJT05TLkNBU0hfUkVHSVNURVJTX1ZJRVcsXG4gIF0sXG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBzaWduSW4oZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykge1xuICAvLyBNb2NrIGF1dGhlbnRpY2F0aW9uIGZvciBkZW1vXG4gIGF3YWl0IG5ldyBQcm9taXNlKHJlc29sdmUgPT4gc2V0VGltZW91dChyZXNvbHZlLCAxMDAwKSlcblxuICBpZiAoZW1haWwgJiYgcGFzc3dvcmQpIHtcbiAgICByZXR1cm4geyB1c2VyOiB7IGlkOiAnMScsIGVtYWlsIH0gfVxuICB9XG5cbiAgdGhyb3cgbmV3IEVycm9yKCdJbnZhbGlkIGNyZWRlbnRpYWxzJylcbn1cblxuZXhwb3J0IGFzeW5jIGZ1bmN0aW9uIHNpZ25PdXQoKSB7XG4gIC8vIE1vY2sgc2lnbiBvdXRcbiAgYXdhaXQgbmV3IFByb21pc2UocmVzb2x2ZSA9PiBzZXRUaW1lb3V0KHJlc29sdmUsIDUwMCkpXG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBnZXRDdXJyZW50VXNlcigpOiBQcm9taXNlPEF1dGhVc2VyIHwgbnVsbD4ge1xuICAvLyBNb2NrIHVzZXIgZm9yIGRlbW8gLSDYqtij2YPYryDZhdmGINij2YYg2KzZhdmK2Lkg2KfZhNi12YTYp9it2YrYp9iqINmF2KrYttmF2YbYqVxuICBjb25zdCBhZG1pblBlcm1pc3Npb25zID0gW1xuICAgIC8vIFVzZXIgTWFuYWdlbWVudFxuICAgICd1c2Vyczp2aWV3JywgJ3VzZXJzOmNyZWF0ZScsICd1c2VyczplZGl0JywgJ3VzZXJzOmRlbGV0ZScsXG4gICAgLy8gQnJhbmNoIE1hbmFnZW1lbnRcbiAgICAnYnJhbmNoZXM6dmlldycsICdicmFuY2hlczpjcmVhdGUnLCAnYnJhbmNoZXM6ZWRpdCcsICdicmFuY2hlczpkZWxldGUnLFxuICAgIC8vIFdhcmVob3VzZSBNYW5hZ2VtZW50XG4gICAgJ3dhcmVob3VzZXM6dmlldycsICd3YXJlaG91c2VzOmNyZWF0ZScsICd3YXJlaG91c2VzOmVkaXQnLCAnd2FyZWhvdXNlczpkZWxldGUnLFxuICAgIC8vIFByb2R1Y3QgTWFuYWdlbWVudFxuICAgICdwcm9kdWN0czp2aWV3JywgJ3Byb2R1Y3RzOmNyZWF0ZScsICdwcm9kdWN0czplZGl0JywgJ3Byb2R1Y3RzOmRlbGV0ZScsXG4gICAgLy8gU2FsZXNcbiAgICAnc2FsZXM6dmlldycsICdzYWxlczpjcmVhdGUnLCAnc2FsZXM6ZWRpdCcsICdzYWxlczpkZWxldGUnLFxuICAgIC8vIFB1cmNoYXNlc1xuICAgICdwdXJjaGFzZXM6dmlldycsICdwdXJjaGFzZXM6Y3JlYXRlJywgJ3B1cmNoYXNlczplZGl0JywgJ3B1cmNoYXNlczpkZWxldGUnLFxuICAgICdwdXJjaGFzZXM6YXBwcm92ZScsICdwdXJjaGFzZXM6cmVjZWl2ZScsICdwdXJjaGFzZXM6cGF5JywgJ3B1cmNoYXNlczpjYW5jZWwnLCAncHVyY2hhc2VzOnByb2Nlc3MnLFxuICAgIC8vIFBPU1xuICAgICdwb3M6YWNjZXNzJywgJ3BvczpjbG9zZV9kYXknLFxuICAgIC8vIENhc2ggUmVnaXN0ZXJzXG4gICAgJ2Nhc2hfcmVnaXN0ZXJzOnZpZXcnLCAnY2FzaF9yZWdpc3RlcnM6Y3JlYXRlJywgJ2Nhc2hfcmVnaXN0ZXJzOmVkaXQnLCAnY2FzaF9yZWdpc3RlcnM6ZGVsZXRlJyxcbiAgICAvLyBBY2NvdW50aW5nXG4gICAgJ2FjY291bnRpbmc6dmlldycsICdhY2NvdW50aW5nOmVkaXQnLCAnYWNjb3VudGluZzptYW5hZ2UnLCAnYWNjb3VudGluZzpjcmVhdGUnLCAnYWNjb3VudGluZzpkZWxldGUnLFxuICAgIC8vIFJlcG9ydHNcbiAgICAncmVwb3J0czp2aWV3JywgJ3JlcG9ydHM6ZXhwb3J0J1xuICBdXG5cbiAgcmV0dXJuIHtcbiAgICBpZDogJzEnLFxuICAgIGVtYWlsOiAnYWRtaW5AZXhhbXBsZS5jb20nLFxuICAgIHVzZXJuYW1lOiAnYWRtaW4nLFxuICAgIGZ1bGxfbmFtZTogJ9mF2K/ZitixINin2YTZhti42KfZhScsXG4gICAgcm9sZTogJ2FkbWluJyxcbiAgICBicmFuY2hfaWQ6ICcxJyxcbiAgICB3YXJlaG91c2VfaWQ6ICcxJyxcbiAgICBwb3NfaWQ6ICcxJyxcbiAgICBpc19hY3RpdmU6IHRydWUsXG4gICAgY3JlYXRlZF9hdDogJzIwMjQtMDEtMDEnLFxuICAgIHVwZGF0ZWRfYXQ6ICcyMDI0LTAxLTAxJyxcbiAgICBwZXJtaXNzaW9uczogYWRtaW5QZXJtaXNzaW9uc1xuICB9XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBoYXNQZXJtaXNzaW9uKHVzZXI6IEF1dGhVc2VyIHwgbnVsbCwgcGVybWlzc2lvbjogc3RyaW5nKTogYm9vbGVhbiB7XG4gIGlmICghdXNlcikgcmV0dXJuIGZhbHNlXG4gIHJldHVybiB1c2VyLnBlcm1pc3Npb25zLmluY2x1ZGVzKHBlcm1pc3Npb24pXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBoYXNBbnlQZXJtaXNzaW9uKHVzZXI6IEF1dGhVc2VyIHwgbnVsbCwgcGVybWlzc2lvbnM6IHN0cmluZ1tdKTogYm9vbGVhbiB7XG4gIGlmICghdXNlcikgcmV0dXJuIGZhbHNlXG4gIHJldHVybiBwZXJtaXNzaW9ucy5zb21lKHBlcm1pc3Npb24gPT4gdXNlci5wZXJtaXNzaW9ucy5pbmNsdWRlcyhwZXJtaXNzaW9uKSlcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIGhhc0FsbFBlcm1pc3Npb25zKHVzZXI6IEF1dGhVc2VyIHwgbnVsbCwgcGVybWlzc2lvbnM6IHN0cmluZ1tdKTogYm9vbGVhbiB7XG4gIGlmICghdXNlcikgcmV0dXJuIGZhbHNlXG4gIHJldHVybiBwZXJtaXNzaW9ucy5ldmVyeShwZXJtaXNzaW9uID0+IHVzZXIucGVybWlzc2lvbnMuaW5jbHVkZXMocGVybWlzc2lvbikpXG59XG4iXSwibmFtZXMiOlsiUEVSTUlTU0lPTlMiLCJVU0VSU19WSUVXIiwiVVNFUlNfQ1JFQVRFIiwiVVNFUlNfRURJVCIsIlVTRVJTX0RFTEVURSIsIkJSQU5DSEVTX1ZJRVciLCJCUkFOQ0hFU19DUkVBVEUiLCJCUkFOQ0hFU19FRElUIiwiQlJBTkNIRVNfREVMRVRFIiwiV0FSRUhPVVNFU19WSUVXIiwiV0FSRUhPVVNFU19DUkVBVEUiLCJXQVJFSE9VU0VTX0VESVQiLCJXQVJFSE9VU0VTX0RFTEVURSIsIlBST0RVQ1RTX1ZJRVciLCJQUk9EVUNUU19DUkVBVEUiLCJQUk9EVUNUU19FRElUIiwiUFJPRFVDVFNfREVMRVRFIiwiU0FMRVNfVklFVyIsIlNBTEVTX0NSRUFURSIsIlNBTEVTX0VESVQiLCJTQUxFU19ERUxFVEUiLCJQVVJDSEFTRVNfVklFVyIsIlBVUkNIQVNFU19DUkVBVEUiLCJQVVJDSEFTRVNfRURJVCIsIlBVUkNIQVNFU19ERUxFVEUiLCJQVVJDSEFTRVNfQVBQUk9WRSIsIlBVUkNIQVNFU19SRUNFSVZFIiwiUFVSQ0hBU0VTX1BBWSIsIlBVUkNIQVNFU19DQU5DRUwiLCJQVVJDSEFTRVNfUFJPQ0VTUyIsIlBPU19BQ0NFU1MiLCJQT1NfQ0xPU0VfREFZIiwiQ0FTSF9SRUdJU1RFUlNfVklFVyIsIkNBU0hfUkVHSVNURVJTX0NSRUFURSIsIkNBU0hfUkVHSVNURVJTX0VESVQiLCJDQVNIX1JFR0lTVEVSU19ERUxFVEUiLCJBQ0NPVU5USU5HX1ZJRVciLCJBQ0NPVU5USU5HX0VESVQiLCJBQ0NPVU5USU5HX01BTkFHRSIsIkFDQ09VTlRJTkdfQ1JFQVRFIiwiQUNDT1VOVElOR19ERUxFVEUiLCJSRVBPUlRTX1ZJRVciLCJSRVBPUlRTX0VYUE9SVCIsIlBFUk1JU1NJT05TX09SR0FOSVpFRCIsIlNBTEVTIiwiVklFVyIsIkNSRUFURSIsIkVESVQiLCJERUxFVEUiLCJQVVJDSEFTRVMiLCJBUFBST1ZFIiwiUkVDRUlWRSIsIlBBWSIsIkNBTkNFTCIsIlBST0NFU1MiLCJST0xFX1BFUk1JU1NJT05TIiwiYWRtaW4iLCJtYW5hZ2VyIiwiZW1wbG95ZWUiLCJjYXNoaWVyIiwic2lnbkluIiwiZW1haWwiLCJwYXNzd29yZCIsIlByb21pc2UiLCJyZXNvbHZlIiwic2V0VGltZW91dCIsInVzZXIiLCJpZCIsIkVycm9yIiwic2lnbk91dCIsImdldEN1cnJlbnRVc2VyIiwiYWRtaW5QZXJtaXNzaW9ucyIsInVzZXJuYW1lIiwiZnVsbF9uYW1lIiwicm9sZSIsImJyYW5jaF9pZCIsIndhcmVob3VzZV9pZCIsInBvc19pZCIsImlzX2FjdGl2ZSIsImNyZWF0ZWRfYXQiLCJ1cGRhdGVkX2F0IiwicGVybWlzc2lvbnMiLCJoYXNQZXJtaXNzaW9uIiwicGVybWlzc2lvbiIsImluY2x1ZGVzIiwiaGFzQW55UGVybWlzc2lvbiIsInNvbWUiLCJoYXNBbGxQZXJtaXNzaW9ucyIsImV2ZXJ5Il0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./src/lib/auth.ts\n");

/***/ }),

/***/ "./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateTax: () => (/* binding */ calculateTax),\n/* harmony export */   calculateTotal: () => (/* binding */ calculateTotal),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   generateInvoiceNumber: () => (/* binding */ generateInvoiceNumber),\n/* harmony export */   generateSKU: () => (/* binding */ generateSKU),\n/* harmony export */   getInitials: () => (/* binding */ getInitials),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   isValidPhone: () => (/* binding */ isValidPhone),\n/* harmony export */   slugify: () => (/* binding */ slugify),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"clsx\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"tailwind-merge\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__]);\n([clsx__WEBPACK_IMPORTED_MODULE_0__, tailwind_merge__WEBPACK_IMPORTED_MODULE_1__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\nfunction formatCurrency(amount, currency = \"EGP\") {\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency: currency\n    }).format(amount);\n}\nfunction formatDate(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\"\n    }).format(new Date(date));\n}\nfunction formatDateTime(date) {\n    return new Intl.DateTimeFormat(\"en-US\", {\n        year: \"numeric\",\n        month: \"short\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    }).format(new Date(date));\n}\nfunction generateSKU(prefix = \"PRD\") {\n    const timestamp = Date.now().toString(36);\n    const random = Math.random().toString(36).substr(2, 5);\n    return `${prefix}-${timestamp}-${random}`.toUpperCase();\n}\nfunction calculateTax(amount, taxRate = 0.14) {\n    return amount * taxRate;\n}\nfunction calculateTotal(subtotal, taxRate = 0.14) {\n    return subtotal + calculateTax(subtotal, taxRate);\n}\nfunction debounce(func, wait) {\n    let timeout;\n    return (...args)=>{\n        clearTimeout(timeout);\n        timeout = setTimeout(()=>func(...args), wait);\n    };\n}\nfunction slugify(text) {\n    return text.toString().toLowerCase().replace(/\\s+/g, \"-\").replace(/[^\\w\\-]+/g, \"\").replace(/\\-\\-+/g, \"-\").replace(/^-+/, \"\").replace(/-+$/, \"\");\n}\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.substr(0, maxLength) + \"...\";\n}\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\nfunction isValidPhone(phone) {\n    const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/;\n    return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\nfunction getInitials(name) {\n    return name.split(\" \").map((word)=>word.charAt(0)).join(\"\").toUpperCase().slice(0, 2);\n}\nfunction generateInvoiceNumber(prefix = \"INV\") {\n    const date = new Date();\n    const year = date.getFullYear().toString().slice(-2);\n    const month = (date.getMonth() + 1).toString().padStart(2, \"0\");\n    const day = date.getDate().toString().padStart(2, \"0\");\n    const random = Math.floor(Math.random() * 1000).toString().padStart(3, \"0\");\n    return `${prefix}-${year}${month}${day}-${random}`;\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/lib/utils.ts\n");

/***/ }),

/***/ "./src/pages/_app.tsx":
/*!****************************!*\
  !*** ./src/pages/_app.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useAuth */ \"./src/hooks/useAuth.tsx\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/styles/globals.css */ \"./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_hooks_useAuth__WEBPACK_IMPORTED_MODULE_1__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n                ...pageProps\n            }, void 0, false, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n                lineNumber: 9,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n            lineNumber: 8,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\_app.tsx\",\n        lineNumber: 7,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvcGFnZXMvX2FwcC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUM4QztBQUNqQjtBQUVkLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQVk7SUFDNUQscUJBQ0UsOERBQUNILHdEQUFZQTtrQkFDWCw0RUFBQ0k7WUFBSUMsV0FBVTtzQkFDYiw0RUFBQ0g7Z0JBQVcsR0FBR0MsU0FBUzs7Ozs7Ozs7Ozs7Ozs7OztBQUloQyIsInNvdXJjZXMiOlsid2VicGFjazovL2J1c2luZXNzLW1hbmFnZW1lbnQtc3lzdGVtLy4vc3JjL3BhZ2VzL19hcHAudHN4P2Y5ZDYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSAnQC9ob29rcy91c2VBdXRoJ1xuaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcydcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfTogQXBwUHJvcHMpIHtcbiAgcmV0dXJuIChcbiAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxuICAgICAgICA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG4gICAgICA8L2Rpdj5cbiAgICA8L0F1dGhQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIkF1dGhQcm92aWRlciIsIkFwcCIsIkNvbXBvbmVudCIsInBhZ2VQcm9wcyIsImRpdiIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/pages/_app.tsx\n");

/***/ }),

/***/ "./src/pages/add-sample-data.tsx":
/*!***************************************!*\
  !*** ./src/pages/add-sample-data.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AddSampleData)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,CheckCircle,Database,Package,ShoppingCart,Truck,Users,XCircle!=!lucide-react */ \"__barrel_optimize__?names=BarChart3,CheckCircle,Database,Package,ShoppingCart,Truck,Users,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_components_ui_card__WEBPACK_IMPORTED_MODULE_2__, _components_ui_button__WEBPACK_IMPORTED_MODULE_3__, _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__]);\n([_components_ui_card__WEBPACK_IMPORTED_MODULE_2__, _components_ui_button__WEBPACK_IMPORTED_MODULE_3__, _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\nfunction AddSampleData() {\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const addSampleData = async ()=>{\n        setLoading(true);\n        setResult(null);\n        try {\n            const response = await fetch(\"/api/postgres/add-sample-data\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            setResult(data);\n        } catch (err) {\n            setResult({\n                success: false,\n                message: \"خطأ في إضافة البيانات التجريبية\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const dataTypes = [\n        {\n            title: \"العملاء\",\n            count: \"5 عملاء\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Users, {\n                className: \"h-8 w-8 text-blue-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                lineNumber: 46,\n                columnNumber: 13\n            }, this),\n            description: \"شركات وعملاء متنوعين\"\n        },\n        {\n            title: \"الموردين\",\n            count: \"3 موردين\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Truck, {\n                className: \"h-8 w-8 text-green-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                lineNumber: 52,\n                columnNumber: 13\n            }, this),\n            description: \"موردين للإلكترونيات وقطع الغيار\"\n        },\n        {\n            title: \"المنتجات\",\n            count: \"8 منتجات\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Package, {\n                className: \"h-8 w-8 text-purple-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                lineNumber: 58,\n                columnNumber: 13\n            }, this),\n            description: \"لابتوبات، هواتف، إكسسوارات\"\n        },\n        {\n            title: \"أوامر المبيعات\",\n            count: \"3 أوامر\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ShoppingCart, {\n                className: \"h-8 w-8 text-orange-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                lineNumber: 64,\n                columnNumber: 13\n            }, this),\n            description: \"أوامر مبيعات بحالات مختلفة\"\n        },\n        {\n            title: \"حركات المخزون\",\n            count: \"5 حركات\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.BarChart3, {\n                className: \"h-8 w-8 text-red-600\"\n            }, void 0, false, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                lineNumber: 70,\n                columnNumber: 13\n            }, this),\n            description: \"مبيعات ومشتريات\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto space-y-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white p-4 rounded-full shadow-lg inline-block mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Database, {\n                                className: \"h-12 w-12 text-blue-600\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                            lineNumber: 80,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold text-gray-900 mb-2\",\n                            children: \"إضافة بيانات تجريبية\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600\",\n                            children: \"إضافة بيانات تجريبية لاختبار النظام\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\",\n                    children: dataTypes.map((type, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                            className: \"bg-white shadow-lg hover:shadow-xl transition-shadow\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                                className: \"p-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-center mb-4\",\n                                        children: type.icon\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold mb-2\",\n                                        children: type.title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl font-bold text-blue-600 mb-2\",\n                                        children: type.count\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 text-sm\",\n                                        children: type.description\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    className: \"bg-white shadow-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            className: \"text-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"text-2xl\",\n                                children: \"إضافة البيانات التجريبية\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                lineNumber: 110,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"space-y-6\",\n                            children: [\n                                !result && !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"سيتم إضافة بيانات تجريبية شاملة لجميع المديولات\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-yellow-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-yellow-800 mb-2\",\n                                                    children: \"⚠️ تنبيه\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-yellow-700 text-sm\",\n                                                    children: \"هذه البيانات للاختبار فقط. يمكنك حذفها لاحقاً وإضافة بياناتك الحقيقية.\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: addSampleData,\n                                            size: \"lg\",\n                                            className: \"bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-8 py-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Database, {\n                                                    className: \"h-5 w-5 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"إضافة البيانات التجريبية\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 15\n                                }, this),\n                                loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Database, {\n                                            className: \"h-12 w-12 mx-auto text-blue-600 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-lg font-semibold\",\n                                            children: \"جاري إضافة البيانات...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"يرجى الانتظار حتى اكتمال العملية\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 15\n                                }, this),\n                                result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                    className: result.success ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\",\n                                    children: [\n                                        result.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.CheckCircle, {\n                                            className: \"h-4 w-4 text-green-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 19\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_CheckCircle_Database_Package_ShoppingCart_Truck_Users_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.XCircle, {\n                                            className: \"h-4 w-4 text-red-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 148,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-lg\",\n                                                        children: result.message\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    result.data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"grid grid-cols-2 md:grid-cols-5 gap-4 text-sm\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-white rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-blue-600\",\n                                                                        children: result.data.customers\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"عملاء\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 158,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-white rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-green-600\",\n                                                                        children: result.data.suppliers\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"موردين\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 162,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                lineNumber: 160,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-white rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-purple-600\",\n                                                                        children: result.data.products\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 165,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"منتجات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                lineNumber: 164,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-white rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-orange-600\",\n                                                                        children: result.data.salesOrders\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 169,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"أوامر مبيعات\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center p-3 bg-white rounded\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-2xl font-bold text-red-600\",\n                                                                        children: result.data.stockMovements\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 173,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600\",\n                                                                        children: \"حركات مخزون\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                        lineNumber: 174,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                lineNumber: 172,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 pt-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: ()=>window.location.href = \"/products\",\n                                                                variant: \"outline\",\n                                                                children: \"عرض المنتجات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                lineNumber: 180,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: ()=>window.location.href = \"/sales\",\n                                                                variant: \"outline\",\n                                                                children: \"عرض المبيعات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                lineNumber: 186,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                onClick: ()=>window.location.href = \"/dashboard\",\n                                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                                children: \"لوحة التحكم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                        lineNumber: 179,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                                lineNumber: 151,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n            lineNumber: 77,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\add-sample-data.tsx\",\n        lineNumber: 76,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/add-sample-data.tsx\n");

/***/ }),

/***/ "./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "@radix-ui/react-slot":
/*!***************************************!*\
  !*** external "@radix-ui/react-slot" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@radix-ui/react-slot");;

/***/ }),

/***/ "class-variance-authority":
/*!*******************************************!*\
  !*** external "class-variance-authority" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("class-variance-authority");;

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "tailwind-merge":
/*!*********************************!*\
  !*** external "tailwind-merge" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tailwind-merge");;

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fadd-sample-data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Cadd-sample-data.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();