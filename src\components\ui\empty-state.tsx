import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Plus, Database, FileX } from 'lucide-react'

interface EmptyStateProps {
  title: string
  description: string
  actionLabel?: string
  actionHref?: string
  onAction?: () => void
  icon?: React.ReactNode
}

export function EmptyState({ 
  title, 
  description, 
  actionLabel, 
  actionHref, 
  onAction,
  icon 
}: EmptyStateProps) {
  return (
    <Card className="border-dashed border-2 border-gray-300">
      <CardContent className="flex flex-col items-center justify-center py-12 px-6 text-center">
        <div className="mb-4">
          {icon || <FileX className="h-16 w-16 text-gray-400" />}
        </div>
        
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {title}
        </h3>
        
        <p className="text-gray-600 mb-6 max-w-md">
          {description}
        </p>
        
        {(actionLabel && (actionHref || onAction)) && (
          <div>
            {actionHref ? (
              <Button asChild className="bg-blue-600 hover:bg-blue-700">
                <a href={actionHref}>
                  <Plus className="h-4 w-4 mr-2" />
                  {actionLabel}
                </a>
              </Button>
            ) : (
              <Button onClick={onAction} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="h-4 w-4 mr-2" />
                {actionLabel}
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// مكونات محددة لكل نوع بيانات
export function EmptyProducts() {
  return (
    <EmptyState
      title="لا توجد منتجات"
      description="ابدأ بإضافة منتجاتك الأولى لبناء كتالوج منتجاتك"
      actionLabel="إضافة منتج جديد"
      actionHref="/products/new"
      icon={<Package className="h-16 w-16 text-gray-400" />}
    />
  )
}

export function EmptyCustomers() {
  return (
    <EmptyState
      title="لا توجد عملاء"
      description="أضف عملاءك لبدء إدارة علاقاتك التجارية"
      actionLabel="إضافة عميل جديد"
      actionHref="/contacts/customers/new"
      icon={<Users className="h-16 w-16 text-gray-400" />}
    />
  )
}

export function EmptySuppliers() {
  return (
    <EmptyState
      title="لا توجد موردين"
      description="أضف الموردين لإدارة عمليات الشراء والتوريد"
      actionLabel="إضافة مورد جديد"
      actionHref="/contacts/suppliers/new"
      icon={<Truck className="h-16 w-16 text-gray-400" />}
    />
  )
}

export function EmptySalesOrders() {
  return (
    <EmptyState
      title="لا توجد أوامر مبيعات"
      description="ابدأ بإنشاء أول أمر مبيعات لعملائك"
      actionLabel="إنشاء أمر مبيعات"
      actionHref="/sales/orders/new"
      icon={<ShoppingCart className="h-16 w-16 text-gray-400" />}
    />
  )
}

export function EmptyQuotations() {
  return (
    <EmptyState
      title="لا توجد عروض أسعار"
      description="أنشئ عروض أسعار لعملائك المحتملين"
      actionLabel="إنشاء عرض سعر"
      actionHref="/sales/quotations/new"
      icon={<FileText className="h-16 w-16 text-gray-400" />}
    />
  )
}

export function EmptyInvoices() {
  return (
    <EmptyState
      title="لا توجد فواتير"
      description="ابدأ بإنشاء فواتير المبيعات لعملائك"
      actionLabel="إنشاء فاتورة"
      actionHref="/sales/invoices/new"
      icon={<Receipt className="h-16 w-16 text-gray-400" />}
    />
  )
}

export function EmptyPurchaseOrders() {
  return (
    <EmptyState
      title="لا توجد أوامر شراء"
      description="أنشئ أوامر شراء للحصول على المنتجات من الموردين"
      actionLabel="إنشاء أمر شراء"
      actionHref="/purchases/orders/new"
      icon={<ClipboardList className="h-16 w-16 text-gray-400" />}
    />
  )
}

export function EmptyMaintenanceRequests() {
  return (
    <EmptyState
      title="لا توجد طلبات صيانة"
      description="ابدأ بإضافة طلبات الصيانة الفنية"
      actionLabel="إضافة طلب صيانة"
      actionHref="/maintenance/requests/new"
      icon={<Wrench className="h-16 w-16 text-gray-400" />}
    />
  )
}

export function EmptyExpenses() {
  return (
    <EmptyState
      title="لا توجد مصروفات"
      description="سجل مصروفاتك لتتبع التكاليف والنفقات"
      actionLabel="إضافة مصروف"
      actionHref="/accounting/expenses/new"
      icon={<DollarSign className="h-16 w-16 text-gray-400" />}
    />
  )
}

// إضافة الأيقونات المفقودة
import { 
  Package, 
  Users, 
  Truck, 
  ShoppingCart, 
  FileText, 
  Receipt, 
  ClipboardList, 
  Wrench, 
  DollarSign 
} from 'lucide-react'
