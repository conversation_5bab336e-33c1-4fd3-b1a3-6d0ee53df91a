# 🔧 دليل استخدام موديول الصيانة الفنية

## 🎯 **نظرة عامة**
موديول الصيانة الفنية هو نظام متكامل لإدارة جميع عمليات الصيانة من استلام الأجهزة حتى التسليم، مع سير عمل محدد، تكامل مع المخزون والمحاسبة، ونظام تواصل تلقائي مع العملاء عبر واتساب.

---

## 📋 **المكونات الرئيسية**

### 1. **إدارة طلبات الصيانة (Maintenance Requests)**
### 2. **نظام التذاكر (Tickets System)**  
### 3. **سير العمل المتقدم (Advanced Workflow)**
### 4. **التكامل مع المخزون (Inventory Integration)**
### 5. **التقارير والتحليلات (Reports & Analytics)**

---

## 📝 **1. إدارة طلبات الصيانة (Maintenance Requests)**

### 📍 **الوصول**: `/maintenance/requests`

### ✨ **الميزات الرئيسية**
- سير عمل محدد من الاستلام للتسليم
- تكامل مع قاعدة بيانات العملاء
- تتبع دقيق لحالة كل طلب
- نظام تواصل تلقائي مع العملاء

### 🔧 **إنشاء طلب صيانة جديد**

#### **الخطوة 1: بيانات العميل**
1. انقر "طلب صيانة جديد"
2. **اختيار العميل**:
   - ابحث في قاعدة البيانات الموجودة
   - أو أدخل بيانات عميل جديد
   - سيتم ملء البيانات تلقائياً (الاسم، الهاتف، واتساب)

#### **الخطوة 2: بيانات الجهاز**
- **نوع الجهاز**: لابتوب، ديسكتوب، هاتف، طابعة، إلخ
- **الموديل والعلامة التجارية**: HP EliteBook 840, iPhone 13, إلخ
- **الرقم التسلسلي**: للتتبع والضمان
- **حالة الجهاز**: وصف الحالة الظاهرية

#### **الخطوة 3: المرفقات**
- ✅ **شاحن**: متوفر/غير متوفر
- ✅ **بطارية**: متوفرة/غير متوفرة  
- ✅ **كرتونة**: متوفرة/غير متوفرة
- ✅ **دليل الاستخدام**: متوفر/غير متوفر
- ✅ **كابلات**: متوفرة/غير متوفرة
- ✅ **جراب**: متوفر/غير متوفر

#### **الخطوة 4: وصف المشكلة**
- وصف تفصيلي للمشكلة من العميل
- الأعراض الظاهرة
- متى بدأت المشكلة
- أي محاولات إصلاح سابقة

#### **الخطوة 5: بيانات إضافية**
- **الفرع والمخزن**: لربط قطع الغيار
- **الأولوية**: منخفضة، متوسطة، عالية، عاجلة
- **التاريخ المتوقع للتسليم**: تلقائي (15 يوم)
- **ملاحظات خاصة**

### 🎯 **النتائج التلقائية عند الحفظ**
- ✅ **رقم تتبع تلقائي**: MNT-2024-XXXXXX
- ✅ **إنشاء تذكرة**: مع عد تنازلي 15 يوم
- ✅ **رسالة واتساب للعميل**: تأكيد الاستلام
- ✅ **تحديث سجل العميل**: إضافة طلب الصيانة

---

## 🔄 **2. سير العمل المتقدم (Advanced Workflow)**

### 📊 **مراحل سير العمل**

#### **🧾 المرحلة 1: استلام الجهاز (Received)**
- **الحالة**: `received`
- **الوصف**: تم استلام الجهاز وتسجيل البيانات
- **الإجراءات المتاحة**:
  - ▶️ بدء التشخيص
  - ❌ إلغاء الطلب
- **رسالة واتساب**: "تم استلام جهازك [نوع الجهاز] بنجاح. رقم التتبع: [رقم]. سيتم فحص الجهاز والتواصل معك خلال 24 ساعة."

#### **🔍 المرحلة 2: التشخيص الفني (Diagnosing)**
- **الحالة**: `diagnosing`
- **الوصف**: جارٍ فحص الجهاز وتحديد المشكلة
- **الإجراءات المتاحة**:
  - ▶️ إرسال التشخيص للعميل
  - ❌ إلغاء الطلب
- **المطلوب من الفني**:
  - فحص شامل للجهاز
  - تحديد المشكلة الفعلية
  - تقدير التكلفة والوقت

#### **📤 المرحلة 3: انتظار موافقة العميل (Awaiting Approval)**
- **الحالة**: `awaiting_approval`
- **الوصف**: تم إرسال التشخيص والتكلفة للعميل
- **البيانات المطلوبة**:
  - 📝 **وصف المشكلة**: تفصيلي ودقيق
  - 🔍 **السبب الجذري**: لماذا حدثت المشكلة
  - 🛠️ **الحل المقترح**: كيفية الإصلاح
  - 💰 **التكلفة المقدرة**: مبلغ الإصلاح
  - 🔧 **قطع الغيار المطلوبة**: إن وجدت
- **رسالة واتساب**: "تم فحص جهازك رقم [رقم]. المشكلة: [وصف]. تكلفة الإصلاح: [مبلغ] جنيه. للموافقة اكتب 'موافق' أو للرفض اكتب 'رفض'."

#### **✅ المرحلة 4أ: موافقة العميل (Approved)**
- **الحالة**: `approved`
- **الوصف**: العميل وافق على الإصلاح والتكلفة
- **الإجراءات المتاحة**:
  - ▶️ بدء الإصلاح
  - ❌ إلغاء الطلب
- **رسالة واتساب**: "تم تأكيد موافقتك على إصلاح الجهاز رقم [رقم]. سيتم البدء في الإصلاح فوراً."

#### **❌ المرحلة 4ب: رفض العميل (Rejected)**
- **الحالة**: `rejected`
- **الوصف**: العميل رفض الإصلاح
- **الإجراءات المتاحة**:
  - ▶️ تسليم الجهاز (بدون إصلاح)
- **رسالة واتساب**: "تم تسجيل رفضك لإصلاح الجهاز رقم [رقم]. يمكنك استلام الجهاز خلال 15 يوم."

#### **🔧 المرحلة 5: جارٍ الإصلاح (In Repair)**
- **الحالة**: `in_repair`
- **الوصف**: العمل على إصلاح الجهاز
- **الإجراءات المتاحة**:
  - ▶️ اكتمال الإصلاح
  - ❌ إلغاء الطلب
- **المهام**:
  - تنفيذ الإصلاح المطلوب
  - استخدام قطع الغيار حسب الحاجة
  - اختبار الجهاز بعد الإصلاح

#### **✅ المرحلة 6: اكتمال الإصلاح (Repair Completed)**
- **الحالة**: `repair_completed`
- **الوصف**: تم إصلاح الجهاز بنجاح
- **البيانات المطلوبة**:
  - 🔧 **قطع الغيار المستخدمة**: تسجيل دقيق
  - 💰 **التكلفة النهائية**: قد تختلف عن المقدرة
- **العمليات التلقائية**:
  - ✅ خصم قطع الغيار من المخزون
  - ✅ إنشاء فاتورة تلقائية
  - ✅ تحديث كشف حساب العميل
- **رسالة واتساب**: "تم إصلاح جهازك رقم [رقم] بنجاح! الجهاز جاهز للاستلام. رابط الفاتورة: [رابط]"

#### **📦 المرحلة 7: تسليم الجهاز (Delivered)**
- **الحالة**: `delivered`
- **الوصف**: تم تسليم الجهاز للعميل
- **حالة نهائية**: لا يمكن تعديلها
- **العمليات النهائية**:
  - ✅ إغلاق التذكرة
  - ✅ تحديث الإحصائيات
  - ✅ أرشفة الطلب

---

## 🎫 **3. نظام التذاكر (Tickets System)**

### 📍 **الوصول**: `/maintenance/tickets`

### ✨ **الميزات الرئيسية**
- تذكرة تلقائية لكل طلب صيانة
- عد تنازلي للمواعيد النهائية (15 يوم)
- تنبيهات عاجلة للتذاكر المتأخرة
- فلترة وترتيب متقدم

### 🔧 **إدارة التذاكر**

#### **حالات التذاكر**
- 🟢 **مفتوحة**: طلبات جديدة
- 🟡 **قيد التنفيذ**: جارٍ العمل عليها
- 🔴 **عاجلة**: أقل من 3 أيام متبقية
- ✅ **مغلقة**: تم التسليم

#### **الفلترة والبحث**
- **حسب الحالة**: مفتوحة، عاجلة، مغلقة
- **حسب الأولوية**: منخفضة، متوسطة، عالية، عاجلة
- **حسب الفني المسؤول**
- **حسب نوع الجهاز**
- **حسب العميل**

#### **التنبيهات التلقائية**
- 🔔 **يومياً**: للتذاكر العاجلة
- 📧 **أسبوعياً**: تقرير حالة التذاكر
- 📱 **فورية**: عند تجاوز الموعد النهائي

---

## 📦 **4. التكامل مع المخزون (Inventory Integration)**

### 🔧 **استخدام قطع الغيار**

#### **عند اكتمال الإصلاح**
1. **تسجيل القطع المستخدمة**:
   - اختيار المنتج من قائمة المخزون
   - تحديد الكمية المستخدمة
   - التكلفة (تلقائية من المخزون)
2. **التحقق من التوفر**:
   - التأكد من وجود الكمية المطلوبة
   - تحذير في حالة عدم التوفر
3. **الخصم التلقائي**:
   - خصم الكميات من المخزون
   - تسجيل حركة مخزون بنوع "maintenance"
   - ربط بطلب الصيانة للمراجعة

#### **تتبع استخدام قطع الغيار**
- **أكثر القطع استخداماً**: لتحسين المخزون
- **تكلفة قطع الغيار**: لكل طلب صيانة
- **مخزون قطع الصيانة**: منفصل عن المبيعات

---

## 💰 **5. التكامل مع المحاسبة**

### 🧾 **إنشاء الفواتير التلقائية**

#### **مكونات فاتورة الصيانة**
- **تكلفة الخدمة**: أجر الفني والتشخيص
- **قطع الغيار**: بالتكلفة الفعلية + هامش ربح
- **الضرائب**: حسب النظام الضريبي
- **الخصومات**: إن وجدت

#### **التكامل مع كشف حساب العميل**
- ✅ **إضافة تلقائية**: للمعاملة في كشف العميل
- ✅ **تحديث الرصيد**: زيادة المدين
- ✅ **ربط المرجع**: بطلب الصيانة

#### **التدفق النقدي**
- ✅ **تسجيل الإيراد**: في فئة "خدمات الصيانة"
- ✅ **تصنيف منفصل**: عن إيرادات المبيعات
- ✅ **تقارير مخصصة**: لإيرادات الصيانة

---

## 📊 **6. التقارير والتحليلات (Reports & Analytics)**

### 📍 **الوصول**: `/maintenance/reports`

### 📈 **التقارير المتاحة**

#### **تقرير الأداء العام**
- إجمالي طلبات الصيانة
- معدل النمو الشهري
- توزيع الطلبات حسب الحالة
- متوسط وقت الإصلاح

#### **تقرير الموافقات والرفض**
- معدل موافقة العملاء على الإصلاح
- أسباب الرفض الأكثر شيوعاً
- تحليل التكاليف المرفوضة

#### **تقرير الإيرادات**
- إجمالي إيرادات الصيانة
- متوسط قيمة طلب الصيانة
- نمو الإيرادات الشهري
- مقارنة مع الأهداف

#### **تقرير قطع الغيار**
- أكثر قطع الغيار استخداماً
- تكلفة قطع الغيار الشهرية
- نسبة قطع الغيار من إجمالي التكلفة

#### **تقرير أنواع الأجهزة**
- توزيع الطلبات حسب نوع الجهاز
- أكثر المشاكل شيوعاً لكل نوع
- متوسط تكلفة الإصلاح لكل نوع

#### **تقرير أداء الفنيين**
- عدد الطلبات المكتملة لكل فني
- متوسط وقت الإصلاح
- معدل رضا العملاء
- تقييم الأداء

---

## 🎯 **نصائح للاستخدام الأمثل**

### ✅ **أفضل الممارسات**
1. **سجل البيانات كاملة** عند الاستلام
2. **تواصل مع العميل** في كل مرحلة
3. **وثق التشخيص بدقة** قبل الإرسال للعميل
4. **سجل قطع الغيار** فور الاستخدام
5. **راجع التذاكر العاجلة** يومياً
6. **استخدم التقارير** لتحسين الأداء

### ⚠️ **تجنب هذه الأخطاء**
- عدم تحديث حالة الطلب فوراً
- إرسال تشخيص غير دقيق للعميل
- نسيان تسجيل قطع الغيار المستخدمة
- تجاهل التذاكر العاجلة
- عدم متابعة رضا العملاء

### 🔧 **اختصارات مفيدة**
- **F5**: تحديث قائمة الطلبات
- **Ctrl + N**: طلب صيانة جديد
- **Ctrl + F**: البحث السريع
- **Enter**: فتح تفاصيل الطلب

---

## 🔗 **الروابط السريعة**

- **الصفحة الرئيسية**: `/maintenance`
- **طلبات الصيانة**: `/maintenance/requests`
- **طلب جديد**: `/maintenance/requests/new`
- **التذاكر**: `/maintenance/tickets`
- **التقارير**: `/maintenance/reports`

---

**💡 ملاحظة**: موديول الصيانة الفنية مصمم ليكون نظاماً متكاملاً يغطي جميع جوانب عمليات الصيانة مع التركيز على تجربة العميل وكفاءة العمليات الداخلية.
