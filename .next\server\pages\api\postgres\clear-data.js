"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/postgres/clear-data";
exports.ids = ["pages/api/postgres/clear-data"];
exports.modules = {

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fclear-data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Cclear-data.ts&middlewareConfigBase64=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fclear-data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Cclear-data.ts&middlewareConfigBase64=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_postgres_clear_data_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\postgres\\clear-data.ts */ \"(api)/./src/pages/api/postgres/clear-data.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_clear_data_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_clear_data_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/postgres/clear-data\",\n        pathname: \"/api/postgres/clear-data\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_postgres_clear_data_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fclear-data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Cclear-data.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/postgres.ts":
/*!*****************************!*\
  !*** ./src/lib/postgres.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTables: () => (/* binding */ checkTables),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   findUser: () => (/* binding */ findUser),\n/* harmony export */   getCustomers: () => (/* binding */ getCustomers),\n/* harmony export */   getInventory: () => (/* binding */ getInventory),\n/* harmony export */   getMaintenanceRequests: () => (/* binding */ getMaintenanceRequests),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getPurchaseOrders: () => (/* binding */ getPurchaseOrders),\n/* harmony export */   getSalesOrders: () => (/* binding */ getSalesOrders),\n/* harmony export */   getStockMovements: () => (/* binding */ getStockMovements),\n/* harmony export */   getSuppliers: () => (/* binding */ getSuppliers),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\n// إعداد الاتصال بقاعدة البيانات PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"5432\"),\n    database: process.env.DB_NAME || \"Vero_ERP_ABA\",\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    ssl:  false ? 0 : false,\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const client = await pool.connect();\n        const result = await client.query(\"SELECT NOW()\");\n        client.release();\n        return {\n            success: true,\n            time: result.rows[0].now\n        };\n    } catch (error) {\n        console.error(\"خطأ في الاتصال بقاعدة البيانات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    try {\n        const result = await pool.query(text, params);\n        return {\n            success: true,\n            data: result.rows,\n            rowCount: result.rowCount\n        };\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للتحقق من وجود الجداول\nconst checkTables = async ()=>{\n    try {\n        const result = await query(`\n      SELECT table_name\n      FROM information_schema.tables\n      WHERE table_schema = 'public'\n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = result.data?.map((row)=>row.table_name) || [];\n        return {\n            success: true,\n            tables,\n            hasBranches: tables.includes(\"branches\"),\n            hasUsers: tables.includes(\"users\"),\n            hasProducts: tables.includes(\"products\"),\n            hasWarehouses: tables.includes(\"warehouses\"),\n            needsSetup: !tables.includes(\"branches\")\n        };\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return {\n            success: false,\n            error,\n            needsSetup: true\n        };\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const result = await query(`\n      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)\n      VALUES ($1, $2, $3, $4, $5, true, NOW())\n      RETURNING id, email, username, full_name, role\n    `, [\n            userData.email,\n            userData.username,\n            userData.full_name,\n            userData.password_hash,\n            userData.role || \"admin\"\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0]\n        };\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للبحث عن مستخدم\nconst findUser = async (email)=>{\n    try {\n        const result = await query(`\n      SELECT id, email, username, full_name, role, password_hash, is_active\n      FROM users\n      WHERE email = $1 AND is_active = true\n    `, [\n            email\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0] || null\n        };\n    } catch (error) {\n        console.error(\"خطأ في البحث عن المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المنتجات متوافقة مع الكود\nconst getProducts = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        p.*,\n        i.total_stock,\n        i.available_stock,\n        i.reserved_stock\n      FROM products p\n      LEFT JOIN inventory i ON p.id = i.product_id\n      WHERE p.is_active = true\n      ORDER BY p.name\n    `);\n        return {\n            success: true,\n            data: result.data?.map((product)=>({\n                    ...product,\n                    current_stock: product.total_stock || 0,\n                    price: product.unit_price,\n                    cost: product.cost_price\n                })) || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المنتجات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المخزون متوافقة مع الكود\nconst getInventory = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        i.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        p.category as product_category,\n        w.name as warehouse_name\n      FROM inventory i\n      JOIN products p ON i.product_id = p.id\n      JOIN warehouses w ON i.warehouse_id = w.id\n      ORDER BY i.updated_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات العملاء\nconst getCustomers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM customers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب العملاء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات الموردين\nconst getSuppliers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM suppliers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب الموردين:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات أوامر المبيعات\nconst getSalesOrders = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        so.*,\n        c.name as customer_name,\n        b.name as branch_name,\n        u.full_name as created_by_name\n      FROM sales_orders so\n      LEFT JOIN customers c ON so.customer_id = c.id\n      LEFT JOIN branches b ON so.branch_id = b.id\n      LEFT JOIN users u ON so.created_by = u.id\n      ORDER BY so.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب أوامر المبيعات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات أوامر الشراء\nconst getPurchaseOrders = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        po.*,\n        s.name as supplier_name,\n        b.name as branch_name,\n        w.name as warehouse_name,\n        u.full_name as created_by_name\n      FROM purchase_orders po\n      LEFT JOIN suppliers s ON po.supplier_id = s.id\n      LEFT JOIN branches b ON po.branch_id = b.id\n      LEFT JOIN warehouses w ON po.warehouse_id = w.id\n      LEFT JOIN users u ON po.created_by = u.id\n      ORDER BY po.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب أوامر الشراء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات حركات المخزون\nconst getStockMovements = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        sm.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        w.name as warehouse_name,\n        u.full_name as created_by_name\n      FROM stock_movements sm\n      JOIN products p ON sm.product_id = p.id\n      JOIN warehouses w ON sm.warehouse_id = w.id\n      LEFT JOIN users u ON sm.created_by = u.id\n      ORDER BY sm.created_at DESC\n      LIMIT 100\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب حركات المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات طلبات الصيانة\nconst getMaintenanceRequests = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        mr.*,\n        t.full_name as technician_name,\n        at.full_name as assigned_technician_name,\n        b.name as branch_name,\n        w.name as warehouse_name\n      FROM maintenance_requests mr\n      LEFT JOIN users t ON mr.technician_id = t.id\n      LEFT JOIN users at ON mr.assigned_technician = at.id\n      LEFT JOIN branches b ON mr.branch_id = b.id\n      LEFT JOIN warehouses w ON mr.warehouse_id = w.id\n      ORDER BY mr.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب طلبات الصيانة:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/postgres.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/postgres/clear-data.ts":
/*!**********************************************!*\
  !*** ./src/pages/api/postgres/clear-data.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/postgres */ \"(api)/./src/lib/postgres.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const results = [];\n        let successCount = 0;\n        let errorCount = 0;\n        // قائمة الجداول التي سيتم مسحها (بالترتيب الصحيح لتجنب مشاكل المفاتيح الخارجية)\n        const tablesToClear = [\n            // جداول التفاصيل أولاً\n            \"sales_order_items\",\n            \"purchase_order_items\",\n            \"maintenance_required_parts\",\n            \"maintenance_used_parts\",\n            \"installment_payments\",\n            \"stock_movements\",\n            \"cash_transactions\",\n            // الجداول الرئيسية\n            \"sales_orders\",\n            \"purchase_orders\",\n            \"maintenance_requests\",\n            \"installments\",\n            \"expenses\",\n            \"inventory\",\n            \"products\",\n            \"customers\",\n            \"suppliers\"\n        ];\n        console.log(\"بدء مسح البيانات الوهمية...\");\n        for (const tableName of tablesToClear){\n            try {\n                console.log(`مسح جدول: ${tableName}`);\n                const result = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`DELETE FROM ${tableName}`);\n                if (result.success) {\n                    successCount++;\n                    results.push({\n                        table: tableName,\n                        success: true,\n                        message: `تم مسح جدول ${tableName} بنجاح`,\n                        rowsDeleted: result.rowCount || 0\n                    });\n                    console.log(`✅ تم مسح ${result.rowCount || 0} سجل من ${tableName}`);\n                } else {\n                    errorCount++;\n                    results.push({\n                        table: tableName,\n                        success: false,\n                        error: result.error,\n                        message: `فشل في مسح جدول ${tableName}`\n                    });\n                    console.log(`❌ فشل في مسح ${tableName}:`, result.error);\n                }\n            } catch (error) {\n                errorCount++;\n                const errorMessage = error instanceof Error ? error.message : \"خطأ غير معروف\";\n                results.push({\n                    table: tableName,\n                    success: false,\n                    error: errorMessage,\n                    message: `خطأ في مسح جدول ${tableName}`\n                });\n                console.log(`❌ خطأ في مسح ${tableName}:`, errorMessage);\n            }\n        }\n        // إعادة تعيين تسلسل الجداول\n        console.log(\"إعادة تعيين تسلسل الجداول...\");\n        const sequenceResets = [\n            \"ALTER SEQUENCE products_id_seq RESTART WITH 1\",\n            \"ALTER SEQUENCE customers_id_seq RESTART WITH 1\",\n            \"ALTER SEQUENCE suppliers_id_seq RESTART WITH 1\",\n            \"ALTER SEQUENCE sales_orders_id_seq RESTART WITH 1\",\n            \"ALTER SEQUENCE purchase_orders_id_seq RESTART WITH 1\",\n            \"ALTER SEQUENCE maintenance_requests_id_seq RESTART WITH 1\",\n            \"ALTER SEQUENCE expenses_id_seq RESTART WITH 1\",\n            \"ALTER SEQUENCE installments_id_seq RESTART WITH 1\"\n        ];\n        for (const resetQuery of sequenceResets){\n            try {\n                await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(resetQuery);\n                console.log(`✅ تم إعادة تعيين التسلسل`);\n            } catch (error) {\n                console.log(`⚠️ تحذير في إعادة تعيين التسلسل:`, error);\n            }\n        }\n        // فحص حالة الجداول بعد المسح\n        const tablesCheck = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.query)(`\n      SELECT \n        schemaname,\n        tablename,\n        n_tup_ins as total_rows\n      FROM pg_stat_user_tables \n      WHERE schemaname = 'public'\n      AND tablename IN (${tablesToClear.map((t)=>`'${t}'`).join(\",\")})\n      ORDER BY tablename\n    `);\n        const remainingData = tablesCheck.success ? tablesCheck.data : [];\n        return res.status(200).json({\n            success: successCount > 0,\n            message: `تم مسح ${successCount} جدول بنجاح، ${errorCount} فشل`,\n            summary: {\n                totalTables: tablesToClear.length,\n                successful: successCount,\n                failed: errorCount,\n                clearedTables: results.filter((r)=>r.success).map((r)=>r.table),\n                failedTables: results.filter((r)=>!r.success).map((r)=>r.table)\n            },\n            results: results,\n            remainingData: remainingData,\n            recommendations: successCount > 0 ? [\n                \"✅ تم مسح جميع البيانات الوهمية بنجاح\",\n                \"\\uD83D\\uDC64 تم الاحتفاظ بالمدير والصلاحيات\",\n                \"\\uD83C\\uDFD7️ تم الاحتفاظ بهيكل قاعدة البيانات\",\n                \"\\uD83D\\uDCCA قاعدة البيانات فارغة وجاهزة للبيانات الحقيقية\",\n                \"\\uD83D\\uDE80 يمكنك الآن إضافة بياناتك الحقيقية\"\n            ] : [\n                \"❌ فشل في مسح البيانات\",\n                \"\\uD83D\\uDD0D تحقق من أخطاء قاعدة البيانات\",\n                \"\\uD83D\\uDCDE تحقق من الاتصال بـ PostgreSQL\"\n            ]\n        });\n    } catch (error) {\n        console.error(\"خطأ في مسح البيانات:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/postgres/clear-data.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Fclear-data&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Cclear-data.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();