import { NextApiRequest, NextApiResponse } from 'next'
import { query } from '@/lib/postgres'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { sql } = req.body

    if (!sql) {
      return res.status(400).json({
        success: false,
        message: 'SQL query is required'
      })
    }

    console.log('تنفيذ الاستعلام:', sql.substring(0, 100) + '...')
    
    const result = await query(sql)

    return res.status(200).json({
      success: result.success,
      message: result.success ? 'تم تنفيذ الاستعلام بنجاح' : 'فشل في تنفيذ الاستعلام',
      data: result.data,
      rowCount: result.rowCount,
      error: result.error,
      query: sql.substring(0, 200) + (sql.length > 200 ? '...' : '')
    })

  } catch (error) {
    console.error('خطأ في تنفيذ الاستعلام:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
