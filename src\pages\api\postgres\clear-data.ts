import { NextApiRequest, NextApiResponse } from 'next'
import { query } from '@/lib/postgres'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const results = []
    let successCount = 0
    let errorCount = 0

    // قائمة الجداول التي سيتم مسحها (بالترتيب الصحيح لتجنب مشاكل المفاتيح الخارجية)
    const tablesToClear = [
      // جداول التفاصيل أولاً
      'sales_order_items',
      'purchase_order_items', 
      'maintenance_required_parts',
      'maintenance_used_parts',
      'installment_payments',
      'stock_movements',
      'cash_transactions',
      
      // الجداول الرئيسية
      'sales_orders',
      'purchase_orders',
      'maintenance_requests',
      'installments',
      'expenses',
      'inventory',
      'products',
      'customers',
      'suppliers'
    ]

    console.log('بدء مسح البيانات الوهمية...')

    for (const tableName of tablesToClear) {
      try {
        console.log(`مسح جدول: ${tableName}`)
        
        const result = await query(`DELETE FROM ${tableName}`)
        
        if (result.success) {
          successCount++
          results.push({
            table: tableName,
            success: true,
            message: `تم مسح جدول ${tableName} بنجاح`,
            rowsDeleted: result.rowCount || 0
          })
          console.log(`✅ تم مسح ${result.rowCount || 0} سجل من ${tableName}`)
        } else {
          errorCount++
          results.push({
            table: tableName,
            success: false,
            error: result.error,
            message: `فشل في مسح جدول ${tableName}`
          })
          console.log(`❌ فشل في مسح ${tableName}:`, result.error)
        }
      } catch (error) {
        errorCount++
        const errorMessage = error instanceof Error ? error.message : 'خطأ غير معروف'
        results.push({
          table: tableName,
          success: false,
          error: errorMessage,
          message: `خطأ في مسح جدول ${tableName}`
        })
        console.log(`❌ خطأ في مسح ${tableName}:`, errorMessage)
      }
    }

    // إعادة تعيين تسلسل الجداول
    console.log('إعادة تعيين تسلسل الجداول...')
    
    const sequenceResets = [
      'ALTER SEQUENCE products_id_seq RESTART WITH 1',
      'ALTER SEQUENCE customers_id_seq RESTART WITH 1', 
      'ALTER SEQUENCE suppliers_id_seq RESTART WITH 1',
      'ALTER SEQUENCE sales_orders_id_seq RESTART WITH 1',
      'ALTER SEQUENCE purchase_orders_id_seq RESTART WITH 1',
      'ALTER SEQUENCE maintenance_requests_id_seq RESTART WITH 1',
      'ALTER SEQUENCE expenses_id_seq RESTART WITH 1',
      'ALTER SEQUENCE installments_id_seq RESTART WITH 1'
    ]

    for (const resetQuery of sequenceResets) {
      try {
        await query(resetQuery)
        console.log(`✅ تم إعادة تعيين التسلسل`)
      } catch (error) {
        console.log(`⚠️ تحذير في إعادة تعيين التسلسل:`, error)
      }
    }

    // فحص حالة الجداول بعد المسح
    const tablesCheck = await query(`
      SELECT 
        schemaname,
        tablename,
        n_tup_ins as total_rows
      FROM pg_stat_user_tables 
      WHERE schemaname = 'public'
      AND tablename IN (${tablesToClear.map(t => `'${t}'`).join(',')})
      ORDER BY tablename
    `)

    const remainingData = tablesCheck.success ? tablesCheck.data : []

    return res.status(200).json({
      success: successCount > 0,
      message: `تم مسح ${successCount} جدول بنجاح، ${errorCount} فشل`,
      summary: {
        totalTables: tablesToClear.length,
        successful: successCount,
        failed: errorCount,
        clearedTables: results.filter(r => r.success).map(r => r.table),
        failedTables: results.filter(r => !r.success).map(r => r.table)
      },
      results: results,
      remainingData: remainingData,
      recommendations: successCount > 0 ? [
        '✅ تم مسح جميع البيانات الوهمية بنجاح',
        '👤 تم الاحتفاظ بالمدير والصلاحيات',
        '🏗️ تم الاحتفاظ بهيكل قاعدة البيانات',
        '📊 قاعدة البيانات فارغة وجاهزة للبيانات الحقيقية',
        '🚀 يمكنك الآن إضافة بياناتك الحقيقية'
      ] : [
        '❌ فشل في مسح البيانات',
        '🔍 تحقق من أخطاء قاعدة البيانات',
        '📞 تحقق من الاتصال بـ PostgreSQL'
      ]
    })

  } catch (error) {
    console.error('خطأ في مسح البيانات:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
