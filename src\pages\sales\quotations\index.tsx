import { useState, useEffect } from 'react'
import { EmptyState } from '@/components/ui/empty-state'
import { useAuth } from '@/hooks/useAuth'
import { hasPermission, PERMISSIONS } from '@/lib/auth'
import Layout from '@/components/layout/Layout'
import { EmptyQuotations } from '@/components/ui/empty-state'
import QuotationForm from '@/components/sales/QuotationForm'
import QuotationPrint from '@/components/sales/QuotationPrint'
import ExportData from '@/components/sales/ExportData'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import {
  Plus,
  Search,
  Edit,
  Trash2,
  Eye,
  FileText,
  Send,
  Copy,
  CheckCircle,
  Clock,
  XCircle,
  RefreshCw,
  Filter,
  Download,
  Printer,
  ArrowRight,
  ShoppingCart,
  Mail,
  MessageCircle,
  Share2,
  Star
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'

export default function QuotationsPage() {
  const { user } = useAuth()
  const [quotations, setQuotations] = useState<any[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchQuotations()
  }, [])

  const fetchQuotations = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/postgres/data?type=quotations')
      const result = await response.json()

      if (result.success) {
        setQuotations(result.data || [])
      } else {
        console.error('فشل في جلب عروض الأسعار:', result.error)
      }
    } catch (error) {
      console.error('خطأ في جلب عروض الأسعار:', error)
    } finally {
      setLoading(false)
    }
  }
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [selectedQuotation, setSelectedQuotation] = useState<any>(null)
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingQuotation, setEditingQuotation] = useState<any>(null)
  const [viewingQuotation, setViewingQuotation] = useState<any>(null)
  const [isViewModalOpen, setIsViewModalOpen] = useState(false)

  // تصفية العروض
  const filteredQuotations = quotations.filter(quotation => {
    const matchesSearch = quotation.quotation_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         quotation.customer_name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || quotation.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const canCreate = hasPermission(user, PERMISSIONS.SALES_CREATE)
  const canEdit = hasPermission(user, PERMISSIONS.SALES_EDIT)
  const canDelete = hasPermission(user, PERMISSIONS.SALES_DELETE)

  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'draft':
        return { color: 'bg-gray-100 text-gray-800', icon: Clock, text: 'مسودة' }
      case 'sent':
        return { color: 'bg-blue-100 text-blue-800', icon: Send, text: 'مرسل' }
      case 'accepted':
        return { color: 'bg-green-100 text-green-800', icon: CheckCircle, text: 'مقبول' }
      case 'rejected':
        return { color: 'bg-red-100 text-red-800', icon: XCircle, text: 'مرفوض' }
      case 'expired':
        return { color: 'bg-orange-100 text-orange-800', icon: Clock, text: 'منتهي الصلاحية' }
      default:
        return { color: 'bg-gray-100 text-gray-800', icon: Clock, text: 'غير محدد' }
    }
  }

  const handleCreateQuotation = () => {
    setEditingQuotation(null)
    setIsFormOpen(true)
  }

  const handleViewQuotation = (quotation: any) => {
    setViewingQuotation(quotation)
    setIsViewModalOpen(true)
  }

  const handleEditQuotation = (quotation: any) => {
    setEditingQuotation(quotation)
    setIsFormOpen(true)
  }

  const handleDeleteQuotation = (quotationId: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العرض؟')) {
      setQuotations(quotations.filter(q => q.id !== quotationId))
    }
  }

  const handleSendQuotation = (quotationId: string) => {
    setQuotations(quotations.map(q =>
      q.id === quotationId ? { ...q, status: 'sent' } : q
    ))
    alert('تم إرسال العرض للعميل')
  }

  const handleConvertToOrder = (quotation: any) => {
    console.log('تحويل العرض إلى طلب:', quotation.id)

    // إنشاء طلب مبيعات جديد من بيانات عرض السعر
    `,
      type: 'order',
      status: 'draft',
      payment_status: 'pending',
      delivery_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 7 أيام من الآن
      created_at: new Date().toISOString().split('T')[0]
    }

    // في التطبيق الحقيقي، سيتم إرسال البيانات إلى API
    alert(`تم تحويل عرض السعر ${quotation.quotation_number} إلى طلب مبيعات ${newOrder.order_number}`)
  }

  const handleConvertToInvoice = (quotation: any) => {
    console.log('تحويل العرض إلى فاتورة:', quotation.id)

    // إنشاء فاتورة جديدة من بيانات عرض السعر
    `,
      type: 'invoice',
      status: 'draft',
      payment_method: 'cash',
      paid_amount: 0,
      remaining_amount: quotation.total_amount,
      due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 يوم من الآن
      created_at: new Date().toISOString().split('T')[0]
    }

    // في التطبيق الحقيقي، سيتم إرسال البيانات إلى API
    alert(`تم تحويل عرض السعر ${quotation.quotation_number} إلى فاتورة مبيعات ${newInvoice.invoice_number}`)
  }

  const handleDownloadQuotation = (quotationId: string) => {
    console.log('تحميل عرض السعر:', quotationId)
    alert('سيتم تحميل عرض السعر كملف PDF')
  }

  const handleSaveQuotation = (quotationData: any) => {
    if (editingQuotation) {
      // تحديث عرض موجود
      setQuotations(quotations.map(q =>
        q.id === editingQuotation.id ? quotationData : q
      ))
      alert('تم تحديث العرض بنجاح')
    } else {
      // إضافة عرض جديد
      setQuotations([quotationData, ...quotations])
      alert('تم إنشاء العرض بنجاح')
    }
    setIsFormOpen(false)
    setEditingQuotation(null)
  }

  const handleDuplicateQuotation = (quotation: any) => {
    `,
      status: 'draft',
      created_at: new Date().toISOString().split('T')[0]
    }
    setQuotations([newQuotation, ...quotations])
    alert('تم نسخ العرض بنجاح')
  }

  // حساب الإحصائيات
  

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">عروض الأسعار</h1>
            <p className="text-gray-600">إدارة عروض الأسعار للعملاء</p>
          </div>
          {canCreate && (
            <Button onClick={handleCreateQuotation}>
              <Plus className="h-4 w-4 mr-2" />
              عرض سعر جديد
            </Button>
          )}
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي العروض</CardTitle>
              <FileText className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">مسودات</CardTitle>
              <Clock className="h-4 w-4 text-gray-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.draft}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">مرسلة</CardTitle>
              <Send className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.sent}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">مقبولة</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.accepted}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي القيمة</CardTitle>
              <FileText className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">{formatCurrency(stats.totalValue)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">قيمة المقبولة</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-lg font-bold">{formatCurrency(stats.acceptedValue)}</div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="البحث في العروض..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="تصفية حسب الحالة" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">جميع الحالات</SelectItem>
                    <SelectItem value="draft">مسودة</SelectItem>
                    <SelectItem value="sent">مرسل</SelectItem>
                    <SelectItem value="accepted">مقبول</SelectItem>
                    <SelectItem value="rejected">مرفوض</SelectItem>
                    <SelectItem value="expired">منتهي الصلاحية</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex space-x-2 space-x-reverse">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4 mr-2" />
                  مرشحات متقدمة
                </Button>
                <ExportData
                  data={filteredQuotations}
                  filename="quotations"
                  type="quotations"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quotations Table or Empty State */}
        {loading ? (
          <Card>
            <CardContent className="py-12 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">جاري تحميل عروض الأسعار...</p>
            </CardContent>
          </Card>
        ) : filteredQuotations.length === 0 ? (
          <EmptyQuotations />
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>قائمة عروض الأسعار</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>رقم العرض</TableHead>
                    <TableHead>العميل</TableHead>
                    <TableHead>التاريخ</TableHead>
                    <TableHead>صالح حتى</TableHead>
                    <TableHead>المبلغ</TableHead>
                    <TableHead>الحالة</TableHead>
                    <TableHead>الإجراءات</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredQuotations.map((quotation) => {
                    const statusInfo = getStatusInfo(quotation.status)
                    const StatusIcon = statusInfo.icon

                    return (
                      <TableRow key={quotation.id}>
                        <TableCell className="font-medium">
                          <button
                            onClick={() => handleViewQuotation(quotation)}
                            className="text-blue-600 hover:text-blue-800 hover:underline font-medium cursor-pointer transition-colors duration-200"
                            title="انقر لعرض تفاصيل عرض السعر"
                          >
                            {quotation.quotation_number}
                          </button>
                        </TableCell>

                        <TableCell>
                          <div>
                            <div className="font-medium">{quotation.customer_name}</div>
                            <div className="text-xs text-gray-500">{quotation.customer_phone}</div>
                          </div>
                        </TableCell>

                        <TableCell>
                          <div>
                            <div>{formatDate(quotation.created_at)}</div>
                            <div className="text-xs text-gray-500">{quotation.created_by}</div>
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className={`text-sm ${
                            new Date(quotation.valid_until) < new Date() ? 'text-red-600' : 'text-gray-900'
                          }`}>
                            {formatDate(quotation.valid_until)}
                          </div>
                        </TableCell>

                        <TableCell>
                          <div className="text-right">
                            <div className="font-bold">{formatCurrency(quotation.total_amount)}</div>
                            <div className="text-xs text-gray-500">
                              {quotation.items?.length || 0} منتج
                            </div>
                          </div>
                        </TableCell>

                        <TableCell>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusInfo.color}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {statusInfo.text}
                          </span>
                        </TableCell>

                        <TableCell className="text-center">
                          <span className="text-sm text-gray-500">انقر على رقم العرض للإجراءات</span>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        )}

        {/* Quotation Form */}
        <QuotationForm
          isOpen={isFormOpen}
          onClose={() => {
            setIsFormOpen(false)
            setEditingQuotation(null)
          }}
          quotation={editingQuotation}
          onSave={handleSaveQuotation}
        />

        {/* Quotation View Modal */}
        <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>تفاصيل عرض السعر {viewingQuotation?.quotation_number}</span>
                <div className="flex flex-wrap gap-2">
                  {viewingQuotation && (
                    <>
                      {/* طباعة */}
                      <QuotationPrint quotation={viewingQuotation} />

                      {/* تحميل PDF */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadQuotation(viewingQuotation.id)}
                        className="text-purple-600 hover:text-purple-700"
                      >
                        <Download className="h-4 w-4 mr-2" />
                        تحميل PDF
                      </Button>

                      {/* إرسال للعميل */}
                      {viewingQuotation.status === 'draft' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            handleSendQuotation(viewingQuotation.id)
                            setViewingQuotation({...viewingQuotation, status: 'sent'})
                          }}
                          className="text-green-600 hover:text-green-700"
                        >
                          <Send className="h-4 w-4 mr-2" />
                          إرسال
                        </Button>
                      )}

                      {/* إرسال بالبريد الإلكتروني */}
                      {viewingQuotation.customer_email && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const subject = `عرض سعر رقم ${viewingQuotation.quotation_number}`
                            const body = `السيد/ة ${viewingQuotation.customer_name} المحترم/ة،%0A%0A` +
                                       `نرسل لكم عرض السعر التالي:%0A%0A` +
                                       `رقم العرض: ${viewingQuotation.quotation_number}%0A` +
                                       `تاريخ الإنشاء: ${formatDate(viewingQuotation.created_at)}%0A` +
                                       `صالح حتى: ${formatDate(viewingQuotation.valid_until)}%0A` +
                                       `المبلغ الإجمالي: ${formatCurrency(viewingQuotation.total_amount)}%0A%0A` +
                                       `نتطلع لموافقتكم.%0A%0A` +
                                       `مع أطيب التحيات`

                            const mailtoUrl = `mailto:${viewingQuotation.customer_email}?subject=${subject}&body=${body}`
                            window.open(mailtoUrl, '_blank')
                          }}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Mail className="h-4 w-4 mr-2" />
                          إيميل
                        </Button>
                      )}

                      {/* إرسال بالواتساب */}
                      {viewingQuotation.customer_phone && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            const message = `عرض سعر رقم ${viewingQuotation.quotation_number}%0A` +
                                          `العميل: ${viewingQuotation.customer_name}%0A` +
                                          `المبلغ الإجمالي: ${formatCurrency(viewingQuotation.total_amount)}%0A` +
                                          `صالح حتى: ${formatDate(viewingQuotation.valid_until)}`

                            const whatsappUrl = `https://wa.me/${viewingQuotation.customer_phone?.replace(/[^0-9]/g, '')}?text=${message}`
                            window.open(whatsappUrl, '_blank')
                          }}
                          className="text-green-600 hover:text-green-700"
                        >
                          <MessageCircle className="h-4 w-4 mr-2" />
                          واتساب
                        </Button>
                      )}

                      {/* تعديل */}
                      {canEdit && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setEditingQuotation(viewingQuotation)
                            setIsViewModalOpen(false)
                            setIsFormOpen(true)
                          }}
                          className="text-orange-600 hover:text-orange-700"
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          تعديل
                        </Button>
                      )}

                      {/* حذف */}
                      {canDelete && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            if (confirm('هل أنت متأكد من حذف هذا العرض؟')) {
                              handleDeleteQuotation(viewingQuotation.id)
                              setIsViewModalOpen(false)
                            }
                          }}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          حذف
                        </Button>
                      )}

                      {/* نسخ العرض */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleDuplicateQuotation(viewingQuotation)
                          setIsViewModalOpen(false)
                        }}
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        نسخ العرض
                      </Button>

                      {/* مشاركة العرض */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          `,
                            text: `عرض سعر رقم ${viewingQuotation.quotation_number} - العميل: ${viewingQuotation.customer_name} - المبلغ: ${formatCurrency(viewingQuotation.total_amount)}`,
                            url: window.location.href
                          }

                          if (navigator.share) {
                            navigator.share(shareData)
                          } else {
                            navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`)
                            alert('تم نسخ تفاصيل العرض للحافظة')
                          }
                        }}
                        className="text-cyan-600 hover:text-cyan-700"
                      >
                        <Share2 className="h-4 w-4 mr-2" />
                        مشاركة
                      </Button>

                      {/* إضافة للمفضلة */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          alert('تم إضافة العرض للمفضلة')
                        }}
                        className="text-yellow-600 hover:text-yellow-700"
                      >
                        <Star className="h-4 w-4 mr-2" />
                        مفضلة
                      </Button>

                      {/* فاصل للتحويلات */}
                      <div className="w-full border-t my-2"></div>

                      {/* تحويل إلى طلب مبيعات */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleConvertToOrder(viewingQuotation)
                          setIsViewModalOpen(false)
                        }}
                        className="text-orange-600 hover:text-orange-700"
                      >
                        <ShoppingCart className="h-4 w-4 mr-2" />
                        تحويل لطلب مبيعات
                      </Button>

                      {/* تحويل إلى فاتورة */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleConvertToInvoice(viewingQuotation)
                          setIsViewModalOpen(false)
                        }}
                        className="text-emerald-600 hover:text-emerald-700"
                      >
                        <ArrowRight className="h-4 w-4 mr-2" />
                        تحويل لفاتورة
                      </Button>
                    </>
                  )}
                </div>
              </DialogTitle>
            </DialogHeader>

            {viewingQuotation && (
              <div className="space-y-6">
                {/* معلومات العرض الأساسية */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">معلومات العرض</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">رقم العرض:</span>
                        <span className="font-medium">{viewingQuotation.quotation_number}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">تاريخ الإنشاء:</span>
                        <span>{formatDate(viewingQuotation.created_at)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">صالح حتى:</span>
                        <span>{formatDate(viewingQuotation.valid_until)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">الحالة:</span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusInfo(viewingQuotation.status).color}`}>
                          {getStatusInfo(viewingQuotation.status).text}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">الفرع:</span>
                        <span>{viewingQuotation.branch_name || 'غير محدد'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">المسؤول:</span>
                        <span>{viewingQuotation.created_by || 'غير محدد'}</span>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">معلومات العميل</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">اسم العميل:</span>
                        <span className="font-medium">{viewingQuotation.customer_name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">رقم الهاتف:</span>
                        <span>{viewingQuotation.customer_phone}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">البريد الإلكتروني:</span>
                        <span>{viewingQuotation.customer_email || 'غير محدد'}</span>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* المنتجات */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">المنتجات</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>المنتج</TableHead>
                          <TableHead>الكمية</TableHead>
                          <TableHead>السعر</TableHead>
                          <TableHead>الإجمالي</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {viewingQuotation.items?.map((item: any, index: number) => (
                          <TableRow key={index}>
                            <TableCell className="font-medium">
                              <div>
                                <div>{item.product_name}</div>
                                {item.selected_components && item.selected_components.length > 0 && (
                                  <div className="text-xs text-gray-500 mt-1">
                                    {item.selected_components.map((comp: any, i: number) => (
                                      <div key={i}>+ {comp.name}</div>
                                    ))}
                                  </div>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>{item.quantity}</TableCell>
                            <TableCell>{formatCurrency(item.unit_price)}</TableCell>
                            <TableCell>{formatCurrency(item.total_price)}</TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </CardContent>
                </Card>

                {/* ملخص المبالغ */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">ملخص المبالغ</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-gray-600">المجموع الفرعي:</span>
                        <span>{formatCurrency(viewingQuotation.subtotal)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">الضريبة:</span>
                        <span>{formatCurrency(viewingQuotation.tax_amount)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">الخصم:</span>
                        <span>{formatCurrency(viewingQuotation.discount_amount)}</span>
                      </div>
                      <div className="border-t pt-3">
                        <div className="flex justify-between text-lg font-bold">
                          <span>المجموع الكلي:</span>
                          <span>{formatCurrency(viewingQuotation.total_amount)}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* ملاحظات */}
                {viewingQuotation.notes && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">ملاحظات</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700">{viewingQuotation.notes}</p>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          </DialogContent>
        </Dialog>
      </div>
    </Layout>
  )
}
