import { useState } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckCircle, 
  XCircle, 
  Trash2, 
  AlertTriangle,
  Database,
  User,
  Shield
} from 'lucide-react'

export default function ClearData() {
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [confirmed, setConfirmed] = useState(false)

  const clearData = async () => {
    if (!confirmed) {
      alert('يرجى تأكيد العملية أولاً')
      return
    }

    setLoading(true)
    setResult(null)
    
    try {
      const response = await fetch('/api/postgres/clear-data', {
        method: 'POST'
      })
      
      const data = await response.json()
      setResult(data)
      
    } catch (err) {
      setResult({
        success: false,
        message: 'خطأ في مسح البيانات'
      })
    } finally {
      setLoading(false)
    }
  }

  const tablesToClear = [
    { name: 'المنتجات', table: 'products', icon: '📦' },
    { name: 'العملاء', table: 'customers', icon: '👥' },
    { name: 'الموردين', table: 'suppliers', icon: '🚚' },
    { name: 'أوامر المبيعات', table: 'sales_orders', icon: '🛒' },
    { name: 'أوامر الشراء', table: 'purchase_orders', icon: '📋' },
    { name: 'المخزون', table: 'inventory', icon: '📊' },
    { name: 'حركات المخزون', table: 'stock_movements', icon: '🔄' },
    { name: 'طلبات الصيانة', table: 'maintenance_requests', icon: '🔧' },
    { name: 'المصروفات', table: 'expenses', icon: '💰' },
    { name: 'الأقساط', table: 'installments', icon: '📅' }
  ]

  const preservedData = [
    { name: 'المدير الأولي', icon: '👤', description: 'سيتم الاحتفاظ بحساب المدير' },
    { name: 'الصلاحيات', icon: '🔐', description: 'سيتم الاحتفاظ بجميع الصلاحيات' },
    { name: 'هيكل قاعدة البيانات', icon: '🏗️', description: 'سيتم الاحتفاظ بجميع الجداول' },
    { name: 'الفروع والمخازن', icon: '🏢', description: 'سيتم الاحتفاظ بالإعدادات الأساسية' }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-100 p-6">
      <div className="max-w-4xl mx-auto space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="bg-white p-4 rounded-full shadow-lg inline-block mb-4">
            <Trash2 className="h-12 w-12 text-red-600" />
          </div>
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            مسح البيانات الوهمية
          </h1>
          <p className="text-xl text-gray-600">
            مسح جميع البيانات التجريبية مع الاحتفاظ بالمدير والصلاحيات
          </p>
        </div>

        {/* Warning */}
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium text-red-800">⚠️ تحذير مهم</p>
              <p className="text-red-700">
                هذه العملية ستحذف جميع البيانات الوهمية والتجريبية من قاعدة البيانات.
                لا يمكن التراجع عن هذه العملية!
              </p>
            </div>
          </AlertDescription>
        </Alert>

        {/* Data to be cleared */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-red-700">
              <Trash2 className="h-6 w-6 mr-2" />
              البيانات التي سيتم مسحها
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              {tablesToClear.map((item, index) => (
                <div key={index} className="text-center p-3 bg-red-50 rounded-lg border border-red-200">
                  <div className="text-2xl mb-2">{item.icon}</div>
                  <p className="text-sm font-medium text-red-800">{item.name}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Data to be preserved */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-green-700">
              <Shield className="h-6 w-6 mr-2" />
              البيانات التي سيتم الاحتفاظ بها
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {preservedData.map((item, index) => (
                <div key={index} className="flex items-center p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-2xl mr-3">{item.icon}</div>
                  <div>
                    <p className="font-medium text-green-800">{item.name}</p>
                    <p className="text-sm text-green-600">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Confirmation and Action */}
        <Card className="bg-white shadow-xl">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl">تأكيد العملية</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {!result && !loading && (
              <div className="space-y-4">
                <div className="flex items-center justify-center space-x-4">
                  <input
                    type="checkbox"
                    id="confirm"
                    checked={confirmed}
                    onChange={(e) => setConfirmed(e.target.checked)}
                    className="w-4 h-4 text-red-600"
                  />
                  <label htmlFor="confirm" className="text-gray-700">
                    أؤكد أنني أريد مسح جميع البيانات الوهمية (لا يمكن التراجع)
                  </label>
                </div>
                
                <div className="text-center">
                  <Button 
                    onClick={clearData}
                    disabled={!confirmed}
                    size="lg"
                    className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-8 py-3"
                  >
                    <Trash2 className="h-5 w-5 mr-2" />
                    مسح البيانات الوهمية
                  </Button>
                </div>
              </div>
            )}

            {loading && (
              <div className="text-center space-y-4">
                <Database className="h-12 w-12 mx-auto text-red-600 animate-pulse" />
                <h3 className="text-lg font-semibold">جاري مسح البيانات...</h3>
                <p className="text-gray-600">يرجى الانتظار حتى اكتمال العملية</p>
              </div>
            )}

            {result && (
              <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                {result.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription>
                  <div className="space-y-4">
                    <p className="font-medium text-lg">{result.message}</p>
                    
                    {result.summary && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-blue-600">{result.summary.totalTables}</p>
                          <p className="text-gray-600">إجمالي الجداول</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-green-600">{result.summary.successful}</p>
                          <p className="text-gray-600">تم مسحها</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-red-600">{result.summary.failed}</p>
                          <p className="text-gray-600">فشل</p>
                        </div>
                        <div className="text-center p-3 bg-white rounded">
                          <p className="text-2xl font-bold text-purple-600">0</p>
                          <p className="text-gray-600">بيانات متبقية</p>
                        </div>
                      </div>
                    )}

                    {result.recommendations && (
                      <div className="space-y-2">
                        <h4 className="font-semibold">النتائج:</h4>
                        <ul className="space-y-1">
                          {result.recommendations.map((rec: string, index: number) => (
                            <li key={index} className="text-sm">{rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className="flex gap-4 pt-4">
                      <Button 
                        onClick={() => window.location.href = '/dashboard'}
                        className="bg-blue-600 hover:bg-blue-700"
                      >
                        <User className="h-4 w-4 mr-2" />
                        لوحة التحكم
                      </Button>
                      <Button 
                        onClick={() => window.location.href = '/products'}
                        variant="outline"
                      >
                        إضافة منتجات
                      </Button>
                    </div>
                  </div>
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
