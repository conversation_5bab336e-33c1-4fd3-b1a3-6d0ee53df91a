# دليل الجداول الكامل لنظام إدارة الأعمال 📊

## 📋 قائمة الجداول الكاملة (23 جدول)

### 🏢 الجداول الأساسية (8 جداول)
1. **branches** - الفروع
2. **warehouses** - المخازن  
3. **cash_registers** - صناديق النقدية
4. **users** - المستخدمين
5. **customers** - العملاء
6. **suppliers** - الموردين
7. **products** - المنتجات
8. **inventory** - المخزون

### 💰 جداول المبيعات والمشتريات (4 جداول)
9. **sales_orders** - أوامر المبيعات
10. **sales_order_items** - تفاصيل أوامر المبيعات
11. **purchase_orders** - أوامر الشراء
12. **purchase_order_items** - تفاصيل أوامر الشراء

### 📦 جداول المخزون والمالية (5 جداول)
13. **inventory_movements** - حركات المخزون
14. **cash_transactions** - المعاملات النقدية
15. **expenses** - المصروفات
16. **installments** - الأقساط
17. **installment_payments** - دفعات الأقساط

### 🔧 جداول الصيانة (3 جداول)
18. **maintenance_requests** - طلبات الصيانة
19. **maintenance_required_parts** - قطع الغيار المطلوبة
20. **maintenance_used_parts** - قطع الغيار المستخدمة

### 📊 الأنواع المخصصة (ENUMs)
- **user_role**: admin, manager, employee, cashier
- **transaction_type**: sale, purchase, expense, payment, receipt
- **payment_method**: cash, card, bank_transfer, vodafone_cash, installment

---

## 🚀 كيفية إكمال الجداول

### الطريقة الأولى: من الواجهة (الأسهل) ⭐

1. **اذهب إلى**: `http://localhost:3000/database-status`
2. **ستظهر لك حالة كل جدول**:
   - ✅ أخضر = موجود
   - ❌ أحمر = مفقود
3. **انقر على زر "إعداد قاعدة البيانات"** (إذا ظهر)
4. **انتظر حتى يكتمل الإعداد**
5. **انقر "إعادة فحص"** للتأكد

### الطريقة الثانية: يدوياً

#### أ. باستخدام pgAdmin:
1. **افتح pgAdmin**
2. **اتصل بقاعدة البيانات** `Vero_ERP_ABA`
3. **افتح Query Tool**
4. **انسخ محتوى ملف** `database_setup_postgres.sql`
5. **الصق ونفذ الاستعلام**

#### ب. باستخدام سطر الأوامر:
```bash
psql -h localhost -U openpg -d Vero_ERP_ABA -f database_setup_postgres.sql
```

#### ج. باستخدام API:
```bash
curl -X POST http://localhost:3000/api/postgres/setup-database
```

---

## ✅ التحقق من اكتمال الإعداد

### 1. فحص سريع
زر: `http://localhost:3000/database-status`

### 2. فحص تفصيلي
زر: `http://localhost:3000/api/postgres/check-tables`

### 3. علامات النجاح
- ✅ جميع الجداول (23) باللون الأخضر
- ✅ رسالة "الإعداد مكتمل"
- ✅ لا توجد أخطاء في Console

---

## 🎯 الخطوات التالية بعد إكمال الجداول

### 1. إنشاء المدير الأولي
```bash
curl -X POST http://localhost:3000/api/postgres/create-admin \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "V@admin010",
    "fullName": "مدير النظام"
  }'
```

### 2. تسجيل الدخول
- **اذهب إلى**: `http://localhost:3000/login`
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: V@admin010

### 3. إضافة بيانات تجريبية
- إضافة منتجات
- إضافة عملاء
- إضافة موردين
- إنشاء أوامر مبيعات تجريبية

---

## 🔧 استكشاف الأخطاء

### خطأ: "relation already exists"
**الحل**: هذا طبيعي، يعني أن الجدول موجود مسبقاً

### خطأ: "permission denied"
**الحل**: تأكد من صلاحيات المستخدم `openpg`

### خطأ: "syntax error"
**الحل**: تأكد من نسخ ملف SQL بالكامل

### خطأ: "connection refused"
**الحل**: تأكد من تشغيل PostgreSQL

---

## 📈 إحصائيات متوقعة بعد الإعداد

- **إجمالي الجداول**: 23
- **الجداول الأساسية**: 8
- **جداول المبيعات**: 4  
- **جداول المالية**: 5
- **جداول الصيانة**: 3
- **الأنواع المخصصة**: 3

**🎉 بعد إكمال جميع الجداول، النظام سيكون جاهز للاستخدام الكامل!**
