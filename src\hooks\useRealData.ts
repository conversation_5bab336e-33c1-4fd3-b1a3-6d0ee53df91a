import { useState, useEffect } from 'react'

interface UseRealDataOptions {
  endpoint: string
  dependencies?: any[]
}

export function useRealData<T = any>({ endpoint, dependencies = [] }: UseRealDataOptions) {
  const [data, setData] = useState<T[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/postgres/data?type=${endpoint}`)
      const result = await response.json()
      
      if (result.success) {
        setData(result.data || [])
      } else {
        setError(result.message || 'فشل في جلب البيانات')
        setData([])
      }
    } catch (err) {
      setError('خطأ في الاتصال بالخادم')
      setData([])
      console.error(`خطأ في جلب ${endpoint}:`, err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, dependencies)

  return {
    data,
    loading,
    error,
    refetch: fetchData,
    isEmpty: !loading && data.length === 0
  }
}

// Hook مخصص للمبيعات
export function useSalesData() {
  return useRealData({ endpoint: 'sales-orders' })
}

// Hook مخصص للمشتريات
export function usePurchasesData() {
  return useRealData({ endpoint: 'purchase-orders' })
}

// Hook مخصص للمنتجات
export function useProductsData() {
  return useRealData({ endpoint: 'products' })
}

// Hook مخصص للعملاء
export function useCustomersData() {
  return useRealData({ endpoint: 'customers' })
}

// Hook مخصص للموردين
export function useSuppliersData() {
  return useRealData({ endpoint: 'suppliers' })
}

// Hook مخصص للمخزون
export function useInventoryData() {
  return useRealData({ endpoint: 'inventory' })
}

// Hook مخصص لحركات المخزون
export function useStockMovementsData() {
  return useRealData({ endpoint: 'stock-movements' })
}

// Hook مخصص لطلبات الصيانة
export function useMaintenanceData() {
  return useRealData({ endpoint: 'maintenance-requests' })
}

// مكون لعرض حالة فارغة
export function EmptyState({ 
  title, 
  description, 
  actionText, 
  onAction 
}: {
  title: string
  description: string
  actionText?: string
  onAction?: () => void
}) {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
        <svg className="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      <p className="text-gray-500 mb-6">{description}</p>
      {actionText && onAction && (
        <button
          onClick={onAction}
          className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
        >
          {actionText}
        </button>
      )}
    </div>
  )
}

// مكون لعرض حالة التحميل
export function LoadingState({ message = 'جاري التحميل...' }: { message?: string }) {
  return (
    <div className="text-center py-12">
      <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
      <p className="text-gray-600">{message}</p>
    </div>
  )
}

// مكون لعرض حالة الخطأ
export function ErrorState({ 
  message, 
  onRetry 
}: { 
  message: string
  onRetry?: () => void 
}) {
  return (
    <div className="text-center py-12">
      <div className="mx-auto w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mb-4">
        <svg className="w-12 h-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">حدث خطأ</h3>
      <p className="text-gray-500 mb-6">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 transition-colors"
        >
          إعادة المحاولة
        </button>
      )}
    </div>
  )
}
