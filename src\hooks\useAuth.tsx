'use client'

import React, { useState, useEffect, createContext, useContext } from 'react'

interface AuthUser {
  id: number
  email: string
  username: string
  full_name: string
  role: 'admin' | 'manager' | 'employee' | 'cashier'
  is_active: boolean
  branch_id?: number
  warehouse_id?: number
  pos_id?: number
}

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<void>
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  // تحميل بيانات المستخدم من localStorage
  const loadUserFromStorage = (): AuthUser | null => {
    try {
      const savedUser = localStorage.getItem('user')
      if (savedUser) {
        return JSON.parse(savedUser)
      }
      return null
    } catch (error) {
      console.error('Error loading user from storage:', error)
      return null
    }
  }

  const refreshUser = async () => {
    const savedUser = loadUserFromStorage()
    setUser(savedUser)
  }

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      const response = await fetch('/api/postgres/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      })

      const result = await response.json()

      if (result.success) {
        const userData = result.user
        localStorage.setItem('user', JSON.stringify(userData))
        setUser(userData)
      } else {
        throw new Error(result.message || 'فشل في تسجيل الدخول')
      }
    } catch (error) {
      console.error('Sign in error:', error)
      throw error
    } finally {
      setLoading(false)
    }
  }

  const signOut = async () => {
    try {
      localStorage.removeItem('user')
      setUser(null)
    } catch (error) {
      console.error('Sign out error:', error)
      throw error
    }
  }

  useEffect(() => {
    // تحميل المستخدم من localStorage عند بدء التطبيق
    const initializeAuth = () => {
      try {
        const savedUser = loadUserFromStorage()
        setUser(savedUser)
      } catch (error) {
        console.error('Error initializing auth:', error)
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()
  }, [])

  const value = {
    user,
    loading,
    signIn,
    signOut,
    refreshUser,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
