"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/postgres/login";
exports.ids = ["pages/api/postgres/login"];
exports.modules = {

/***/ "bcryptjs":
/*!***************************!*\
  !*** external "bcryptjs" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("bcryptjs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "pg":
/*!*********************!*\
  !*** external "pg" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("pg");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Clogin.ts&middlewareConfigBase64=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Clogin.ts&middlewareConfigBase64=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_postgres_login_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\postgres\\login.ts */ \"(api)/./src/pages/api/postgres/login.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_postgres_login_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/postgres/login\",\n        pathname: \"/api/postgres/login\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_postgres_login_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Clogin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERMISSIONS: () => (/* binding */ PERMISSIONS),\n/* harmony export */   PERMISSIONS_ORGANIZED: () => (/* binding */ PERMISSIONS_ORGANIZED),\n/* harmony export */   ROLE_PERMISSIONS: () => (/* binding */ ROLE_PERMISSIONS),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   hasAllPermissions: () => (/* binding */ hasAllPermissions),\n/* harmony export */   hasAnyPermission: () => (/* binding */ hasAnyPermission),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut)\n/* harmony export */ });\n// Mock user type for demo\nconst PERMISSIONS = {\n    // User Management\n    USERS_VIEW: \"users:view\",\n    USERS_CREATE: \"users:create\",\n    USERS_EDIT: \"users:edit\",\n    USERS_DELETE: \"users:delete\",\n    // Branch Management\n    BRANCHES_VIEW: \"branches:view\",\n    BRANCHES_CREATE: \"branches:create\",\n    BRANCHES_EDIT: \"branches:edit\",\n    BRANCHES_DELETE: \"branches:delete\",\n    // Warehouse Management\n    WAREHOUSES_VIEW: \"warehouses:view\",\n    WAREHOUSES_CREATE: \"warehouses:create\",\n    WAREHOUSES_EDIT: \"warehouses:edit\",\n    WAREHOUSES_DELETE: \"warehouses:delete\",\n    // Product Management\n    PRODUCTS_VIEW: \"products:view\",\n    PRODUCTS_CREATE: \"products:create\",\n    PRODUCTS_EDIT: \"products:edit\",\n    PRODUCTS_DELETE: \"products:delete\",\n    // Sales\n    SALES_VIEW: \"sales:view\",\n    SALES_CREATE: \"sales:create\",\n    SALES_EDIT: \"sales:edit\",\n    SALES_DELETE: \"sales:delete\",\n    // Purchases\n    PURCHASES_VIEW: \"purchases:view\",\n    PURCHASES_CREATE: \"purchases:create\",\n    PURCHASES_EDIT: \"purchases:edit\",\n    PURCHASES_DELETE: \"purchases:delete\",\n    PURCHASES_APPROVE: \"purchases:approve\",\n    PURCHASES_RECEIVE: \"purchases:receive\",\n    PURCHASES_PAY: \"purchases:pay\",\n    PURCHASES_CANCEL: \"purchases:cancel\",\n    PURCHASES_PROCESS: \"purchases:process\",\n    // POS\n    POS_ACCESS: \"pos:access\",\n    POS_CLOSE_DAY: \"pos:close_day\",\n    // Cash Registers\n    CASH_REGISTERS_VIEW: \"cash_registers:view\",\n    CASH_REGISTERS_CREATE: \"cash_registers:create\",\n    CASH_REGISTERS_EDIT: \"cash_registers:edit\",\n    CASH_REGISTERS_DELETE: \"cash_registers:delete\",\n    // Accounting\n    ACCOUNTING_VIEW: \"accounting:view\",\n    ACCOUNTING_EDIT: \"accounting:edit\",\n    ACCOUNTING_MANAGE: \"accounting:manage\",\n    ACCOUNTING_CREATE: \"accounting:create\",\n    ACCOUNTING_DELETE: \"accounting:delete\",\n    // Reports\n    REPORTS_VIEW: \"reports:view\",\n    REPORTS_EXPORT: \"reports:export\"\n};\n// Organized permissions for easier use\nconst PERMISSIONS_ORGANIZED = {\n    SALES: {\n        VIEW: PERMISSIONS.SALES_VIEW,\n        CREATE: PERMISSIONS.SALES_CREATE,\n        EDIT: PERMISSIONS.SALES_EDIT,\n        DELETE: PERMISSIONS.SALES_DELETE\n    },\n    PURCHASES: {\n        VIEW: PERMISSIONS.PURCHASES_VIEW,\n        CREATE: PERMISSIONS.PURCHASES_CREATE,\n        EDIT: PERMISSIONS.PURCHASES_EDIT,\n        DELETE: PERMISSIONS.PURCHASES_DELETE,\n        APPROVE: PERMISSIONS.PURCHASES_APPROVE,\n        RECEIVE: PERMISSIONS.PURCHASES_RECEIVE,\n        PAY: PERMISSIONS.PURCHASES_PAY,\n        CANCEL: PERMISSIONS.PURCHASES_CANCEL,\n        PROCESS: PERMISSIONS.PURCHASES_PROCESS\n    }\n};\nconst ROLE_PERMISSIONS = {\n    admin: [\n        // جميع الصلاحيات للمدير\n        ...Object.values(PERMISSIONS)\n    ],\n    manager: [\n        PERMISSIONS.USERS_VIEW,\n        PERMISSIONS.BRANCHES_VIEW,\n        PERMISSIONS.BRANCHES_CREATE,\n        PERMISSIONS.BRANCHES_EDIT,\n        PERMISSIONS.WAREHOUSES_VIEW,\n        PERMISSIONS.WAREHOUSES_CREATE,\n        PERMISSIONS.WAREHOUSES_EDIT,\n        PERMISSIONS.CASH_REGISTERS_VIEW,\n        PERMISSIONS.CASH_REGISTERS_CREATE,\n        PERMISSIONS.CASH_REGISTERS_EDIT,\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.PRODUCTS_CREATE,\n        PERMISSIONS.PRODUCTS_EDIT,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.SALES_EDIT,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.PURCHASES_EDIT,\n        PERMISSIONS.PURCHASES_APPROVE,\n        PERMISSIONS.PURCHASES_RECEIVE,\n        PERMISSIONS.PURCHASES_PAY,\n        PERMISSIONS.PURCHASES_CANCEL,\n        PERMISSIONS.PURCHASES_PROCESS,\n        PERMISSIONS.ACCOUNTING_VIEW,\n        PERMISSIONS.ACCOUNTING_EDIT,\n        PERMISSIONS.ACCOUNTING_MANAGE,\n        PERMISSIONS.ACCOUNTING_CREATE,\n        PERMISSIONS.ACCOUNTING_DELETE,\n        PERMISSIONS.REPORTS_VIEW,\n        PERMISSIONS.REPORTS_EXPORT\n    ],\n    employee: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.PURCHASES_VIEW,\n        PERMISSIONS.PURCHASES_CREATE,\n        PERMISSIONS.REPORTS_VIEW\n    ],\n    cashier: [\n        PERMISSIONS.PRODUCTS_VIEW,\n        PERMISSIONS.SALES_VIEW,\n        PERMISSIONS.SALES_CREATE,\n        PERMISSIONS.POS_ACCESS,\n        PERMISSIONS.CASH_REGISTERS_VIEW\n    ]\n};\nasync function signIn(email, password) {\n    // Mock authentication for demo\n    await new Promise((resolve)=>setTimeout(resolve, 1000));\n    if (email && password) {\n        return {\n            user: {\n                id: \"1\",\n                email\n            }\n        };\n    }\n    throw new Error(\"Invalid credentials\");\n}\nasync function signOut() {\n    // Mock sign out\n    await new Promise((resolve)=>setTimeout(resolve, 500));\n}\nasync function getCurrentUser() {\n    // Mock user for demo - تأكد من أن جميع الصلاحيات متضمنة\n    const adminPermissions = [\n        // User Management\n        \"users:view\",\n        \"users:create\",\n        \"users:edit\",\n        \"users:delete\",\n        // Branch Management\n        \"branches:view\",\n        \"branches:create\",\n        \"branches:edit\",\n        \"branches:delete\",\n        // Warehouse Management\n        \"warehouses:view\",\n        \"warehouses:create\",\n        \"warehouses:edit\",\n        \"warehouses:delete\",\n        // Product Management\n        \"products:view\",\n        \"products:create\",\n        \"products:edit\",\n        \"products:delete\",\n        // Sales\n        \"sales:view\",\n        \"sales:create\",\n        \"sales:edit\",\n        \"sales:delete\",\n        // Purchases\n        \"purchases:view\",\n        \"purchases:create\",\n        \"purchases:edit\",\n        \"purchases:delete\",\n        \"purchases:approve\",\n        \"purchases:receive\",\n        \"purchases:pay\",\n        \"purchases:cancel\",\n        \"purchases:process\",\n        // POS\n        \"pos:access\",\n        \"pos:close_day\",\n        // Cash Registers\n        \"cash_registers:view\",\n        \"cash_registers:create\",\n        \"cash_registers:edit\",\n        \"cash_registers:delete\",\n        // Accounting\n        \"accounting:view\",\n        \"accounting:edit\",\n        \"accounting:manage\",\n        \"accounting:create\",\n        \"accounting:delete\",\n        // Reports\n        \"reports:view\",\n        \"reports:export\"\n    ];\n    return {\n        id: \"1\",\n        email: \"<EMAIL>\",\n        username: \"admin\",\n        full_name: \"مدير النظام\",\n        role: \"admin\",\n        branch_id: \"1\",\n        warehouse_id: \"1\",\n        pos_id: \"1\",\n        is_active: true,\n        created_at: \"2024-01-01\",\n        updated_at: \"2024-01-01\",\n        permissions: adminPermissions\n    };\n}\nfunction hasPermission(user, permission) {\n    if (!user) return false;\n    return user.permissions.includes(permission);\n}\nfunction hasAnyPermission(user, permissions) {\n    if (!user) return false;\n    return permissions.some((permission)=>user.permissions.includes(permission));\n}\nfunction hasAllPermissions(user, permissions) {\n    if (!user) return false;\n    return permissions.every((permission)=>user.permissions.includes(permission));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(api)/./src/lib/postgres.ts":
/*!*****************************!*\
  !*** ./src/lib/postgres.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   checkTables: () => (/* binding */ checkTables),\n/* harmony export */   createUser: () => (/* binding */ createUser),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   findUser: () => (/* binding */ findUser),\n/* harmony export */   getCustomers: () => (/* binding */ getCustomers),\n/* harmony export */   getInventory: () => (/* binding */ getInventory),\n/* harmony export */   getMaintenanceRequests: () => (/* binding */ getMaintenanceRequests),\n/* harmony export */   getProducts: () => (/* binding */ getProducts),\n/* harmony export */   getPurchaseOrders: () => (/* binding */ getPurchaseOrders),\n/* harmony export */   getSalesOrders: () => (/* binding */ getSalesOrders),\n/* harmony export */   getStockMovements: () => (/* binding */ getStockMovements),\n/* harmony export */   getSuppliers: () => (/* binding */ getSuppliers),\n/* harmony export */   query: () => (/* binding */ query),\n/* harmony export */   testConnection: () => (/* binding */ testConnection)\n/* harmony export */ });\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! pg */ \"pg\");\n/* harmony import */ var pg__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(pg__WEBPACK_IMPORTED_MODULE_0__);\n\n// إعداد الاتصال بقاعدة البيانات PostgreSQL\nconst pool = new pg__WEBPACK_IMPORTED_MODULE_0__.Pool({\n    host: process.env.DB_HOST || \"localhost\",\n    port: parseInt(process.env.DB_PORT || \"5432\"),\n    database: process.env.DB_NAME || \"Vero_ERP_ABA\",\n    user: process.env.DB_USER || \"openpg\",\n    password: process.env.DB_PASSWORD || \"V@admin010\",\n    ssl:  false ? 0 : false,\n    max: 20,\n    idleTimeoutMillis: 30000,\n    connectionTimeoutMillis: 2000\n});\n// دالة للتحقق من الاتصال\nconst testConnection = async ()=>{\n    try {\n        const client = await pool.connect();\n        const result = await client.query(\"SELECT NOW()\");\n        client.release();\n        return {\n            success: true,\n            time: result.rows[0].now\n        };\n    } catch (error) {\n        console.error(\"خطأ في الاتصال بقاعدة البيانات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة لتنفيذ استعلام\nconst query = async (text, params)=>{\n    try {\n        const result = await pool.query(text, params);\n        return {\n            success: true,\n            data: result.rows,\n            rowCount: result.rowCount\n        };\n    } catch (error) {\n        console.error(\"خطأ في تنفيذ الاستعلام:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للتحقق من وجود الجداول\nconst checkTables = async ()=>{\n    try {\n        const result = await query(`\n      SELECT table_name\n      FROM information_schema.tables\n      WHERE table_schema = 'public'\n      AND table_name IN ('branches', 'users', 'products', 'warehouses')\n    `);\n        const tables = result.data?.map((row)=>row.table_name) || [];\n        return {\n            success: true,\n            tables,\n            hasBranches: tables.includes(\"branches\"),\n            hasUsers: tables.includes(\"users\"),\n            hasProducts: tables.includes(\"products\"),\n            hasWarehouses: tables.includes(\"warehouses\"),\n            needsSetup: !tables.includes(\"branches\")\n        };\n    } catch (error) {\n        console.error(\"خطأ في فحص الجداول:\", error);\n        return {\n            success: false,\n            error,\n            needsSetup: true\n        };\n    }\n};\n// دالة لإنشاء مستخدم جديد\nconst createUser = async (userData)=>{\n    try {\n        const result = await query(`\n      INSERT INTO users (email, username, full_name, password_hash, role, is_active, created_at)\n      VALUES ($1, $2, $3, $4, $5, true, NOW())\n      RETURNING id, email, username, full_name, role\n    `, [\n            userData.email,\n            userData.username,\n            userData.full_name,\n            userData.password_hash,\n            userData.role || \"admin\"\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0]\n        };\n    } catch (error) {\n        console.error(\"خطأ في إنشاء المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// دالة للبحث عن مستخدم\nconst findUser = async (email)=>{\n    try {\n        const result = await query(`\n      SELECT id, email, username, full_name, role, password_hash, is_active\n      FROM users\n      WHERE email = $1 AND is_active = true\n    `, [\n            email\n        ]);\n        return {\n            success: true,\n            user: result.data?.[0] || null\n        };\n    } catch (error) {\n        console.error(\"خطأ في البحث عن المستخدم:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المنتجات متوافقة مع الكود\nconst getProducts = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        p.*,\n        i.total_stock,\n        i.available_stock,\n        i.reserved_stock\n      FROM products p\n      LEFT JOIN inventory i ON p.id = i.product_id\n      WHERE p.is_active = true\n      ORDER BY p.name\n    `);\n        return {\n            success: true,\n            data: result.data?.map((product)=>({\n                    ...product,\n                    current_stock: product.total_stock || 0,\n                    price: product.unit_price,\n                    cost: product.cost_price\n                })) || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المنتجات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات المخزون متوافقة مع الكود\nconst getInventory = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        i.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        p.category as product_category,\n        w.name as warehouse_name\n      FROM inventory i\n      JOIN products p ON i.product_id = p.id\n      JOIN warehouses w ON i.warehouse_id = w.id\n      ORDER BY i.updated_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات العملاء\nconst getCustomers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM customers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب العملاء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات الموردين\nconst getSuppliers = async ()=>{\n    try {\n        const result = await query(`\n      SELECT * FROM suppliers\n      WHERE is_active = true\n      ORDER BY name\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب الموردين:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات أوامر المبيعات\nconst getSalesOrders = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        so.*,\n        c.name as customer_name,\n        b.name as branch_name,\n        u.full_name as created_by_name\n      FROM sales_orders so\n      LEFT JOIN customers c ON so.customer_id = c.id\n      LEFT JOIN branches b ON so.branch_id = b.id\n      LEFT JOIN users u ON so.created_by = u.id\n      ORDER BY so.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب أوامر المبيعات:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات أوامر الشراء\nconst getPurchaseOrders = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        po.*,\n        s.name as supplier_name,\n        b.name as branch_name,\n        w.name as warehouse_name,\n        u.full_name as created_by_name\n      FROM purchase_orders po\n      LEFT JOIN suppliers s ON po.supplier_id = s.id\n      LEFT JOIN branches b ON po.branch_id = b.id\n      LEFT JOIN warehouses w ON po.warehouse_id = w.id\n      LEFT JOIN users u ON po.created_by = u.id\n      ORDER BY po.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب أوامر الشراء:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات حركات المخزون\nconst getStockMovements = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        sm.*,\n        p.name as product_name,\n        p.sku as product_sku,\n        w.name as warehouse_name,\n        u.full_name as created_by_name\n      FROM stock_movements sm\n      JOIN products p ON sm.product_id = p.id\n      JOIN warehouses w ON sm.warehouse_id = w.id\n      LEFT JOIN users u ON sm.created_by = u.id\n      ORDER BY sm.created_at DESC\n      LIMIT 100\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب حركات المخزون:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n// خدمات طلبات الصيانة\nconst getMaintenanceRequests = async ()=>{\n    try {\n        const result = await query(`\n      SELECT\n        mr.*,\n        t.full_name as technician_name,\n        at.full_name as assigned_technician_name,\n        b.name as branch_name,\n        w.name as warehouse_name\n      FROM maintenance_requests mr\n      LEFT JOIN users t ON mr.technician_id = t.id\n      LEFT JOIN users at ON mr.assigned_technician = at.id\n      LEFT JOIN branches b ON mr.branch_id = b.id\n      LEFT JOIN warehouses w ON mr.warehouse_id = w.id\n      ORDER BY mr.created_at DESC\n    `);\n        return {\n            success: true,\n            data: result.data || []\n        };\n    } catch (error) {\n        console.error(\"خطأ في جلب طلبات الصيانة:\", error);\n        return {\n            success: false,\n            error\n        };\n    }\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pool);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwaSkvLi9zcmMvbGliL3Bvc3RncmVzLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBeUI7QUFFekIsMkNBQTJDO0FBQzNDLE1BQU1DLE9BQU8sSUFBSUQsb0NBQUlBLENBQUM7SUFDcEJFLE1BQU1DLFFBQVFDLEdBQUcsQ0FBQ0MsT0FBTyxJQUFJO0lBQzdCQyxNQUFNQyxTQUFTSixRQUFRQyxHQUFHLENBQUNJLE9BQU8sSUFBSTtJQUN0Q0MsVUFBVU4sUUFBUUMsR0FBRyxDQUFDTSxPQUFPLElBQUk7SUFDakNDLE1BQU1SLFFBQVFDLEdBQUcsQ0FBQ1EsT0FBTyxJQUFJO0lBQzdCQyxVQUFVVixRQUFRQyxHQUFHLENBQUNVLFdBQVcsSUFBSTtJQUNyQ0MsS0FBS1osTUFBeUIsR0FBZSxDQUE2QixHQUFHO0lBQzdFYyxLQUFLO0lBQ0xDLG1CQUFtQjtJQUNuQkMseUJBQXlCO0FBQzNCO0FBRUEseUJBQXlCO0FBQ2xCLE1BQU1DLGlCQUFpQjtJQUM1QixJQUFJO1FBQ0YsTUFBTUMsU0FBUyxNQUFNcEIsS0FBS3FCLE9BQU87UUFDakMsTUFBTUMsU0FBUyxNQUFNRixPQUFPRyxLQUFLLENBQUM7UUFDbENILE9BQU9JLE9BQU87UUFDZCxPQUFPO1lBQUVDLFNBQVM7WUFBTUMsTUFBTUosT0FBT0ssSUFBSSxDQUFDLEVBQUUsQ0FBQ0MsR0FBRztRQUFDO0lBQ25ELEVBQUUsT0FBT0MsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsbUNBQW1DQTtRQUNqRCxPQUFPO1lBQUVKLFNBQVM7WUFBT0k7UUFBTTtJQUNqQztBQUNGLEVBQUM7QUFFRCxzQkFBc0I7QUFDZixNQUFNTixRQUFRLE9BQU9RLE1BQWNDO0lBQ3hDLElBQUk7UUFDRixNQUFNVixTQUFTLE1BQU10QixLQUFLdUIsS0FBSyxDQUFDUSxNQUFNQztRQUN0QyxPQUFPO1lBQUVQLFNBQVM7WUFBTVEsTUFBTVgsT0FBT0ssSUFBSTtZQUFFTyxVQUFVWixPQUFPWSxRQUFRO1FBQUM7SUFDdkUsRUFBRSxPQUFPTCxPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywyQkFBMkJBO1FBQ3pDLE9BQU87WUFBRUosU0FBUztZQUFPSTtRQUFNO0lBQ2pDO0FBQ0YsRUFBQztBQUVELDhCQUE4QjtBQUN2QixNQUFNTSxjQUFjO0lBQ3pCLElBQUk7UUFDRixNQUFNYixTQUFTLE1BQU1DLE1BQU0sQ0FBQzs7Ozs7SUFLNUIsQ0FBQztRQUVELE1BQU1hLFNBQVNkLE9BQU9XLElBQUksRUFBRUksSUFBSUMsQ0FBQUEsTUFBT0EsSUFBSUMsVUFBVSxLQUFLLEVBQUU7UUFFNUQsT0FBTztZQUNMZCxTQUFTO1lBQ1RXO1lBQ0FJLGFBQWFKLE9BQU9LLFFBQVEsQ0FBQztZQUM3QkMsVUFBVU4sT0FBT0ssUUFBUSxDQUFDO1lBQzFCRSxhQUFhUCxPQUFPSyxRQUFRLENBQUM7WUFDN0JHLGVBQWVSLE9BQU9LLFFBQVEsQ0FBQztZQUMvQkksWUFBWSxDQUFDVCxPQUFPSyxRQUFRLENBQUM7UUFDL0I7SUFDRixFQUFFLE9BQU9aLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUF1QkE7UUFDckMsT0FBTztZQUFFSixTQUFTO1lBQU9JO1lBQU9nQixZQUFZO1FBQUs7SUFDbkQ7QUFDRixFQUFDO0FBRUQsMEJBQTBCO0FBQ25CLE1BQU1DLGFBQWEsT0FBT0M7SUFPL0IsSUFBSTtRQUNGLE1BQU16QixTQUFTLE1BQU1DLE1BQU0sQ0FBQzs7OztJQUk1QixDQUFDLEVBQUU7WUFDRHdCLFNBQVNDLEtBQUs7WUFDZEQsU0FBU0UsUUFBUTtZQUNqQkYsU0FBU0csU0FBUztZQUNsQkgsU0FBU0ksYUFBYTtZQUN0QkosU0FBU0ssSUFBSSxJQUFJO1NBQ2xCO1FBRUQsT0FBTztZQUFFM0IsU0FBUztZQUFNZixNQUFNWSxPQUFPVyxJQUFJLEVBQUUsQ0FBQyxFQUFFO1FBQUM7SUFDakQsRUFBRSxPQUFPSixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQywwQkFBMEJBO1FBQ3hDLE9BQU87WUFBRUosU0FBUztZQUFPSTtRQUFNO0lBQ2pDO0FBQ0YsRUFBQztBQUVELHVCQUF1QjtBQUNoQixNQUFNd0IsV0FBVyxPQUFPTDtJQUM3QixJQUFJO1FBQ0YsTUFBTTFCLFNBQVMsTUFBTUMsTUFBTSxDQUFDOzs7O0lBSTVCLENBQUMsRUFBRTtZQUFDeUI7U0FBTTtRQUVWLE9BQU87WUFBRXZCLFNBQVM7WUFBTWYsTUFBTVksT0FBT1csSUFBSSxFQUFFLENBQUMsRUFBRSxJQUFJO1FBQUs7SUFDekQsRUFBRSxPQUFPSixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyw2QkFBNkJBO1FBQzNDLE9BQU87WUFBRUosU0FBUztZQUFPSTtRQUFNO0lBQ2pDO0FBQ0YsRUFBQztBQUVELGtDQUFrQztBQUMzQixNQUFNeUIsY0FBYztJQUN6QixJQUFJO1FBQ0YsTUFBTWhDLFNBQVMsTUFBTUMsTUFBTSxDQUFDOzs7Ozs7Ozs7O0lBVTVCLENBQUM7UUFFRCxPQUFPO1lBQ0xFLFNBQVM7WUFDVFEsTUFBTVgsT0FBT1csSUFBSSxFQUFFSSxJQUFJa0IsQ0FBQUEsVUFBWTtvQkFDakMsR0FBR0EsT0FBTztvQkFDVkMsZUFBZUQsUUFBUUUsV0FBVyxJQUFJO29CQUN0Q0MsT0FBT0gsUUFBUUksVUFBVTtvQkFDekJDLE1BQU1MLFFBQVFNLFVBQVU7Z0JBQzFCLE9BQU8sRUFBRTtRQUNYO0lBQ0YsRUFBRSxPQUFPaEMsT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsd0JBQXdCQTtRQUN0QyxPQUFPO1lBQUVKLFNBQVM7WUFBT0k7UUFBTTtJQUNqQztBQUNGLEVBQUM7QUFFRCxpQ0FBaUM7QUFDMUIsTUFBTWlDLGVBQWU7SUFDMUIsSUFBSTtRQUNGLE1BQU14QyxTQUFTLE1BQU1DLE1BQU0sQ0FBQzs7Ozs7Ozs7Ozs7SUFXNUIsQ0FBQztRQUVELE9BQU87WUFBRUUsU0FBUztZQUFNUSxNQUFNWCxPQUFPVyxJQUFJLElBQUksRUFBRTtRQUFDO0lBQ2xELEVBQUUsT0FBT0osT0FBTztRQUNkQyxRQUFRRCxLQUFLLENBQUMsdUJBQXVCQTtRQUNyQyxPQUFPO1lBQUVKLFNBQVM7WUFBT0k7UUFBTTtJQUNqQztBQUNGLEVBQUM7QUFFRCxnQkFBZ0I7QUFDVCxNQUFNa0MsZUFBZTtJQUMxQixJQUFJO1FBQ0YsTUFBTXpDLFNBQVMsTUFBTUMsTUFBTSxDQUFDOzs7O0lBSTVCLENBQUM7UUFFRCxPQUFPO1lBQUVFLFNBQVM7WUFBTVEsTUFBTVgsT0FBT1csSUFBSSxJQUFJLEVBQUU7UUFBQztJQUNsRCxFQUFFLE9BQU9KLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLHVCQUF1QkE7UUFDckMsT0FBTztZQUFFSixTQUFTO1lBQU9JO1FBQU07SUFDakM7QUFDRixFQUFDO0FBRUQsaUJBQWlCO0FBQ1YsTUFBTW1DLGVBQWU7SUFDMUIsSUFBSTtRQUNGLE1BQU0xQyxTQUFTLE1BQU1DLE1BQU0sQ0FBQzs7OztJQUk1QixDQUFDO1FBRUQsT0FBTztZQUFFRSxTQUFTO1lBQU1RLE1BQU1YLE9BQU9XLElBQUksSUFBSSxFQUFFO1FBQUM7SUFDbEQsRUFBRSxPQUFPSixPQUFPO1FBQ2RDLFFBQVFELEtBQUssQ0FBQyx3QkFBd0JBO1FBQ3RDLE9BQU87WUFBRUosU0FBUztZQUFPSTtRQUFNO0lBQ2pDO0FBQ0YsRUFBQztBQUVELHVCQUF1QjtBQUNoQixNQUFNb0MsaUJBQWlCO0lBQzVCLElBQUk7UUFDRixNQUFNM0MsU0FBUyxNQUFNQyxNQUFNLENBQUM7Ozs7Ozs7Ozs7O0lBVzVCLENBQUM7UUFFRCxPQUFPO1lBQUVFLFNBQVM7WUFBTVEsTUFBTVgsT0FBT1csSUFBSSxJQUFJLEVBQUU7UUFBQztJQUNsRCxFQUFFLE9BQU9KLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDhCQUE4QkE7UUFDNUMsT0FBTztZQUFFSixTQUFTO1lBQU9JO1FBQU07SUFDakM7QUFDRixFQUFDO0FBRUQscUJBQXFCO0FBQ2QsTUFBTXFDLG9CQUFvQjtJQUMvQixJQUFJO1FBQ0YsTUFBTTVDLFNBQVMsTUFBTUMsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs7O0lBYTVCLENBQUM7UUFFRCxPQUFPO1lBQUVFLFNBQVM7WUFBTVEsTUFBTVgsT0FBT1csSUFBSSxJQUFJLEVBQUU7UUFBQztJQUNsRCxFQUFFLE9BQU9KLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDRCQUE0QkE7UUFDMUMsT0FBTztZQUFFSixTQUFTO1lBQU9JO1FBQU07SUFDakM7QUFDRixFQUFDO0FBRUQsc0JBQXNCO0FBQ2YsTUFBTXNDLG9CQUFvQjtJQUMvQixJQUFJO1FBQ0YsTUFBTTdDLFNBQVMsTUFBTUMsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs7O0lBYTVCLENBQUM7UUFFRCxPQUFPO1lBQUVFLFNBQVM7WUFBTVEsTUFBTVgsT0FBT1csSUFBSSxJQUFJLEVBQUU7UUFBQztJQUNsRCxFQUFFLE9BQU9KLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBTztZQUFFSixTQUFTO1lBQU9JO1FBQU07SUFDakM7QUFDRixFQUFDO0FBRUQsc0JBQXNCO0FBQ2YsTUFBTXVDLHlCQUF5QjtJQUNwQyxJQUFJO1FBQ0YsTUFBTTlDLFNBQVMsTUFBTUMsTUFBTSxDQUFDOzs7Ozs7Ozs7Ozs7O0lBYTVCLENBQUM7UUFFRCxPQUFPO1lBQUVFLFNBQVM7WUFBTVEsTUFBTVgsT0FBT1csSUFBSSxJQUFJLEVBQUU7UUFBQztJQUNsRCxFQUFFLE9BQU9KLE9BQU87UUFDZEMsUUFBUUQsS0FBSyxDQUFDLDZCQUE2QkE7UUFDM0MsT0FBTztZQUFFSixTQUFTO1lBQU9JO1FBQU07SUFDakM7QUFDRixFQUFDO0FBSUQsaUVBQWU3QixJQUFJQSxFQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYnVzaW5lc3MtbWFuYWdlbWVudC1zeXN0ZW0vLi9zcmMvbGliL3Bvc3RncmVzLnRzPzliYWEiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUG9vbCB9IGZyb20gJ3BnJ1xuXG4vLyDYpdi52K/Yp9ivINin2YTYp9iq2LXYp9mEINio2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqIFBvc3RncmVTUUxcbmNvbnN0IHBvb2wgPSBuZXcgUG9vbCh7XG4gIGhvc3Q6IHByb2Nlc3MuZW52LkRCX0hPU1QgfHwgJ2xvY2FsaG9zdCcsXG4gIHBvcnQ6IHBhcnNlSW50KHByb2Nlc3MuZW52LkRCX1BPUlQgfHwgJzU0MzInKSxcbiAgZGF0YWJhc2U6IHByb2Nlc3MuZW52LkRCX05BTUUgfHwgJ1Zlcm9fRVJQX0FCQScsXG4gIHVzZXI6IHByb2Nlc3MuZW52LkRCX1VTRVIgfHwgJ29wZW5wZycsXG4gIHBhc3N3b3JkOiBwcm9jZXNzLmVudi5EQl9QQVNTV09SRCB8fCAnVkBhZG1pbjAxMCcsXG4gIHNzbDogcHJvY2Vzcy5lbnYuTk9ERV9FTlYgPT09ICdwcm9kdWN0aW9uJyA/IHsgcmVqZWN0VW5hdXRob3JpemVkOiBmYWxzZSB9IDogZmFsc2UsXG4gIG1heDogMjAsXG4gIGlkbGVUaW1lb3V0TWlsbGlzOiAzMDAwMCxcbiAgY29ubmVjdGlvblRpbWVvdXRNaWxsaXM6IDIwMDAsXG59KVxuXG4vLyDYr9in2YTYqSDZhNmE2KrYrdmC2YIg2YXZhiDYp9mE2KfYqti12KfZhFxuZXhwb3J0IGNvbnN0IHRlc3RDb25uZWN0aW9uID0gYXN5bmMgKCkgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IGNsaWVudCA9IGF3YWl0IHBvb2wuY29ubmVjdCgpXG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgY2xpZW50LnF1ZXJ5KCdTRUxFQ1QgTk9XKCknKVxuICAgIGNsaWVudC5yZWxlYXNlKClcbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCB0aW1lOiByZXN1bHQucm93c1swXS5ub3cgfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINin2YTYp9iq2LXYp9mEINio2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvciB9XG4gIH1cbn1cblxuLy8g2K/Yp9mE2Kkg2YTYqtmG2YHZitiwINin2LPYqti52YTYp9mFXG5leHBvcnQgY29uc3QgcXVlcnkgPSBhc3luYyAodGV4dDogc3RyaW5nLCBwYXJhbXM/OiBhbnlbXSkgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHBvb2wucXVlcnkodGV4dCwgcGFyYW1zKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3VsdC5yb3dzLCByb3dDb3VudDogcmVzdWx0LnJvd0NvdW50IH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYqtmG2YHZitiwINin2YTYp9iz2KrYudmE2KfZhTonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3IgfVxuICB9XG59XG5cbi8vINiv2KfZhNipINmE2YTYqtit2YLZgiDZhdmGINmI2KzZiNivINin2YTYrNiv2KfZiNmEXG5leHBvcnQgY29uc3QgY2hlY2tUYWJsZXMgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoYFxuICAgICAgU0VMRUNUIHRhYmxlX25hbWVcbiAgICAgIEZST00gaW5mb3JtYXRpb25fc2NoZW1hLnRhYmxlc1xuICAgICAgV0hFUkUgdGFibGVfc2NoZW1hID0gJ3B1YmxpYydcbiAgICAgIEFORCB0YWJsZV9uYW1lIElOICgnYnJhbmNoZXMnLCAndXNlcnMnLCAncHJvZHVjdHMnLCAnd2FyZWhvdXNlcycpXG4gICAgYClcblxuICAgIGNvbnN0IHRhYmxlcyA9IHJlc3VsdC5kYXRhPy5tYXAocm93ID0+IHJvdy50YWJsZV9uYW1lKSB8fCBbXVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICB0YWJsZXMsXG4gICAgICBoYXNCcmFuY2hlczogdGFibGVzLmluY2x1ZGVzKCdicmFuY2hlcycpLFxuICAgICAgaGFzVXNlcnM6IHRhYmxlcy5pbmNsdWRlcygndXNlcnMnKSxcbiAgICAgIGhhc1Byb2R1Y3RzOiB0YWJsZXMuaW5jbHVkZXMoJ3Byb2R1Y3RzJyksXG4gICAgICBoYXNXYXJlaG91c2VzOiB0YWJsZXMuaW5jbHVkZXMoJ3dhcmVob3VzZXMnKSxcbiAgICAgIG5lZWRzU2V0dXA6ICF0YWJsZXMuaW5jbHVkZXMoJ2JyYW5jaGVzJylcbiAgICB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2YHYrdi1INin2YTYrNiv2KfZiNmEOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvciwgbmVlZHNTZXR1cDogdHJ1ZSB9XG4gIH1cbn1cblxuLy8g2K/Yp9mE2Kkg2YTYpdmG2LTYp9ihINmF2LPYqtiu2K/ZhSDYrNiv2YrYr1xuZXhwb3J0IGNvbnN0IGNyZWF0ZVVzZXIgPSBhc3luYyAodXNlckRhdGE6IHtcbiAgZW1haWw6IHN0cmluZ1xuICB1c2VybmFtZTogc3RyaW5nXG4gIGZ1bGxfbmFtZTogc3RyaW5nXG4gIHBhc3N3b3JkX2hhc2g6IHN0cmluZ1xuICByb2xlPzogc3RyaW5nXG59KSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoYFxuICAgICAgSU5TRVJUIElOVE8gdXNlcnMgKGVtYWlsLCB1c2VybmFtZSwgZnVsbF9uYW1lLCBwYXNzd29yZF9oYXNoLCByb2xlLCBpc19hY3RpdmUsIGNyZWF0ZWRfYXQpXG4gICAgICBWQUxVRVMgKCQxLCAkMiwgJDMsICQ0LCAkNSwgdHJ1ZSwgTk9XKCkpXG4gICAgICBSRVRVUk5JTkcgaWQsIGVtYWlsLCB1c2VybmFtZSwgZnVsbF9uYW1lLCByb2xlXG4gICAgYCwgW1xuICAgICAgdXNlckRhdGEuZW1haWwsXG4gICAgICB1c2VyRGF0YS51c2VybmFtZSxcbiAgICAgIHVzZXJEYXRhLmZ1bGxfbmFtZSxcbiAgICAgIHVzZXJEYXRhLnBhc3N3b3JkX2hhc2gsXG4gICAgICB1c2VyRGF0YS5yb2xlIHx8ICdhZG1pbidcbiAgICBdKVxuXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgdXNlcjogcmVzdWx0LmRhdGE/LlswXSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KXZhti02KfYoSDYp9mE2YXYs9iq2K7Yr9mFOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvciB9XG4gIH1cbn1cblxuLy8g2K/Yp9mE2Kkg2YTZhNio2K3YqyDYudmGINmF2LPYqtiu2K/ZhVxuZXhwb3J0IGNvbnN0IGZpbmRVc2VyID0gYXN5bmMgKGVtYWlsOiBzdHJpbmcpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBxdWVyeShgXG4gICAgICBTRUxFQ1QgaWQsIGVtYWlsLCB1c2VybmFtZSwgZnVsbF9uYW1lLCByb2xlLCBwYXNzd29yZF9oYXNoLCBpc19hY3RpdmVcbiAgICAgIEZST00gdXNlcnNcbiAgICAgIFdIRVJFIGVtYWlsID0gJDEgQU5EIGlzX2FjdGl2ZSA9IHRydWVcbiAgICBgLCBbZW1haWxdKVxuXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgdXNlcjogcmVzdWx0LmRhdGE/LlswXSB8fCBudWxsIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYp9mE2KjYrdirINi52YYg2KfZhNmF2LPYqtiu2K/ZhTonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3IgfVxuICB9XG59XG5cbi8vINiu2K/Zhdin2Kog2KfZhNmF2YbYqtis2KfYqiDZhdiq2YjYp9mB2YLYqSDZhdi5INin2YTZg9mI2K9cbmV4cG9ydCBjb25zdCBnZXRQcm9kdWN0cyA9IGFzeW5jICgpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBxdWVyeShgXG4gICAgICBTRUxFQ1RcbiAgICAgICAgcC4qLFxuICAgICAgICBpLnRvdGFsX3N0b2NrLFxuICAgICAgICBpLmF2YWlsYWJsZV9zdG9jayxcbiAgICAgICAgaS5yZXNlcnZlZF9zdG9ja1xuICAgICAgRlJPTSBwcm9kdWN0cyBwXG4gICAgICBMRUZUIEpPSU4gaW52ZW50b3J5IGkgT04gcC5pZCA9IGkucHJvZHVjdF9pZFxuICAgICAgV0hFUkUgcC5pc19hY3RpdmUgPSB0cnVlXG4gICAgICBPUkRFUiBCWSBwLm5hbWVcbiAgICBgKVxuXG4gICAgcmV0dXJuIHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiByZXN1bHQuZGF0YT8ubWFwKHByb2R1Y3QgPT4gKHtcbiAgICAgICAgLi4ucHJvZHVjdCxcbiAgICAgICAgY3VycmVudF9zdG9jazogcHJvZHVjdC50b3RhbF9zdG9jayB8fCAwLFxuICAgICAgICBwcmljZTogcHJvZHVjdC51bml0X3ByaWNlLFxuICAgICAgICBjb3N0OiBwcm9kdWN0LmNvc3RfcHJpY2VcbiAgICAgIH0pKSB8fCBbXVxuICAgIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYrNmE2Kgg2KfZhNmF2YbYqtis2KfYqjonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3IgfVxuICB9XG59XG5cbi8vINiu2K/Zhdin2Kog2KfZhNmF2K7YstmI2YYg2YXYqtmI2KfZgdmC2Kkg2YXYuSDYp9mE2YPZiNivXG5leHBvcnQgY29uc3QgZ2V0SW52ZW50b3J5ID0gYXN5bmMgKCkgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHF1ZXJ5KGBcbiAgICAgIFNFTEVDVFxuICAgICAgICBpLiosXG4gICAgICAgIHAubmFtZSBhcyBwcm9kdWN0X25hbWUsXG4gICAgICAgIHAuc2t1IGFzIHByb2R1Y3Rfc2t1LFxuICAgICAgICBwLmNhdGVnb3J5IGFzIHByb2R1Y3RfY2F0ZWdvcnksXG4gICAgICAgIHcubmFtZSBhcyB3YXJlaG91c2VfbmFtZVxuICAgICAgRlJPTSBpbnZlbnRvcnkgaVxuICAgICAgSk9JTiBwcm9kdWN0cyBwIE9OIGkucHJvZHVjdF9pZCA9IHAuaWRcbiAgICAgIEpPSU4gd2FyZWhvdXNlcyB3IE9OIGkud2FyZWhvdXNlX2lkID0gdy5pZFxuICAgICAgT1JERVIgQlkgaS51cGRhdGVkX2F0IERFU0NcbiAgICBgKVxuXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzdWx0LmRhdGEgfHwgW10gfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINis2YTYqCDYp9mE2YXYrtiy2YjZhjonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3IgfVxuICB9XG59XG5cbi8vINiu2K/Zhdin2Kog2KfZhNi52YXZhNin2KFcbmV4cG9ydCBjb25zdCBnZXRDdXN0b21lcnMgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoYFxuICAgICAgU0VMRUNUICogRlJPTSBjdXN0b21lcnNcbiAgICAgIFdIRVJFIGlzX2FjdGl2ZSA9IHRydWVcbiAgICAgIE9SREVSIEJZIG5hbWVcbiAgICBgKVxuXG4gICAgcmV0dXJuIHsgc3VjY2VzczogdHJ1ZSwgZGF0YTogcmVzdWx0LmRhdGEgfHwgW10gfVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoJ9iu2LfYoyDZgdmKINis2YTYqCDYp9mE2LnZhdmE2KfYoTonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3IgfVxuICB9XG59XG5cbi8vINiu2K/Zhdin2Kog2KfZhNmF2YjYsdiv2YrZhlxuZXhwb3J0IGNvbnN0IGdldFN1cHBsaWVycyA9IGFzeW5jICgpID0+IHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBxdWVyeShgXG4gICAgICBTRUxFQ1QgKiBGUk9NIHN1cHBsaWVyc1xuICAgICAgV0hFUkUgaXNfYWN0aXZlID0gdHJ1ZVxuICAgICAgT1JERVIgQlkgbmFtZVxuICAgIGApXG5cbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXN1bHQuZGF0YSB8fCBbXSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KzZhNioINin2YTZhdmI2LHYr9mK2YY6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yIH1cbiAgfVxufVxuXG4vLyDYrtiv2YXYp9iqINij2YjYp9mF2LEg2KfZhNmF2KjZiti52KfYqlxuZXhwb3J0IGNvbnN0IGdldFNhbGVzT3JkZXJzID0gYXN5bmMgKCkgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHF1ZXJ5KGBcbiAgICAgIFNFTEVDVFxuICAgICAgICBzby4qLFxuICAgICAgICBjLm5hbWUgYXMgY3VzdG9tZXJfbmFtZSxcbiAgICAgICAgYi5uYW1lIGFzIGJyYW5jaF9uYW1lLFxuICAgICAgICB1LmZ1bGxfbmFtZSBhcyBjcmVhdGVkX2J5X25hbWVcbiAgICAgIEZST00gc2FsZXNfb3JkZXJzIHNvXG4gICAgICBMRUZUIEpPSU4gY3VzdG9tZXJzIGMgT04gc28uY3VzdG9tZXJfaWQgPSBjLmlkXG4gICAgICBMRUZUIEpPSU4gYnJhbmNoZXMgYiBPTiBzby5icmFuY2hfaWQgPSBiLmlkXG4gICAgICBMRUZUIEpPSU4gdXNlcnMgdSBPTiBzby5jcmVhdGVkX2J5ID0gdS5pZFxuICAgICAgT1JERVIgQlkgc28uY3JlYXRlZF9hdCBERVNDXG4gICAgYClcblxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3VsdC5kYXRhIHx8IFtdIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYrNmE2Kgg2KPZiNin2YXYsSDYp9mE2YXYqNmK2LnYp9iqOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvciB9XG4gIH1cbn1cblxuLy8g2K7Yr9mF2KfYqiDYo9mI2KfZhdixINin2YTYtNix2KfYoVxuZXhwb3J0IGNvbnN0IGdldFB1cmNoYXNlT3JkZXJzID0gYXN5bmMgKCkgPT4ge1xuICB0cnkge1xuICAgIGNvbnN0IHJlc3VsdCA9IGF3YWl0IHF1ZXJ5KGBcbiAgICAgIFNFTEVDVFxuICAgICAgICBwby4qLFxuICAgICAgICBzLm5hbWUgYXMgc3VwcGxpZXJfbmFtZSxcbiAgICAgICAgYi5uYW1lIGFzIGJyYW5jaF9uYW1lLFxuICAgICAgICB3Lm5hbWUgYXMgd2FyZWhvdXNlX25hbWUsXG4gICAgICAgIHUuZnVsbF9uYW1lIGFzIGNyZWF0ZWRfYnlfbmFtZVxuICAgICAgRlJPTSBwdXJjaGFzZV9vcmRlcnMgcG9cbiAgICAgIExFRlQgSk9JTiBzdXBwbGllcnMgcyBPTiBwby5zdXBwbGllcl9pZCA9IHMuaWRcbiAgICAgIExFRlQgSk9JTiBicmFuY2hlcyBiIE9OIHBvLmJyYW5jaF9pZCA9IGIuaWRcbiAgICAgIExFRlQgSk9JTiB3YXJlaG91c2VzIHcgT04gcG8ud2FyZWhvdXNlX2lkID0gdy5pZFxuICAgICAgTEVGVCBKT0lOIHVzZXJzIHUgT04gcG8uY3JlYXRlZF9ieSA9IHUuaWRcbiAgICAgIE9SREVSIEJZIHBvLmNyZWF0ZWRfYXQgREVTQ1xuICAgIGApXG5cbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXN1bHQuZGF0YSB8fCBbXSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KzZhNioINij2YjYp9mF2LEg2KfZhNi02LHYp9ihOicsIGVycm9yKVxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IGZhbHNlLCBlcnJvciB9XG4gIH1cbn1cblxuLy8g2K7Yr9mF2KfYqiDYrdix2YPYp9iqINin2YTZhdiu2LLZiNmGXG5leHBvcnQgY29uc3QgZ2V0U3RvY2tNb3ZlbWVudHMgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoYFxuICAgICAgU0VMRUNUXG4gICAgICAgIHNtLiosXG4gICAgICAgIHAubmFtZSBhcyBwcm9kdWN0X25hbWUsXG4gICAgICAgIHAuc2t1IGFzIHByb2R1Y3Rfc2t1LFxuICAgICAgICB3Lm5hbWUgYXMgd2FyZWhvdXNlX25hbWUsXG4gICAgICAgIHUuZnVsbF9uYW1lIGFzIGNyZWF0ZWRfYnlfbmFtZVxuICAgICAgRlJPTSBzdG9ja19tb3ZlbWVudHMgc21cbiAgICAgIEpPSU4gcHJvZHVjdHMgcCBPTiBzbS5wcm9kdWN0X2lkID0gcC5pZFxuICAgICAgSk9JTiB3YXJlaG91c2VzIHcgT04gc20ud2FyZWhvdXNlX2lkID0gdy5pZFxuICAgICAgTEVGVCBKT0lOIHVzZXJzIHUgT04gc20uY3JlYXRlZF9ieSA9IHUuaWRcbiAgICAgIE9SREVSIEJZIHNtLmNyZWF0ZWRfYXQgREVTQ1xuICAgICAgTElNSVQgMTAwXG4gICAgYClcblxuICAgIHJldHVybiB7IHN1Y2Nlc3M6IHRydWUsIGRhdGE6IHJlc3VsdC5kYXRhIHx8IFtdIH1cbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCfYrti32KMg2YHZiiDYrNmE2Kgg2K3YsdmD2KfYqiDYp9mE2YXYrtiy2YjZhjonLCBlcnJvcilcbiAgICByZXR1cm4geyBzdWNjZXNzOiBmYWxzZSwgZXJyb3IgfVxuICB9XG59XG5cbi8vINiu2K/Zhdin2Kog2LfZhNio2KfYqiDYp9mE2LXZitin2YbYqVxuZXhwb3J0IGNvbnN0IGdldE1haW50ZW5hbmNlUmVxdWVzdHMgPSBhc3luYyAoKSA9PiB7XG4gIHRyeSB7XG4gICAgY29uc3QgcmVzdWx0ID0gYXdhaXQgcXVlcnkoYFxuICAgICAgU0VMRUNUXG4gICAgICAgIG1yLiosXG4gICAgICAgIHQuZnVsbF9uYW1lIGFzIHRlY2huaWNpYW5fbmFtZSxcbiAgICAgICAgYXQuZnVsbF9uYW1lIGFzIGFzc2lnbmVkX3RlY2huaWNpYW5fbmFtZSxcbiAgICAgICAgYi5uYW1lIGFzIGJyYW5jaF9uYW1lLFxuICAgICAgICB3Lm5hbWUgYXMgd2FyZWhvdXNlX25hbWVcbiAgICAgIEZST00gbWFpbnRlbmFuY2VfcmVxdWVzdHMgbXJcbiAgICAgIExFRlQgSk9JTiB1c2VycyB0IE9OIG1yLnRlY2huaWNpYW5faWQgPSB0LmlkXG4gICAgICBMRUZUIEpPSU4gdXNlcnMgYXQgT04gbXIuYXNzaWduZWRfdGVjaG5pY2lhbiA9IGF0LmlkXG4gICAgICBMRUZUIEpPSU4gYnJhbmNoZXMgYiBPTiBtci5icmFuY2hfaWQgPSBiLmlkXG4gICAgICBMRUZUIEpPSU4gd2FyZWhvdXNlcyB3IE9OIG1yLndhcmVob3VzZV9pZCA9IHcuaWRcbiAgICAgIE9SREVSIEJZIG1yLmNyZWF0ZWRfYXQgREVTQ1xuICAgIGApXG5cbiAgICByZXR1cm4geyBzdWNjZXNzOiB0cnVlLCBkYXRhOiByZXN1bHQuZGF0YSB8fCBbXSB9XG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcign2K7Yt9ijINmB2Yog2KzZhNioINi32YTYqNin2Kog2KfZhNi12YrYp9mG2Kk6JywgZXJyb3IpXG4gICAgcmV0dXJuIHsgc3VjY2VzczogZmFsc2UsIGVycm9yIH1cbiAgfVxufVxuXG5cblxuZXhwb3J0IGRlZmF1bHQgcG9vbFxuIl0sIm5hbWVzIjpbIlBvb2wiLCJwb29sIiwiaG9zdCIsInByb2Nlc3MiLCJlbnYiLCJEQl9IT1NUIiwicG9ydCIsInBhcnNlSW50IiwiREJfUE9SVCIsImRhdGFiYXNlIiwiREJfTkFNRSIsInVzZXIiLCJEQl9VU0VSIiwicGFzc3dvcmQiLCJEQl9QQVNTV09SRCIsInNzbCIsInJlamVjdFVuYXV0aG9yaXplZCIsIm1heCIsImlkbGVUaW1lb3V0TWlsbGlzIiwiY29ubmVjdGlvblRpbWVvdXRNaWxsaXMiLCJ0ZXN0Q29ubmVjdGlvbiIsImNsaWVudCIsImNvbm5lY3QiLCJyZXN1bHQiLCJxdWVyeSIsInJlbGVhc2UiLCJzdWNjZXNzIiwidGltZSIsInJvd3MiLCJub3ciLCJlcnJvciIsImNvbnNvbGUiLCJ0ZXh0IiwicGFyYW1zIiwiZGF0YSIsInJvd0NvdW50IiwiY2hlY2tUYWJsZXMiLCJ0YWJsZXMiLCJtYXAiLCJyb3ciLCJ0YWJsZV9uYW1lIiwiaGFzQnJhbmNoZXMiLCJpbmNsdWRlcyIsImhhc1VzZXJzIiwiaGFzUHJvZHVjdHMiLCJoYXNXYXJlaG91c2VzIiwibmVlZHNTZXR1cCIsImNyZWF0ZVVzZXIiLCJ1c2VyRGF0YSIsImVtYWlsIiwidXNlcm5hbWUiLCJmdWxsX25hbWUiLCJwYXNzd29yZF9oYXNoIiwicm9sZSIsImZpbmRVc2VyIiwiZ2V0UHJvZHVjdHMiLCJwcm9kdWN0IiwiY3VycmVudF9zdG9jayIsInRvdGFsX3N0b2NrIiwicHJpY2UiLCJ1bml0X3ByaWNlIiwiY29zdCIsImNvc3RfcHJpY2UiLCJnZXRJbnZlbnRvcnkiLCJnZXRDdXN0b21lcnMiLCJnZXRTdXBwbGllcnMiLCJnZXRTYWxlc09yZGVycyIsImdldFB1cmNoYXNlT3JkZXJzIiwiZ2V0U3RvY2tNb3ZlbWVudHMiLCJnZXRNYWludGVuYW5jZVJlcXVlc3RzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(api)/./src/lib/postgres.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/postgres/login.ts":
/*!*****************************************!*\
  !*** ./src/pages/api/postgres/login.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_postgres__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/postgres */ \"(api)/./src/lib/postgres.ts\");\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/auth */ \"(api)/./src/lib/auth.ts\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! bcryptjs */ \"bcryptjs\");\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(bcryptjs__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { email, password } = req.body;\n        if (!email || !password) {\n            return res.status(400).json({\n                success: false,\n                message: \"البريد الإلكتروني وكلمة المرور مطلوبان\"\n            });\n        }\n        // البحث عن المستخدم\n        const userResult = await (0,_lib_postgres__WEBPACK_IMPORTED_MODULE_0__.findUser)(email);\n        if (!userResult.success || !userResult.user) {\n            return res.status(401).json({\n                success: false,\n                message: \"بيانات تسجيل الدخول غير صحيحة\"\n            });\n        }\n        const user = userResult.user;\n        // التحقق من كلمة المرور\n        const isPasswordValid = await bcryptjs__WEBPACK_IMPORTED_MODULE_2___default().compare(password, user.password_hash);\n        if (!isPasswordValid) {\n            return res.status(401).json({\n                success: false,\n                message: \"بيانات تسجيل الدخول غير صحيحة\"\n            });\n        }\n        // تسجيل دخول ناجح\n        const permissions = _lib_auth__WEBPACK_IMPORTED_MODULE_1__.ROLE_PERMISSIONS[user.role] || [];\n        return res.status(200).json({\n            success: true,\n            message: \"تم تسجيل الدخول بنجاح\",\n            user: {\n                id: user.id,\n                email: user.email,\n                username: user.username,\n                full_name: user.full_name,\n                role: user.role,\n                is_active: user.is_active,\n                permissions\n            }\n        });\n    } catch (error) {\n        console.error(\"Error during login:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/postgres/login.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fpostgres%2Flogin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Cpostgres%5Clogin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();