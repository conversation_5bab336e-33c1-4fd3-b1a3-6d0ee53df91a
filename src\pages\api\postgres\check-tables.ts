import { NextApiRequest, NextApiResponse } from 'next'
import { query } from '@/lib/postgres'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // فحص الجداول الموجودة
    const tablesResult = await query(`
      SELECT
        table_name,
        table_type
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name
    `)

    if (!tablesResult.success) {
      return res.status(500).json({
        success: false,
        message: 'فشل في فحص الجداول',
        error: tablesResult.error
      })
    }

    const allTables = tablesResult.data || []
    const tableNames = allTables.map(row => row.table_name)

    // الجداول المطلوبة للنظام
    const requiredTables = [
      // الجداول الأساسية
      'branches',
      'warehouses',
      'cash_registers',
      'users',
      'customers',
      'suppliers',
      'products',
      'inventory',

      // جداول المبيعات والمشتريات
      'sales_orders',
      'sales_order_items',
      'purchase_orders',
      'purchase_order_items',

      // جداول المخزون والمالية
      'inventory_movements',
      'cash_transactions',
      'expenses',
      'installments',
      'installment_payments',

      // جداول الصيانة
      'maintenance_requests',
      'maintenance_required_parts',
      'maintenance_used_parts'
    ]

    // فحص الجداول المطلوبة
    const tableStatus = {}
    for (const table of requiredTables) {
      const exists = tableNames.includes(table)
      tableStatus[table] = {
        exists,
        status: exists ? '✅ موجود' : '❌ غير موجود'
      }

      // إذا كان الجدول موجود، فحص عدد السجلات
      if (exists) {
        try {
          const countResult = await query(`SELECT COUNT(*) as count FROM ${table}`)
          if (countResult.success) {
            tableStatus[table].count = parseInt(countResult.data[0].count)
            tableStatus[table].status += ` (${tableStatus[table].count} سجل)`
          }
        } catch (error) {
          tableStatus[table].status += ' (خطأ في العد)'
        }
      }
    }

    // فحص الأنواع المخصصة (ENUMs)
    const enumsResult = await query(`
      SELECT
        t.typname as enum_name,
        array_agg(e.enumlabel ORDER BY e.enumsortorder) as enum_values
      FROM pg_type t
      JOIN pg_enum e ON t.oid = e.enumtypid
      WHERE t.typname IN ('user_role', 'transaction_type', 'payment_method')
      GROUP BY t.typname
      ORDER BY t.typname
    `)

    const enums = enumsResult.success ? enumsResult.data : []

    // حساب الإحصائيات
    const existingTables = Object.values(tableStatus).filter(t => t.exists).length
    const missingTables = requiredTables.length - existingTables
    const setupComplete = missingTables === 0

    return res.status(200).json({
      success: true,
      database: {
        name: process.env.DB_NAME,
        host: process.env.DB_HOST,
        connected: true
      },
      summary: {
        totalRequired: requiredTables.length,
        existing: existingTables,
        missing: missingTables,
        setupComplete,
        status: setupComplete ? '✅ الإعداد مكتمل' : `⚠️ يحتاج ${missingTables} جداول`
      },
      tables: tableStatus,
      enums: enums.map(e => ({
        name: e.enum_name,
        values: e.enum_values,
        status: '✅ موجود'
      })),
      allTables: tableNames,
      recommendations: setupComplete ? [
        '✅ جميع الجداول موجودة',
        '🎯 يمكنك الآن إنشاء مستخدم جديد',
        '🚀 النظام جاهز للاستخدام'
      ] : [
        '⚠️ بعض الجداول مفقودة',
        '📝 نفذ ملف database_setup_postgres.sql',
        '🔄 أعد فحص الجداول بعد التنفيذ'
      ]
    })

  } catch (error) {
    console.error('خطأ في فحص الجداول:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
