import { useState, useEffect } from 'react'
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  Warehouse,
  CreditCard,
  Users,
  CheckCircle,
  AlertTriangle,
  ArrowRight,
  Settings,
  Plus
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface SetupOverviewProps {
  onNavigate: (path: string) => void
}

export default function SetupOverview({ onNavigate }: SetupOverviewProps) {
  const [setupData, setSetupData] = useState({
    branches: {
      total: 1,
      active: 1,
      inactive: 0,
      totalSales: 0,
    },
    warehouses: {
      total: 1,
      active: 1,
      inactive: 0,
      totalValue: 0,
      lowStock: 0,
    },
    cashRegisters: {
      total: 1,
      open: 0,
      closed: 1,
      locked: 0,
      totalBalance: 0,
    }
  })

  const [loading, setLoading] = useState(false)
  const [systemStatus, setSystemStatus] = useState({
    database: 'checking',
    tables: 'checking',
    users: 'checking',
    branches: 'checking'
  })

  useEffect(() => {
    checkSystemStatus()
  }, [])

  const checkSystemStatus = async () => {
    setLoading(true)
    try {
      // فحص حالة قاعدة البيانات
      const response = await fetch('/api/postgres/check-tables')
      const result = await response.json()

      if (result.success) {
        setSystemStatus({
          database: 'connected',
          tables: result.allTablesExist ? 'ready' : 'needs_setup',
          users: result.tableStatus?.users ? 'configured' : 'needs_setup',
          branches: result.tableStatus?.branches ? 'configured' : 'needs_setup'
        })
      } else {
        setSystemStatus({
          database: 'error',
          tables: 'error',
          users: 'error',
          branches: 'error'
        })
      }
    } catch (error) {
      console.error('Error checking system status:', error)
      setSystemStatus({
        database: 'error',
        tables: 'error',
        users: 'error',
        branches: 'error'
      })
    } finally {
      setLoading(false)
    }
  }

  const setupCards = [
    {
      title: 'الفروع',
      icon: Building2,
      path: '/branches',
      color: 'blue',
      stats: [
        { label: 'إجمالي الفروع', value: setupData.branches.total },
        { label: 'الفروع النشطة', value: setupData.branches.active },
        { label: 'المبيعات الشهرية', value: formatCurrency(setupData.branches.totalSales) },
      ],
      status: setupData.branches.active > 0 ? 'active' : 'warning',
      description: 'إدارة الفروع والمواقع'
    },
    {
      title: 'المخازن',
      icon: Warehouse,
      path: '/warehouses',
      color: 'green',
      stats: [
        { label: 'إجمالي المخازن', value: setupData.warehouses.total },
        { label: 'المخازن النشطة', value: setupData.warehouses.active },
        { label: 'قيمة المخزون', value: formatCurrency(setupData.warehouses.totalValue) },
      ],
      status: setupData.warehouses.lowStock > 0 ? 'warning' : 'active',
      description: 'إدارة المخازن والمخزون',
      alerts: setupData.warehouses.lowStock > 0 ? `${setupData.warehouses.lowStock} تنبيه مخزون` : null
    },
    {
      title: 'الصناديق النقدية',
      icon: CreditCard,
      path: '/cash-registers',
      color: 'purple',
      stats: [
        { label: 'إجمالي الصناديق', value: setupData.cashRegisters.total },
        { label: 'الصناديق المفتوحة', value: setupData.cashRegisters.open },
        { label: 'إجمالي الأرصدة', value: formatCurrency(setupData.cashRegisters.totalBalance) },
      ],
      status: setupData.cashRegisters.open > 0 ? 'active' : 'warning',
      description: 'إدارة الصناديق النقدية'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100'
      case 'warning':
        return 'text-orange-600 bg-orange-100'
      case 'error':
        return 'text-red-600 bg-red-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="h-4 w-4" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4" />
      default:
        return <Settings className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">إعداد النظام</h1>
        <p className="text-gray-600">إدارة الفروع والمخازن والصناديق النقدية</p>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">إجمالي الفروع</p>
                <p className="text-2xl font-bold text-blue-900">{setupData.branches.total}</p>
              </div>
              <Building2 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">إجمالي المخازن</p>
                <p className="text-2xl font-bold text-green-900">{setupData.warehouses.total}</p>
              </div>
              <Warehouse className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">إجمالي الصناديق</p>
                <p className="text-2xl font-bold text-purple-900">{setupData.cashRegisters.total}</p>
              </div>
              <CreditCard className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Setup Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {setupCards.map((card, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow cursor-pointer group">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className={`p-2 rounded-lg ${
                    card.color === 'blue' ? 'bg-blue-100' :
                    card.color === 'green' ? 'bg-green-100' : 'bg-purple-100'
                  }`}>
                    <card.icon className={`h-6 w-6 ${
                      card.color === 'blue' ? 'text-blue-600' :
                      card.color === 'green' ? 'text-green-600' : 'text-purple-600'
                    }`} />
                  </div>
                  <div>
                    <CardTitle className="text-lg">{card.title}</CardTitle>
                    <p className="text-sm text-gray-500">{card.description}</p>
                  </div>
                </div>
                <div className={`p-1 rounded-full ${getStatusColor(card.status)}`}>
                  {getStatusIcon(card.status)}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Stats */}
              <div className="space-y-2">
                {card.stats.map((stat, statIndex) => (
                  <div key={statIndex} className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">{stat.label}</span>
                    <span className="font-medium">{stat.value}</span>
                  </div>
                ))}
              </div>

              {/* Alerts */}
              {card.alerts && (
                <div className="flex items-center space-x-2 space-x-reverse p-2 bg-orange-50 rounded-lg">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <span className="text-sm text-orange-700">{card.alerts}</span>
                </div>
              )}

              {/* Action Button */}
              <Button
                className="w-full group-hover:bg-primary/90 transition-colors"
                onClick={() => onNavigate(card.path)}
              >
                إدارة {card.title}
                <ArrowRight className="h-4 w-4 mr-2" />
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Integration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Settings className="h-5 w-5 mr-2" />
            حالة التكامل بين الوحدات
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">حالة قاعدة البيانات</h4>
              <div className="space-y-2">
                <div className={`flex items-center justify-between p-2 rounded ${
                  systemStatus.database === 'connected' ? 'bg-green-50' :
                  systemStatus.database === 'error' ? 'bg-red-50' : 'bg-gray-50'
                }`}>
                  <span className="text-sm">الاتصال بقاعدة البيانات</span>
                  <Badge className={
                    systemStatus.database === 'connected' ? 'bg-green-100 text-green-800' :
                    systemStatus.database === 'error' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'
                  }>
                    {systemStatus.database === 'connected' ? 'متصل' :
                     systemStatus.database === 'error' ? 'خطأ' : 'فحص...'}
                  </Badge>
                </div>
                <div className={`flex items-center justify-between p-2 rounded ${
                  systemStatus.tables === 'ready' ? 'bg-green-50' :
                  systemStatus.tables === 'error' ? 'bg-red-50' : 'bg-orange-50'
                }`}>
                  <span className="text-sm">الجداول</span>
                  <Badge className={
                    systemStatus.tables === 'ready' ? 'bg-green-100 text-green-800' :
                    systemStatus.tables === 'error' ? 'bg-red-100 text-red-800' : 'bg-orange-100 text-orange-800'
                  }>
                    {systemStatus.tables === 'ready' ? 'جاهز' :
                     systemStatus.tables === 'error' ? 'خطأ' : 'يحتاج إعداد'}
                  </Badge>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-medium text-gray-900">حالة النظام</h4>
              <div className="space-y-2">
                <div className={`flex items-center justify-between p-2 rounded ${
                  systemStatus.users === 'configured' ? 'bg-green-50' : 'bg-orange-50'
                }`}>
                  <span className="text-sm">المستخدمين</span>
                  <Badge className={
                    systemStatus.users === 'configured' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                  }>
                    {systemStatus.users === 'configured' ? 'مُعد' : 'يحتاج إعداد'}
                  </Badge>
                </div>
                <div className={`flex items-center justify-between p-2 rounded ${
                  systemStatus.branches === 'configured' ? 'bg-green-50' : 'bg-orange-50'
                }`}>
                  <span className="text-sm">الفروع</span>
                  <Badge className={
                    systemStatus.branches === 'configured' ? 'bg-green-100 text-green-800' : 'bg-orange-100 text-orange-800'
                  }>
                    {systemStatus.branches === 'configured' ? 'مُعد' : 'يحتاج إعداد'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>إجراءات سريعة</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={() => onNavigate('/branches')}
            >
              <Plus className="h-6 w-6" />
              <span>إضافة فرع جديد</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={() => onNavigate('/warehouses')}
            >
              <Plus className="h-6 w-6" />
              <span>إضافة مخزن جديد</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex flex-col items-center justify-center space-y-2"
              onClick={() => onNavigate('/cash-registers')}
            >
              <Plus className="h-6 w-6" />
              <span>إضافة صندوق جديد</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
