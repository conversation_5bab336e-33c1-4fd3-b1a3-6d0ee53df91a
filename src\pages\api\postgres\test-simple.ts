import { NextApiRequest, NextApiResponse } from 'next'
import { query } from '@/lib/postgres'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const results = []

    // اختبار 1: استعلام بسيط
    console.log('اختبار 1: استعلام بسيط')
    const test1 = await query('SELECT NOW() as current_time, \'اختبار الاتصال\' as message')
    results.push({
      test: 'اختبار الاتصال',
      success: test1.success,
      data: test1.data,
      error: test1.error
    })

    // اختبار 2: فحص الجداول الموجودة
    console.log('اختبار 2: فحص الجداول الموجودة')
    const test2 = await query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `)
    results.push({
      test: 'فحص الجداول الموجودة',
      success: test2.success,
      data: test2.data,
      error: test2.error
    })

    // اختبار 3: فحص الأنواع المخصصة
    console.log('اختبار 3: فحص الأنواع المخصصة')
    const test3 = await query(`
      SELECT typname 
      FROM pg_type 
      WHERE typname IN ('user_role', 'transaction_type', 'payment_method')
    `)
    results.push({
      test: 'فحص الأنواع المخصصة',
      success: test3.success,
      data: test3.data,
      error: test3.error
    })

    // اختبار 4: إنشاء جدول بسيط
    console.log('اختبار 4: إنشاء جدول بسيط')
    const test4 = await query(`
      CREATE TABLE IF NOT EXISTS test_table (
        id SERIAL PRIMARY KEY,
        name VARCHAR(100),
        created_at TIMESTAMP DEFAULT NOW()
      )
    `)
    results.push({
      test: 'إنشاء جدول بسيط',
      success: test4.success,
      data: test4.data,
      error: test4.error
    })

    // اختبار 5: إدراج بيانات في الجدول التجريبي
    console.log('اختبار 5: إدراج بيانات')
    const test5 = await query(`
      INSERT INTO test_table (name) 
      VALUES ('اختبار البيانات') 
      ON CONFLICT DO NOTHING
    `)
    results.push({
      test: 'إدراج بيانات',
      success: test5.success,
      data: test5.data,
      error: test5.error
    })

    // اختبار 6: قراءة البيانات
    console.log('اختبار 6: قراءة البيانات')
    const test6 = await query('SELECT * FROM test_table LIMIT 5')
    results.push({
      test: 'قراءة البيانات',
      success: test6.success,
      data: test6.data,
      error: test6.error
    })

    // حذف الجدول التجريبي
    await query('DROP TABLE IF EXISTS test_table')

    // حساب الإحصائيات
    const successCount = results.filter(r => r.success).length
    const failCount = results.filter(r => !r.success).length

    return res.status(200).json({
      success: successCount > 0,
      message: `تم تنفيذ ${successCount} اختبار بنجاح، ${failCount} فشل`,
      summary: {
        totalTests: results.length,
        successful: successCount,
        failed: failCount
      },
      results,
      database: {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        name: process.env.DB_NAME,
        user: process.env.DB_USER
      }
    })

  } catch (error) {
    console.error('خطأ في الاختبار:', error)
    return res.status(500).json({
      success: false,
      message: 'خطأ في الخادم',
      error: error instanceof Error ? error.message : 'Unknown error'
    })
  }
}
