# إعداد النظام مع PostgreSQL الموجود 🐘

## معلومات قاعدة البيانات الحالية:
- **المضيف**: localhost
- **المستخدم**: openpg  
- **كلمة المرور**: V@admin010
- **قاعدة البيانات**: Vero_ERP_ABA

## الخطوات المطلوبة:

### الخطوة 1: تطبيق الجداول على قاعدة البيانات

1. **افتح pgAdmin أو أي أداة PostgreSQL**
2. **اتصل بقاعدة البيانات** `Vero_ERP_ABA`
3. **انسخ محتوى ملف** `database_setup_postgres.sql`
4. **نفذ الاستعلام** في Query Tool

أو باستخدام سطر الأوامر:
```bash
psql -h localhost -U openpg -d Vero_ERP_ABA -f database_setup_postgres.sql
```

### الخطوة 2: التحقق من تثبيت المكتبات

انتظر حتى يكتمل تثبيت المكتبات:
```bash
npm install pg @types/pg bcryptjs @types/bcryptjs
```

### الخطوة 3: اختبار الاتصال

1. **أعد تشغيل النظام**:
   ```bash
   npm run dev
   ```

2. **اختبر الاتصال** بزيارة:
   ```
   http://localhost:3000/api/postgres/test-connection
   ```

### الخطوة 4: إنشاء المدير الأولي

1. **اذهب إلى صفحة الإعداد**: `http://localhost:3000/setup`
2. **أو استخدم API مباشرة**:
   ```bash
   curl -X POST http://localhost:3000/api/postgres/create-admin \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "password": "V@admin010",
       "fullName": "مدير النظام"
     }'
   ```

## التحقق من النجاح ✅

### إذا نجح الإعداد:
- ✅ `http://localhost:3000/api/postgres/test-connection` يعطي استجابة إيجابية
- ✅ لا توجد أخطاء في Console
- ✅ يمكن إنشاء مستخدم جديد
- ✅ يمكن تسجيل الدخول

### إذا فشل الإعداد:
- ❌ خطأ في الاتصال بقاعدة البيانات
- ❌ جداول غير موجودة
- ❌ خطأ في كلمة المرور

## استكشاف الأخطاء 🔧

### خطأ: "password authentication failed"
**الحل**: تأكد من كلمة المرور `V@admin010`

### خطأ: "database does not exist"
**الحل**: تأكد من اسم قاعدة البيانات `Vero_ERP_ABA`

### خطأ: "relation does not exist"
**الحل**: تطبيق ملف `database_setup_postgres.sql`

### خطأ: "connection refused"
**الحل**: تأكد من تشغيل PostgreSQL على المنفذ 5432

## بيانات تسجيل الدخول الافتراضية

بعد إنشاء المدير:
- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: V@admin010

## ملاحظات مهمة 📝

1. **الأمان**: غير كلمة المرور بعد أول تسجيل دخول
2. **النسخ الاحتياطي**: قم بعمل نسخة احتياطية من قاعدة البيانات
3. **الصلاحيات**: تأكد من صلاحيات المستخدم `openpg`
4. **الشبكة**: تأكد من إعدادات الجدار الناري

---

**🚀 الآن النظام جاهز للعمل مع قاعدة البيانات الموجودة!**
