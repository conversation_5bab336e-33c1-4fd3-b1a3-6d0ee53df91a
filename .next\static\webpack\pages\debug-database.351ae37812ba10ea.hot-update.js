"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/debug-database",{

/***/ "./src/pages/debug-database.tsx":
/*!**************************************!*\
  !*** ./src/pages/debug-database.tsx ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DebugDatabase; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/textarea */ \"./src/components/ui/textarea.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Copy,Database,Play,XCircle!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Copy,Database,Play,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"./node_modules/next/dist/build/polyfills/process.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction DebugDatabase() {\n    _s();\n    const [result, setResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [setupLoading, setSetupLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [sql, setSql] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"-- اختبار بسيط\\nSELECT NOW() as current_time, 'اختبار الاتصال' as message;\");\n    const testQueries = [\n        {\n            name: \"اختبار الاتصال\",\n            sql: \"SELECT NOW() as current_time, 'اختبار الاتصال' as message;\"\n        },\n        {\n            name: \"فحص الجداول الموجودة\",\n            sql: \"SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;\"\n        },\n        {\n            name: \"إنشاء نوع مخصص\",\n            sql: \"DO $$\\nBEGIN\\n    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'user_role') THEN\\n        CREATE TYPE user_role AS ENUM ('admin', 'manager', 'employee', 'cashier');\\n    END IF;\\nEND $$;\"\n        },\n        {\n            name: \"إنشاء جدول الفروع\",\n            sql: \"CREATE TABLE IF NOT EXISTS branches (\\n    id SERIAL PRIMARY KEY,\\n    name VARCHAR(255) NOT NULL,\\n    address TEXT NOT NULL,\\n    phone VARCHAR(50),\\n    email VARCHAR(255),\\n    is_active BOOLEAN DEFAULT true,\\n    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),\\n    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()\\n);\"\n        },\n        {\n            name: \"إدراج بيانات تجريبية\",\n            sql: \"INSERT INTO branches (name, address, phone, email)\\nVALUES ('الفرع الرئيسي', 'العنوان الرئيسي للشركة', '+201234567890', '<EMAIL>')\\nON CONFLICT DO NOTHING;\"\n        }\n    ];\n    const executeQuery = async (queryToRun)=>{\n        setLoading(true);\n        setResult(null);\n        try {\n            const response = await fetch(\"/api/postgres/single-query\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    sql: queryToRun || sql\n                })\n            });\n            const data = await response.json();\n            setResult(data);\n        } catch (err) {\n            setResult({\n                success: false,\n                message: \"خطأ في الاتصال بالخادم\",\n                error: err instanceof Error ? err.message : \"Unknown error\"\n            });\n        } finally{\n            setLoading(false);\n        }\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"تشخيص قاعدة البيانات PostgreSQL\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"اختبار الاستعلامات خطوة بخطوة لفهم المشكلة\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"اختبارات سريعة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 108,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\",\n                                children: testQueries.map((test, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"outline\",\n                                        onClick: ()=>executeQuery(test.sql),\n                                        disabled: loading,\n                                        className: \"h-auto p-4 text-right\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: test.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-xs text-gray-500 mt-1\",\n                                                    children: [\n                                                        test.sql.substring(0, 50),\n                                                        \"...\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"استعلام مخصص\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 135,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            className: \"space-y-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_5__.Textarea, {\n                                    value: sql,\n                                    onChange: (e)=>setSql(e.target.value),\n                                    placeholder: \"أدخل استعلام SQL هنا...\",\n                                    rows: 8,\n                                    className: \"font-mono text-sm\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            onClick: ()=>executeQuery(),\n                                            disabled: loading,\n                                            className: \"flex items-center\",\n                                            children: [\n                                                loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Database, {\n                                                    className: \"h-4 w-4 mr-2 animate-pulse\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Play, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 155,\n                                                    columnNumber: 19\n                                                }, this),\n                                                loading ? \"جاري التنفيذ...\" : \"تنفيذ الاستعلام\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"outline\",\n                                            onClick: ()=>copyToClipboard(sql),\n                                            className: \"flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.Copy, {\n                                                    className: \"h-4 w-4 mr-2\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                    lineNumber: 164,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"نسخ\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 9\n                }, this),\n                result && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    result.success ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.CheckCircle, {\n                                        className: \"h-5 w-5 mr-2 text-green-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 177,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Copy_Database_Play_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__.XCircle, {\n                                        className: \"h-5 w-5 mr-2 text-red-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"نتيجة الاستعلام\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 174,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                                className: result.success ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: result.message\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this),\n                                            result.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-red-100 p-3 rounded text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"خطأ:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    \" \",\n                                                    JSON.stringify(result.error, null, 2)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 23\n                                            }, this),\n                                            result.data && result.data.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-green-100 p-3 rounded text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                        children: \"البيانات:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                        lineNumber: 198,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                        className: \"mt-2 overflow-auto\",\n                                                        children: JSON.stringify(result.data, null, 2)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 23\n                                            }, this),\n                                            result.rowCount !== undefined && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    \"عدد الصفوف المتأثرة: \",\n                                                    result.rowCount\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 185,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 173,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"معلومات قاعدة البيانات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 217,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"المضيف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: process.env.NEXT_PUBLIC_DB_HOST || \"localhost\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"المنفذ\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: process.env.NEXT_PUBLIC_DB_PORT || \"5432\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"قاعدة البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: process.env.NEXT_PUBLIC_DB_NAME || \"Vero_ERP_ABA\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"المستخدم\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: process.env.NEXT_PUBLIC_DB_USER || \"openpg\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                        lineNumber: 234,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n            lineNumber: 95,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\debug-database.tsx\",\n        lineNumber: 94,\n        columnNumber: 5\n    }, this);\n}\n_s(DebugDatabase, \"hm71hE+kApM6ptN9udLgQN9AHhI=\");\n_c = DebugDatabase;\nvar _c;\n$RefreshReg$(_c, \"DebugDatabase\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/debug-database.tsx\n"));

/***/ })

});