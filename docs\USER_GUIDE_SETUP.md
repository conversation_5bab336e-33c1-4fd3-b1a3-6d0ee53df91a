# ⚙️ دليل استخدام موديول الإعدادات العامة

## 🎯 **نظرة عامة**
موديول الإعدادات العامة هو أساس النظام الذي يحتوي على جميع الإعدادات الأساسية المطلوبة لتشغيل باقي الموديولات، بما في ذلك إدارة الفروع والمخازن وصناديق النقد وإعدادات النظام العامة.

---

## 📋 **المكونات الرئيسية**

### 1. **إدارة الفروع (Branches Management)**
### 2. **إدارة المخازن (Warehouses Management)**  
### 3. **إدارة صناديق النقد (Cash Registers)**
### 4. **إعدادات النظام (System Settings)**
### 5. **إدارة المستخدمين والصلاحيات (Users & Permissions)**

---

## 🏢 **1. إدارة الفروع (Branches Management)**

### 📍 **الوصول**: `/setup/branches`

### ✨ **الميزات الرئيسية**
- إدارة فروع متعددة للشركة
- ربط كل فرع بمخازن وصناديق نقد
- تتبع الأنشطة لكل فرع منفصل
- تقارير منفصلة لكل فرع

### 🔧 **إنشاء فرع جديد**

#### **البيانات الأساسية**
1. انقر "فرع جديد"
2. **معلومات الفرع**:
   - اسم الفرع (مثل: الفرع الرئيسي، فرع المعادي)
   - كود الفرع (تلقائي: BR001, BR002)
   - نوع الفرع (رئيسي، فرعي، مستودع، معرض)
   - حالة الفرع (نشط، غير نشط، تحت الإنشاء)

#### **العنوان والموقع**
- العنوان التفصيلي
- المدينة والمحافظة
- الرمز البريدي
- إحداثيات GPS (اختياري)
- خريطة الموقع

#### **بيانات الاتصال**
- الهاتف الأساسي
- هاتف إضافي
- الفاكس
- البريد الإلكتروني
- الموقع الإلكتروني

#### **الإعدادات التشغيلية**
- ساعات العمل (من - إلى)
- أيام العمل في الأسبوع
- المسؤول عن الفرع
- عدد الموظفين
- المساحة الإجمالية

#### **الإعدادات المالية**
- العملة الأساسية للفرع
- طرق الدفع المقبولة
- حد الصلاحية للمبيعات
- نسبة الخصم المسموحة

### 🎯 **ربط الفرع بالموديولات الأخرى**
- **المخازن**: كل فرع له مخازن مخصصة
- **صناديق النقد**: ربط الصناديق بالفروع
- **المبيعات**: تتبع مبيعات كل فرع
- **المشتريات**: ربط المشتريات بالفروع
- **الصيانة**: تحديد فرع الصيانة

---

## 📦 **2. إدارة المخازن (Warehouses Management)**

### 📍 **الوصول**: `/setup/warehouses`

### ✨ **الميزات الرئيسية**
- مخازن متعددة لكل فرع
- تصنيف المخازن حسب النوع والاستخدام
- إدارة المواقع داخل المخزن
- تتبع منفصل لمخزون كل مخزن

### 🔧 **إنشاء مخزن جديد**

#### **البيانات الأساسية**
1. انقر "مخزن جديد"
2. **معلومات المخزن**:
   - اسم المخزن (مثل: المخزن الرئيسي، مخزن قطع الغيار)
   - كود المخزن (تلقائي: WH001, WH002)
   - الفرع التابع له
   - نوع المخزن (رئيسي، فرعي، مؤقت، مرتجعات)

#### **التصنيف والاستخدام**
- **حسب النوع**:
  - 📦 **مخزن عام**: جميع أنواع البضائع
  - 🔧 **مخزن قطع غيار**: للصيانة فقط
  - 📱 **مخزن إلكترونيات**: أجهزة حساسة
  - 🏪 **مخزن معرض**: للعرض والبيع
- **حسب الاستخدام**:
  - 📥 **استقبال**: للبضائع الواردة
  - 📤 **إرسال**: للبضائع الصادرة
  - 🔄 **ترانزيت**: مؤقت للتحويلات
  - 🔒 **حجر**: للبضائع المعيبة

#### **المواصفات الفنية**
- المساحة الإجمالية (متر مربع)
- السعة التخزينية (طن أو متر مكعب)
- نوع التخزين (عادي، مبرد، مجمد)
- مستوى الأمان المطلوب
- نظام مكافحة الحريق

#### **إدارة المواقع**
- **الممرات (Aisles)**: A, B, C, D
- **الرفوف (Shelves)**: 1, 2, 3, 4, 5
- **المستويات (Levels)**: أعلى، وسط، أسفل
- **مثال موقع كامل**: A-3-وسط
- **الباركود للمواقع**: لتسريع العمليات

#### **الإعدادات التشغيلية**
- المسؤول عن المخزن
- ساعات العمل
- طريقة تقييم المخزون (FIFO, LIFO, متوسط)
- السماح بالمخزون السالب (نعم/لا)
- مستوى إعادة الطلب التلقائي

---

## 💰 **3. إدارة صناديق النقد (Cash Registers)**

### 📍 **الوصول**: `/setup/cash-registers`

### ✨ **الميزات الرئيسية**
- صناديق نقد متعددة لكل فرع
- تتبع الحركات النقدية
- إدارة الورديات والمناوبات
- تقارير يومية للصناديق

### 🔧 **إنشاء صندوق نقد جديد**

#### **البيانات الأساسية**
1. انقر "صندوق جديد"
2. **معلومات الصندوق**:
   - اسم الصندوق (مثل: صندوق المبيعات، صندوق الصيانة)
   - كود الصندوق (تلقائي: CR001, CR002)
   - الفرع التابع له
   - نوع الصندوق (رئيسي، فرعي، مؤقت)

#### **الإعدادات التشغيلية**
- **العملة الأساسية**: جنيه مصري، دولار، يورو
- **الرصيد الافتتاحي**: المبلغ الأولي
- **الحد الأقصى للرصيد**: للأمان
- **الحد الأدنى للرصيد**: تحذير عند الوصول إليه
- **المسؤول عن الصندوق**: أمين الصندوق

#### **أنواع المعاملات المسموحة**
- ✅ **مبيعات نقدية**: من فواتير المبيعات
- ✅ **مقبوضات من العملاء**: تحصيل ديون
- ✅ **مدفوعات للموردين**: سداد مستحقات
- ✅ **مصروفات نقدية**: مصروفات يومية
- ✅ **تحويلات بين الصناديق**: نقل أموال

#### **إعدادات الأمان**
- **كلمة مرور الصندوق**: للحماية
- **صلاحيات الوصول**: من يمكنه استخدام الصندوق
- **حد الصلاحية**: أقصى مبلغ لكل معاملة
- **تسجيل العمليات**: تتبع جميع الحركات

### 🔧 **إدارة الورديات**
- **وردية صباحية**: 8 ص - 4 م
- **وردية مسائية**: 4 م - 12 م
- **وردية ليلية**: 12 م - 8 ص
- **تسليم واستلام الورديات**: مع مطابقة الأرصدة

---

## ⚙️ **4. إعدادات النظام (System Settings)**

### 📍 **الوصول**: `/setup/settings`

### ✨ **الإعدادات العامة**

#### **إعدادات الشركة**
- اسم الشركة
- شعار الشركة
- العنوان الرئيسي
- أرقام الهواتف
- البريد الإلكتروني
- الموقع الإلكتروني
- الرقم الضريبي
- السجل التجاري

#### **إعدادات العملة والتسعير**
- **العملة الأساسية**: جنيه مصري
- **عملات إضافية**: دولار، يورو
- **أسعار الصرف**: تحديث يدوي أو تلقائي
- **عدد الخانات العشرية**: للأسعار والكميات
- **طريقة التقريب**: لأعلى، لأسفل، أقرب رقم

#### **إعدادات التاريخ والوقت**
- المنطقة الزمنية
- تنسيق التاريخ (dd/mm/yyyy)
- تنسيق الوقت (12/24 ساعة)
- بداية السنة المالية
- بداية الأسبوع (السبت/الأحد)

#### **إعدادات اللغة**
- اللغة الأساسية: العربية
- اللغة الثانوية: الإنجليزية
- اتجاه النص: من اليمين لليسار
- ترجمة تلقائية للتقارير

### ✨ **إعدادات الأرقام التسلسلية**

#### **أرقام المستندات**
- **فواتير المبيعات**: INV-2024-0001
- **فواتير المشتريات**: PUR-2024-0001
- **طلبات الصيانة**: MNT-2024-0001
- **عروض الأسعار**: QUO-2024-0001
- **أوامر البيع**: SO-2024-0001

#### **إعدادات الترقيم**
- بادئة الرقم (INV, PUR, MNT)
- السنة في الرقم (2024)
- عدد الأرقام (4 خانات)
- إعادة تعيين سنوياً (نعم/لا)

### ✨ **إعدادات النسخ الاحتياطي**
- **نسخ احتياطي تلقائي**: يومي، أسبوعي، شهري
- **مكان الحفظ**: محلي، سحابي، كليهما
- **عدد النسخ المحفوظة**: 30 نسخة
- **تشفير النسخ**: لحماية البيانات

---

## 👥 **5. إدارة المستخدمين والصلاحيات (Users & Permissions)**

### 📍 **الوصول**: `/setup/users`

### ✨ **إدارة المستخدمين**

#### **إضافة مستخدم جديد**
1. انقر "مستخدم جديد"
2. **البيانات الشخصية**:
   - الاسم الكامل
   - اسم المستخدم (للدخول)
   - كلمة المرور
   - البريد الإلكتروني
   - رقم الهاتف

#### **بيانات العمل**
- المسمى الوظيفي
- القسم/الإدارة
- الفرع التابع له
- تاريخ بداية العمل
- حالة الموظف (نشط، معطل، مجمد)

#### **الصلاحيات والأدوار**
- **مدير النظام**: صلاحيات كاملة
- **مدير المبيعات**: المبيعات والعملاء
- **مدير المشتريات**: المشتريات والموردين
- **أمين المخزن**: المخزون والحركات
- **المحاسب**: المحاسبة والتقارير المالية
- **فني الصيانة**: طلبات الصيانة فقط
- **موظف مبيعات**: إنشاء فواتير فقط

### ✨ **إدارة الصلاحيات التفصيلية**

#### **صلاحيات المبيعات**
- ✅ عرض قائمة العملاء
- ✅ إضافة عميل جديد
- ✅ تعديل بيانات العميل
- ❌ حذف العميل
- ✅ إنشاء عرض سعر
- ✅ إنشاء فاتورة مبيعات
- ✅ خصم حتى 10%
- ❌ خصم أكثر من 10%

#### **صلاحيات المخزون**
- ✅ عرض المخزون
- ✅ إضافة منتج جديد
- ✅ تعديل أسعار المنتجات
- ✅ حركات المخزون
- ❌ حذف المنتجات
- ✅ التحويلات بين المخازن
- ❌ تعديل التكلفة

#### **صلاحيات المحاسبة**
- ✅ عرض التقارير المالية
- ✅ كشوف حسابات العملاء
- ✅ تسجيل المصروفات
- ❌ تعديل الحسابات الرئيسية
- ✅ التدفق النقدي
- ❌ حذف المعاملات المالية

---

## 🎯 **نصائح للاستخدام الأمثل**

### ✅ **أفضل الممارسات**
1. **ابدأ بإعداد الفروع** قبل باقي الإعدادات
2. **أنشئ المخازن** لكل فرع حسب الحاجة
3. **حدد صناديق النقد** بوضوح لكل نشاط
4. **راجع الصلاحيات** بانتظام
5. **احتفظ بنسخ احتياطية** دورية

### ⚠️ **تجنب هذه الأخطاء**
- عدم تحديد الفروع والمخازن بوضوح
- إعطاء صلاحيات أكثر من المطلوب
- عدم تحديث إعدادات النظام
- تجاهل النسخ الاحتياطية
- عدم تدريب المستخدمين على النظام

### 🔧 **ترتيب الإعداد الأولي**
1. **إعدادات الشركة العامة**
2. **إنشاء الفروع**
3. **إنشاء المخازن لكل فرع**
4. **إنشاء صناديق النقد**
5. **إضافة المستخدمين وتحديد الصلاحيات**
6. **اختبار النظام**

---

## 🔗 **الروابط السريعة**

- **الفروع**: `/setup/branches`
- **المخازن**: `/setup/warehouses`
- **صناديق النقد**: `/setup/cash-registers`
- **إعدادات النظام**: `/setup/settings`
- **المستخدمين**: `/setup/users`

---

**💡 ملاحظة**: موديول الإعدادات هو الأساس لجميع موديولات النظام. يجب إعداده بعناية قبل البدء في استخدام باقي الموديولات لضمان عمل النظام بكفاءة.
