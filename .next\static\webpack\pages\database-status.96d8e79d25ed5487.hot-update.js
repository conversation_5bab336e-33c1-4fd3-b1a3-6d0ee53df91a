"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/database-status",{

/***/ "./src/pages/database-status.tsx":
/*!***************************************!*\
  !*** ./src/pages/database-status.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DatabaseStatus; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_alert__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/alert */ \"./src/components/ui/alert.tsx\");\n/* harmony import */ var _barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircle,Database,ExternalLink,RefreshCw,XCircle!=!lucide-react */ \"__barrel_optimize__?names=CheckCircle,Database,ExternalLink,RefreshCw,XCircle!=!./node_modules/lucide-react/dist/esm/lucide-react.js\");\n\nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction DatabaseStatus() {\n    _s();\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [setupLoading, setSetupLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [setupResult, setSetupResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const fetchStatus = async ()=>{\n        setLoading(true);\n        setError(\"\");\n        try {\n            const response = await fetch(\"/api/postgres/check-tables\");\n            const data = await response.json();\n            if (data.success) {\n                setStatus(data);\n            } else {\n                setError(data.message || \"فشل في فحص قاعدة البيانات\");\n            }\n        } catch (err) {\n            setError(\"خطأ في الاتصال بالخادم\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchStatus();\n    }, []);\n    const setupDatabase = async ()=>{\n        setSetupLoading(true);\n        setSetupResult(null);\n        try {\n            const response = await fetch(\"/api/postgres/setup-database\", {\n                method: \"POST\"\n            });\n            const data = await response.json();\n            setSetupResult(data);\n            // إعادة فحص الجداول بعد الإعداد\n            if (data.success) {\n                setTimeout(()=>{\n                    fetchStatus();\n                }, 1000);\n            }\n        } catch (err) {\n            setSetupResult({\n                success: false,\n                message: \"خطأ في تنفيذ الإعداد\"\n            });\n        } finally{\n            setSetupLoading(false);\n        }\n    };\n    const copyToClipboard = (text)=>{\n        navigator.clipboard.writeText(text);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.RefreshCw, {\n                        className: \"h-8 w-8 animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: \"جاري فحص قاعدة البيانات...\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                        lineNumber: 111,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                lineNumber: 109,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n            lineNumber: 108,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 p-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-6xl mx-auto space-y-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-2\",\n                            children: \"حالة قاعدة البيانات PostgreSQL\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: \"فحص الجداول والإعدادات المطلوبة للنظام\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 125,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 121,\n                    columnNumber: 9\n                }, this),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.Alert, {\n                    className: \"border-red-200 bg-red-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.XCircle, {\n                            className: \"h-4 w-4 text-red-600\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert__WEBPACK_IMPORTED_MODULE_4__.AlertDescription, {\n                            className: \"text-red-800\",\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 11\n                }, this),\n                (status === null || status === void 0 ? void 0 : status.database) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                className: \"flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Database, {\n                                        className: \"h-5 w-5 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 145,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"معلومات قاعدة البيانات\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"اسم قاعدة البيانات\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 152,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: status.database.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"المضيف\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium\",\n                                                children: status.database.host\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: \"حالة الاتصال\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 160,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: status.database.connected ? \"✅ متصل\" : \"❌ غير متصل\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this),\n                (status === null || status === void 0 ? void 0 : status.summary) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"ملخص الحالة\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 174,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 173,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-blue-600\",\n                                                    children: status.summary.totalRequired\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"المطلوب\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 182,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-green-600\",\n                                                    children: status.summary.existing\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"موجود\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-red-600\",\n                                                    children: status.summary.missing\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"مفقود\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold\",\n                                                    children: status.summary.setupComplete ? \"✅\" : \"⚠️\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: \"الحالة\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-medium\",\n                                        children: status.summary.status\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 172,\n                    columnNumber: 11\n                }, this),\n                (status === null || status === void 0 ? void 0 : status.tables) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"حالة الجداول\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\",\n                                children: Object.entries(status.tables).map((param)=>{\n                                    let [tableName, tableInfo] = param;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4 rounded-lg border \".concat(tableInfo.exists ? \"border-green-200 bg-green-50\" : \"border-red-200 bg-red-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium\",\n                                                        children: tableName\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    tableInfo.exists ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.CheckCircle, {\n                                                        className: \"h-5 w-5 text-green-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                        lineNumber: 230,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.XCircle, {\n                                                        className: \"h-5 w-5 text-red-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 227,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm\",\n                                                children: tableInfo.status\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, tableName, true, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 19\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 212,\n                    columnNumber: 11\n                }, this),\n                (status === null || status === void 0 ? void 0 : status.recommendations) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardTitle, {\n                                children: \"التوصيات\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 247,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 246,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-2\",\n                                children: status.recommendations.map((rec, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"flex items-start\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"mr-2\",\n                                            children: rec\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, index, false, {\n                                        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                        lineNumber: 252,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                lineNumber: 250,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 249,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-4 justify-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            onClick: fetchStatus,\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.RefreshCw, {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 264,\n                                    columnNumber: 13\n                                }, this),\n                                \"إعادة فحص\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 263,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"outline\",\n                            onClick: ()=>window.open(\"/api/postgres/check-tables\", \"_blank\"),\n                            className: \"flex items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircle_Database_ExternalLink_RefreshCw_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ExternalLink, {\n                                    className: \"h-4 w-4 mr-2\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                                    lineNumber: 273,\n                                    columnNumber: 13\n                                }, this),\n                                \"عرض JSON\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n                    lineNumber: 262,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n            lineNumber: 119,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\pages\\\\database-status.tsx\",\n        lineNumber: 118,\n        columnNumber: 5\n    }, this);\n}\n_s(DatabaseStatus, \"cDnSqfkiAtcJG30LV1Hr3X4Nggs=\");\n_c = DatabaseStatus;\nvar _c;\n$RefreshReg$(_c, \"DatabaseStatus\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/pages/database-status.tsx\n"));

/***/ })

});