# 👥 دليل استخدام موديول جهات الاتصال

## 🎯 **نظرة عامة**
موديول جهات الاتصال هو نظام موحد لإدارة جميع العملاء والموردين والشركاء التجاريين، مع إمكانية تصنيف كل جهة اتصال كعميل أو مورد أو كليهما، مع تكامل كامل مع جميع موديولات النظام.

---

## 📋 **المكونات الرئيسية**

### 1. **إدارة جهات الاتصال (Contacts Management)**
### 2. **كشوف الحسابات (Account Statements)**
### 3. **سجل المعاملات (Transaction History)**
### 4. **التصنيفات والمجموعات (Categories & Groups)**
### 5. **التقارير والإحصائيات (Reports & Analytics)**

---

## 👤 **1. إدارة جهات الاتصال (Contacts Management)**

### 📍 **الوصول**: `/contacts`

### ✨ **الميزات الرئيسية**
- نظام موحد للعملاء والموردين
- تصنيف مرن (عميل، مورد، أو كليهما)
- بيانات شاملة ومفصلة
- تتبع تاريخ التعاملات
- إدارة متقدمة للحسابات

### 🔧 **كيفية الاستخدام**

#### **إضافة جهة اتصال جديدة**
1. انقر على "إضافة جهة اتصال جديدة"
2. **البيانات الأساسية**:
   - الاسم الكامل/اسم الشركة
   - نوع جهة الاتصال:
     - 👤 **عميل فقط**: للمبيعات والخدمات
     - 🏭 **مورد فقط**: للمشتريات
     - 🔄 **عميل ومورد**: للتعامل في الاتجاهين
   - رقم الهاتف الأساسي
   - رقم واتساب (للتواصل التلقائي)
   - البريد الإلكتروني
3. **العنوان والموقع**:
   - العنوان التفصيلي
   - المدينة والمحافظة
   - الرمز البريدي
   - الدولة
4. **البيانات التجارية**:
   - الرقم الضريبي
   - السجل التجاري
   - نوع النشاط
   - تصنيف العميل/المورد
5. **الإعدادات المالية**:
   - العملة المفضلة
   - شروط الدفع
   - حد الائتمان
   - فترة السداد
6. **معلومات إضافية**:
   - الموقع الإلكتروني
   - ملاحظات خاصة
   - مستوى الأهمية
   - تاريخ بداية التعامل
7. **حفظ البيانات**

#### **تصنيفات جهات الاتصال**
- 🌟 **VIP**: عملاء مهمون
- 🏆 **مميز**: عملاء أو موردين مميزين
- 📈 **نشط**: تعاملات منتظمة
- 😴 **خامل**: لا توجد تعاملات حديثة
- ⚠️ **مراقب**: يحتاج متابعة خاصة

---

## 📊 **2. كشوف الحسابات (Account Statements)**

### 📍 **الوصول**: `/contacts/[id]` → تبويب "كشف الحساب"

### ✨ **الميزات الرئيسية**
- كشف حساب شامل لكل جهة اتصال
- تتبع المدين والدائن
- تفاصيل كل معاملة
- أرصدة محدثة لحظياً

### 🔧 **فهم كشف الحساب**

#### **للعملاء (المدين)**
- 💰 **المدين**: ما يدين به العميل للشركة
- 💳 **الدائن**: ما دفعه العميل أو المرتجعات
- 📊 **الرصيد**: الفرق بين المدين والدائن

#### **للموردين (الدائن)**
- 💰 **المدين**: ما دفعته الشركة للمورد
- 💳 **الدائن**: ما تدين به الشركة للمورد
- 📊 **الرصيد**: الفرق بين الدائن والمدين

#### **أنواع المعاملات**
- 🧾 **فاتورة مبيعات**: زيادة مدين العميل
- 💵 **دفعة من العميل**: زيادة دائن العميل
- 🔄 **مرتجع مبيعات**: تقليل مدين العميل
- 🛒 **فاتورة شراء**: زيادة دائن المورد
- 💸 **دفعة للمورد**: زيادة مدين المورد
- 🔄 **مرتجع مشتريات**: تقليل دائن المورد
- 🔧 **خدمات صيانة**: حسب نوع الخدمة

---

## 📋 **3. سجل المعاملات (Transaction History)**

### 📍 **الوصول**: `/contacts/[id]` → تبويب "السجل"

### ✨ **تتبع شامل للأنشطة**
- جميع المعاملات التجارية
- تواريخ ومبالغ دقيقة
- مراجع المستندات
- ملاحظات وتفاصيل

### 🔧 **أنواع السجلات**

#### **سجل المبيعات**
- عروض الأسعار المرسلة
- أوامر البيع المؤكدة
- فواتير المبيعات
- مرتجعات المبيعات
- المدفوعات المستلمة

#### **سجل المشتريات**
- طلبات الشراء المرسلة
- فواتير المشتريات
- مرتجعات المشتريات
- المدفوعات المرسلة

#### **سجل الصيانة**
- طلبات الصيانة
- حالة كل طلب
- فواتير الصيانة
- قطع الغيار المستخدمة

#### **سجل التواصل**
- رسائل واتساب
- مكالمات هاتفية
- رسائل بريد إلكتروني
- اجتماعات ولقاءات

---

## 🏷️ **4. التصنيفات والمجموعات (Categories & Groups)**

### ✨ **تصنيفات العملاء**
- 🏢 **شركات**: عملاء مؤسسيون
- 👤 **أفراد**: عملاء أفراد
- 🏪 **تجار**: موزعين وتجار جملة
- 🏥 **مؤسسات**: مستشفيات، مدارس، حكومة

### ✨ **تصنيفات الموردين**
- 🏭 **مصنعين**: موردين مباشرين
- 📦 **موزعين**: وسطاء تجاريين
- 🚚 **خدمات**: نقل، تخزين، صيانة
- 🌍 **دوليين**: موردين من الخارج

### 🔧 **إدارة المجموعات**
- إنشاء مجموعات مخصصة
- تصنيف حسب المنطقة الجغرافية
- تجميع حسب حجم التعامل
- مجموعات خاصة للحملات التسويقية

---

## 📊 **5. التقارير والإحصائيات (Reports & Analytics)**

### 📈 **تقارير العملاء**
- **كشف حسابات العملاء**: أرصدة جميع العملاء
- **أعمار الديون**: تحليل فترات الاستحقاق
- **أفضل العملاء**: حسب حجم المبيعات
- **العملاء الخاملين**: لم يتعاملوا مؤخراً

### 📊 **تقارير الموردين**
- **كشف حسابات الموردين**: أرصدة جميع الموردين
- **تقييم أداء الموردين**: جودة، سعر، توقيت
- **أكبر الموردين**: حسب حجم المشتريات
- **مواعيد الاستحقاق**: المدفوعات المطلوبة

### 📋 **تقارير تحليلية**
- **تحليل الربحية**: ربحية كل عميل
- **تحليل المخاطر**: تقييم المخاطر الائتمانية
- **اتجاهات التعامل**: نمو أو تراجع التعاملات
- **التوزيع الجغرافي**: العملاء والموردين حسب المناطق

---

## 💡 **ميزات متقدمة**

### 📱 **التكامل مع واتساب**
- إرسال رسائل تلقائية
- تنبيهات المواعيد
- تأكيدات الطلبات
- متابعة المدفوعات

### 🔔 **التنبيهات الذكية**
- تنبيهات مواعيد الاستحقاق
- تحذيرات تجاوز حد الائتمان
- إشعارات العملاء الخاملين
- تذكيرات المتابعة

### 📊 **لوحة المعلومات**
- ملخص سريع لكل جهة اتصال
- آخر المعاملات
- الرصيد الحالي
- إجراءات سريعة

### 🔍 **البحث المتقدم**
- البحث بالاسم أو الهاتف
- فلترة حسب النوع والتصنيف
- ترتيب حسب معايير مختلفة
- حفظ عمليات البحث المفضلة

---

## 🎯 **نصائح للاستخدام الأمثل**

### ✅ **أفضل الممارسات**
1. **أدخل البيانات كاملة** عند إنشاء جهة اتصال جديدة
2. **حدث البيانات بانتظام** خاصة أرقام الهواتف والعناوين
3. **استخدم التصنيفات** لتسهيل البحث والفلترة
4. **راجع كشوف الحسابات** شهرياً
5. **تابع التنبيهات** والمواعيد المهمة

### ⚠️ **تجنب هذه الأخطاء**
- إنشاء جهات اتصال مكررة
- عدم تحديث البيانات المتغيرة
- تجاهل التنبيهات المالية
- عدم تصنيف جهات الاتصال

### 🔧 **اختصارات مفيدة**
- **Ctrl + F**: البحث السريع
- **Ctrl + N**: إضافة جهة اتصال جديدة
- **Enter**: فتح تفاصيل جهة الاتصال
- **F5**: تحديث القائمة

---

## 🔗 **الروابط السريعة**

- **جميع جهات الاتصال**: `/contacts`
- **العملاء فقط**: `/contacts?type=customer`
- **الموردين فقط**: `/contacts?type=supplier`
- **كشوف الحسابات**: `/accounting/statements`
- **تقارير جهات الاتصال**: `/contacts/reports`

---

## 🆘 **الدعم والمساعدة**

في حالة وجود أي مشاكل أو استفسارات:
1. راجع هذا الدليل أولاً
2. تحقق من صحة البيانات المدخلة
3. تأكد من الصلاحيات المطلوبة
4. تواصل مع فريق الدعم الفني

---

**💡 ملاحظة**: موديول جهات الاتصال هو الأساس لجميع المعاملات التجارية في النظام. الحفاظ على دقة وتحديث هذه البيانات أمر بالغ الأهمية لنجاح العمل.

---

## 🔄 **التكامل مع الموديولات الأخرى**

### 💰 **مع المبيعات**
- ربط تلقائي مع فواتير المبيعات
- تحديث كشف حساب العميل
- تتبع أوامر البيع والعروض

### 🛒 **مع المشتريات**
- ربط مع فواتير المشتريات
- تحديث كشف حساب المورد
- متابعة طلبات الشراء

### 🔧 **مع الصيانة**
- ربط طلبات الصيانة بالعملاء
- فواتير خدمات الصيانة
- تتبع تاريخ الصيانة

### 💳 **مع المحاسبة**
- كشوف الحسابات التفصيلية
- تقارير الذمم المدينة والدائنة
- التدفق النقدي
