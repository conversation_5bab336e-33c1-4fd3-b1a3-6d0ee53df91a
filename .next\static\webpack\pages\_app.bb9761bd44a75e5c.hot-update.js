"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./src/hooks/useAuth.tsx":
/*!*******************************!*\
  !*** ./src/hooks/useAuth.tsx ***!
  \*******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"./node_modules/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth */ \"./src/lib/auth.ts\");\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// دالة للحصول على صلاحيات الدور\nconst getRolePermissions = (role)=>{\n    return _lib_auth__WEBPACK_IMPORTED_MODULE_2__.ROLE_PERMISSIONS[role] || [];\n};\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // تحميل بيانات المستخدم من localStorage\n    const loadUserFromStorage = ()=>{\n        try {\n            const savedUser = localStorage.getItem(\"user\");\n            if (savedUser) {\n                return JSON.parse(savedUser);\n            }\n            return null;\n        } catch (error) {\n            console.error(\"Error loading user from storage:\", error);\n            return null;\n        }\n    };\n    const refreshUser = async ()=>{\n        const savedUser = loadUserFromStorage();\n        if (savedUser && !savedUser.permissions) {\n            // إضافة الصلاحيات إذا لم تكن موجودة\n            const permissions = getRolePermissions(savedUser.role);\n            const userWithPermissions = {\n                ...savedUser,\n                permissions\n            };\n            localStorage.setItem(\"user\", JSON.stringify(userWithPermissions));\n            setUser(userWithPermissions);\n        } else {\n            setUser(savedUser);\n        }\n    };\n    const signIn = async (email, password)=>{\n        setLoading(true);\n        try {\n            const response = await fetch(\"/api/postgres/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                const userData = result.user;\n                // إضافة الصلاحيات بناءً على الدور\n                const permissions = getRolePermissions(userData.role);\n                const userWithPermissions = {\n                    ...userData,\n                    permissions\n                };\n                localStorage.setItem(\"user\", JSON.stringify(userWithPermissions));\n                setUser(userWithPermissions);\n            } else {\n                throw new Error(result.message || \"فشل في تسجيل الدخول\");\n            }\n        } catch (error) {\n            console.error(\"Sign in error:\", error);\n            throw error;\n        } finally{\n            setLoading(false);\n        }\n    };\n    const signOut = async ()=>{\n        try {\n            localStorage.removeItem(\"user\");\n            setUser(null);\n        } catch (error) {\n            console.error(\"Sign out error:\", error);\n            throw error;\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // تحميل المستخدم من localStorage عند بدء التطبيق\n        const initializeAuth = ()=>{\n            try {\n                const savedUser = loadUserFromStorage();\n                setUser(savedUser);\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n                setUser(null);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n    }, []);\n    const value = {\n        user,\n        loading,\n        signIn,\n        signOut,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\v\\\\src\\\\hooks\\\\useAuth.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/hooks/useAuth.tsx\n"));

/***/ })

});