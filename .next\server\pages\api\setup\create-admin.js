"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/api/setup/create-admin";
exports.ids = ["pages/api/setup/create-admin"];
exports.modules = {

/***/ "@supabase/supabase-js":
/*!****************************************!*\
  !*** external "@supabase/supabase-js" ***!
  \****************************************/
/***/ ((module) => {

module.exports = require("@supabase/supabase-js");

/***/ }),

/***/ "next/dist/compiled/next-server/pages-api.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages-api.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/pages-api.runtime.dev.js");

/***/ }),

/***/ "(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   routeModule: () => (/* binding */ routeModule)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages-api/module.compiled */ \"(api)/./node_modules/next/dist/server/future/route-modules/pages-api/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(api)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(api)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var _src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src\\pages\\api\\setup\\create-admin.ts */ \"(api)/./src/pages/api/setup/create-admin.ts\");\n\n\n\n// Import the userland code.\n\n// Re-export the handler (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__, \"default\"));\n// Re-export config.\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__, \"config\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_api_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesAPIRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES_API,\n        page: \"/api/setup/create-admin\",\n        pathname: \"/api/setup/create-admin\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    userland: _src_pages_api_setup_create_admin_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n\n//# sourceMappingURL=pages-api.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(api)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSupabaseAdmin: () => (/* binding */ createSupabaseAdmin),\n/* harmony export */   getCurrentUser: () => (/* binding */ getCurrentUser),\n/* harmony export */   getUserProfile: () => (/* binding */ getUserProfile),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__);\n\n// Supabase configuration\nconst supabaseUrl = \"https://your-project-id.supabase.co\";\nconst supabaseAnonKey = \"your-anon-key-here\";\nif (!supabaseUrl || !supabaseAnonKey) {\n    throw new Error(\"Missing Supabase environment variables. Please check your .env.local file.\");\n}\n// Client-side Supabase client\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseAnonKey, {\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n// Server-side client with service role key (for admin operations)\nconst createSupabaseAdmin = ()=>{\n    const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n    if (!serviceRoleKey) {\n        throw new Error(\"Missing SUPABASE_SERVICE_ROLE_KEY environment variable\");\n    }\n    return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, serviceRoleKey, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n};\n// Helper function to get current user\nconst getCurrentUser = async ()=>{\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) throw error;\n    return user;\n};\n// Helper function to get user profile\nconst getUserProfile = async (userId)=>{\n    const { data, error } = await supabase.from(\"users\").select(\"*\").eq(\"id\", userId).single();\n    if (error) throw error;\n    return data;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(api)/./src/pages/api/setup/create-admin.ts":
/*!*********************************************!*\
  !*** ./src/pages/api/setup/create-admin.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ handler)\n/* harmony export */ });\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/supabase */ \"(api)/./src/lib/supabase.ts\");\n\nasync function handler(req, res) {\n    if (req.method !== \"POST\") {\n        return res.status(405).json({\n            message: \"Method not allowed\"\n        });\n    }\n    try {\n        const { email, password, fullName } = req.body;\n        if (!email || !password || !fullName) {\n            return res.status(400).json({\n                success: false,\n                message: \"جميع الحقول مطلوبة\"\n            });\n        }\n        const supabaseAdmin = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_0__.createSupabaseAdmin)();\n        // التحقق من عدم وجود مستخدمين مسبقاً\n        const { data: existingUsers } = await supabaseAdmin.from(\"users\").select(\"id\").limit(1);\n        if (existingUsers && existingUsers.length > 0) {\n            return res.status(400).json({\n                success: false,\n                message: \"يوجد مستخدمون في النظام مسبقاً\"\n            });\n        }\n        // إنشاء المستخدم في نظام المصادقة\n        const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({\n            email,\n            password,\n            email_confirm: true\n        });\n        if (authError) {\n            console.error(\"Auth error:\", authError);\n            return res.status(400).json({\n                success: false,\n                message: authError.message || \"فشل في إنشاء المستخدم\"\n            });\n        }\n        if (!authData.user) {\n            return res.status(400).json({\n                success: false,\n                message: \"فشل في إنشاء المستخدم\"\n            });\n        }\n        // إنشاء ملف المستخدم\n        const { error: profileError } = await supabaseAdmin.from(\"users\").insert({\n            id: authData.user.id,\n            email,\n            username: email.split(\"@\")[0],\n            full_name: fullName,\n            role: \"admin\",\n            is_active: true\n        });\n        if (profileError) {\n            console.error(\"Profile error:\", profileError);\n            // حذف المستخدم من نظام المصادقة إذا فشل إنشاء الملف\n            await supabaseAdmin.auth.admin.deleteUser(authData.user.id);\n            return res.status(400).json({\n                success: false,\n                message: \"فشل في إنشاء ملف المستخدم\"\n            });\n        }\n        return res.status(200).json({\n            success: true,\n            message: \"تم إنشاء المدير الأولي بنجاح\",\n            userId: authData.user.id\n        });\n    } catch (error) {\n        console.error(\"Error creating initial admin:\", error);\n        return res.status(500).json({\n            success: false,\n            message: \"خطأ في الخادم\",\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(api)/./src/pages/api/setup/create-admin.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-api-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(api)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES_API&page=%2Fapi%2Fsetup%2Fcreate-admin&preferredRegion=&absolutePagePath=.%2Fsrc%5Cpages%5Capi%5Csetup%5Ccreate-admin.ts&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();