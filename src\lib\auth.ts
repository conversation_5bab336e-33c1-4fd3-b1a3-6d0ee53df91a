// Mock user type for demo
export interface AuthUser {
  id: string
  email: string
  username: string
  full_name: string
  role: 'admin' | 'manager' | 'employee' | 'cashier'
  branch_id: string | null
  warehouse_id: string | null
  pos_id: string | null
  is_active: boolean
  created_at: string
  updated_at: string
  permissions: string[]
}

export const PERMISSIONS = {
  // User Management
  USERS_VIEW: 'users:view',
  USERS_CREATE: 'users:create',
  USERS_EDIT: 'users:edit',
  USERS_DELETE: 'users:delete',

  // Branch Management
  BRANCHES_VIEW: 'branches:view',
  BRANCHES_CREATE: 'branches:create',
  BRANCHES_EDIT: 'branches:edit',
  BRANCHES_DELETE: 'branches:delete',

  // Warehouse Management
  WAREHOUSES_VIEW: 'warehouses:view',
  WAREHOUSES_CREATE: 'warehouses:create',
  WAREHOUSES_EDIT: 'warehouses:edit',
  WAREHOUSES_DELETE: 'warehouses:delete',

  // Product Management
  PRODUCTS_VIEW: 'products:view',
  PRODUCTS_CREATE: 'products:create',
  PRODUCTS_EDIT: 'products:edit',
  PRODUCTS_DELETE: 'products:delete',

  // Sales
  SALES_VIEW: 'sales:view',
  SALES_CREATE: 'sales:create',
  SALES_EDIT: 'sales:edit',
  SALES_DELETE: 'sales:delete',

  // Purchases
  PURCHASES_VIEW: 'purchases:view',
  PURCHASES_CREATE: 'purchases:create',
  PURCHASES_EDIT: 'purchases:edit',
  PURCHASES_DELETE: 'purchases:delete',
  PURCHASES_APPROVE: 'purchases:approve',
  PURCHASES_RECEIVE: 'purchases:receive',
  PURCHASES_PAY: 'purchases:pay',
  PURCHASES_CANCEL: 'purchases:cancel',
  PURCHASES_PROCESS: 'purchases:process',

  // POS
  POS_ACCESS: 'pos:access',
  POS_CLOSE_DAY: 'pos:close_day',

  // Cash Registers
  CASH_REGISTERS_VIEW: 'cash_registers:view',
  CASH_REGISTERS_CREATE: 'cash_registers:create',
  CASH_REGISTERS_EDIT: 'cash_registers:edit',
  CASH_REGISTERS_DELETE: 'cash_registers:delete',

  // Accounting
  ACCOUNTING_VIEW: 'accounting:view',
  ACCOUNTING_EDIT: 'accounting:edit',
  ACCOUNTING_MANAGE: 'accounting:manage',
  ACCOUNTING_CREATE: 'accounting:create',
  ACCOUNTING_DELETE: 'accounting:delete',

  // Reports
  REPORTS_VIEW: 'reports:view',
  REPORTS_EXPORT: 'reports:export',
} as const

// Organized permissions for easier use
export const PERMISSIONS_ORGANIZED = {
  SALES: {
    VIEW: PERMISSIONS.SALES_VIEW,
    CREATE: PERMISSIONS.SALES_CREATE,
    EDIT: PERMISSIONS.SALES_EDIT,
    DELETE: PERMISSIONS.SALES_DELETE,
  },
  PURCHASES: {
    VIEW: PERMISSIONS.PURCHASES_VIEW,
    CREATE: PERMISSIONS.PURCHASES_CREATE,
    EDIT: PERMISSIONS.PURCHASES_EDIT,
    DELETE: PERMISSIONS.PURCHASES_DELETE,
    APPROVE: PERMISSIONS.PURCHASES_APPROVE,
    RECEIVE: PERMISSIONS.PURCHASES_RECEIVE,
    PAY: PERMISSIONS.PURCHASES_PAY,
    CANCEL: PERMISSIONS.PURCHASES_CANCEL,
    PROCESS: PERMISSIONS.PURCHASES_PROCESS,
  },
}

export const ROLE_PERMISSIONS = {
  admin: [
    // جميع الصلاحيات للمدير
    ...Object.values(PERMISSIONS)
  ],
  manager: [
    PERMISSIONS.USERS_VIEW,
    PERMISSIONS.BRANCHES_VIEW,
    PERMISSIONS.BRANCHES_CREATE,
    PERMISSIONS.BRANCHES_EDIT,
    PERMISSIONS.WAREHOUSES_VIEW,
    PERMISSIONS.WAREHOUSES_CREATE,
    PERMISSIONS.WAREHOUSES_EDIT,
    PERMISSIONS.CASH_REGISTERS_VIEW,
    PERMISSIONS.CASH_REGISTERS_CREATE,
    PERMISSIONS.CASH_REGISTERS_EDIT,
    PERMISSIONS.PRODUCTS_VIEW,
    PERMISSIONS.PRODUCTS_CREATE,
    PERMISSIONS.PRODUCTS_EDIT,
    PERMISSIONS.SALES_VIEW,
    PERMISSIONS.SALES_CREATE,
    PERMISSIONS.SALES_EDIT,
    PERMISSIONS.PURCHASES_VIEW,
    PERMISSIONS.PURCHASES_CREATE,
    PERMISSIONS.PURCHASES_EDIT,
    PERMISSIONS.PURCHASES_APPROVE,
    PERMISSIONS.PURCHASES_RECEIVE,
    PERMISSIONS.PURCHASES_PAY,
    PERMISSIONS.PURCHASES_CANCEL,
    PERMISSIONS.PURCHASES_PROCESS,
    PERMISSIONS.ACCOUNTING_VIEW,
    PERMISSIONS.ACCOUNTING_EDIT,
    PERMISSIONS.ACCOUNTING_MANAGE,
    PERMISSIONS.ACCOUNTING_CREATE,
    PERMISSIONS.ACCOUNTING_DELETE,
    PERMISSIONS.REPORTS_VIEW,
    PERMISSIONS.REPORTS_EXPORT,
  ],
  employee: [
    PERMISSIONS.PRODUCTS_VIEW,
    PERMISSIONS.SALES_VIEW,
    PERMISSIONS.SALES_CREATE,
    PERMISSIONS.PURCHASES_VIEW,
    PERMISSIONS.PURCHASES_CREATE,
    PERMISSIONS.REPORTS_VIEW,
  ],
  cashier: [
    PERMISSIONS.PRODUCTS_VIEW,
    PERMISSIONS.SALES_VIEW,
    PERMISSIONS.SALES_CREATE,
    PERMISSIONS.POS_ACCESS,
    PERMISSIONS.CASH_REGISTERS_VIEW,
  ],
}

export async function signIn(email: string, password: string) {
  // Mock authentication for demo
  await new Promise(resolve => setTimeout(resolve, 1000))

  if (email && password) {
    return { user: { id: '1', email } }
  }

  throw new Error('Invalid credentials')
}

export async function signOut() {
  // Mock sign out
  await new Promise(resolve => setTimeout(resolve, 500))
}

export async function getCurrentUser(): Promise<AuthUser | null> {
  // Mock user for demo - تأكد من أن جميع الصلاحيات متضمنة
  const adminPermissions = [
    // User Management
    'users:view', 'users:create', 'users:edit', 'users:delete',
    // Branch Management
    'branches:view', 'branches:create', 'branches:edit', 'branches:delete',
    // Warehouse Management
    'warehouses:view', 'warehouses:create', 'warehouses:edit', 'warehouses:delete',
    // Product Management
    'products:view', 'products:create', 'products:edit', 'products:delete',
    // Sales
    'sales:view', 'sales:create', 'sales:edit', 'sales:delete',
    // Purchases
    'purchases:view', 'purchases:create', 'purchases:edit', 'purchases:delete',
    'purchases:approve', 'purchases:receive', 'purchases:pay', 'purchases:cancel', 'purchases:process',
    // POS
    'pos:access', 'pos:close_day',
    // Cash Registers
    'cash_registers:view', 'cash_registers:create', 'cash_registers:edit', 'cash_registers:delete',
    // Accounting
    'accounting:view', 'accounting:edit', 'accounting:manage', 'accounting:create', 'accounting:delete',
    // Reports
    'reports:view', 'reports:export'
  ]

  return {
    id: '1',
    email: '<EMAIL>',
    username: 'admin',
    full_name: 'مدير النظام',
    role: 'admin',
    branch_id: '1',
    warehouse_id: '1',
    pos_id: '1',
    is_active: true,
    created_at: '2024-01-01',
    updated_at: '2024-01-01',
    permissions: adminPermissions
  }
}

export function hasPermission(user: AuthUser | null, permission: string): boolean {
  if (!user) return false
  return user.permissions.includes(permission)
}

export function hasAnyPermission(user: AuthUser | null, permissions: string[]): boolean {
  if (!user) return false
  return permissions.some(permission => user.permissions.includes(permission))
}

export function hasAllPermissions(user: AuthUser | null, permissions: string[]): boolean {
  if (!user) return false
  return permissions.every(permission => user.permissions.includes(permission))
}
