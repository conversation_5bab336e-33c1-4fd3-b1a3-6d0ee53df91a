import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/useAuth'
import { hasPermission, PERMISSIONS } from '@/lib/auth'
import Layout from '@/components/layout/Layout'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import {
  Plus,
  ShoppingCart,
  FileText,
  RotateCcw,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  AlertTriangle,
  Package,
  DollarSign,
  Users,
  Calendar,
  ArrowRight
} from 'lucide-react'
import { formatCurrency, formatDate } from '@/lib/utils'
import Link from 'next/link'

export default function SalesOverview() {
  const { user } = useAuth()
  const [loading, setLoading] = useState(true)
  const [salesData, setSalesData] = useState({
    orders: [],
    invoices: [],
    customers: [],
    stats: {
      totalQuotations: 0,
      pendingQuotations: 0,
      approvedQuotations: 0,
      expiredQuotations: 0,
      totalOrders: 0,
      pendingOrders: 0,
      confirmedOrders: 0,
      shippedOrders: 0,
      totalInvoices: 0,
      pendingInvoices: 0,
      paidInvoices: 0,
      overdueInvoices: 0,
      totalReturns: 0,
      pendingReturns: 0,
      processedReturns: 0,
      totalValue: 0,
      paidValue: 0,
      pendingValue: 0,
      returnValue: 0
    }
  })

  useEffect(() => {
    fetchSalesData()
  }, [])

  const fetchSalesData = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/postgres/data?type=sales-orders')
      const result = await response.json()

      if (result.success) {
        // حساب الإحصائيات من البيانات الحقيقية
        const orders = result.data || []
        const stats = {
          totalQuotations: 0,
          pendingQuotations: 0,
          approvedQuotations: 0,
          expiredQuotations: 0,
          totalOrders: orders.length,
          pendingOrders: orders.filter((o: any) => o.order_status === 'pending').length,
          confirmedOrders: orders.filter((o: any) => o.order_status === 'confirmed').length,
          shippedOrders: orders.filter((o: any) => o.order_status === 'shipped').length,
          totalInvoices: 0,
          pendingInvoices: 0,
          paidInvoices: 0,
          overdueInvoices: 0,
          totalReturns: 0,
          pendingReturns: 0,
          processedReturns: 0,
          totalValue: orders.reduce((sum: number, order: any) => sum + (order.final_amount || 0), 0),
          paidValue: orders.filter((o: any) => o.payment_status === 'paid').reduce((sum: number, order: any) => sum + (order.final_amount || 0), 0),
          pendingValue: orders.filter((o: any) => o.payment_status === 'pending').reduce((sum: number, order: any) => sum + (order.final_amount || 0), 0),
          returnValue: 0
        }

        setSalesData({
          orders,
          invoices: [],
          customers: [],
          stats
        })
      }
    } catch (error) {
      console.error('خطأ في جلب بيانات المبيعات:', error)
    } finally {
      setLoading(false)
    }
  }



  // Recent activities
  const recentQuotations = [
    {
      id: '1',
      quotation_number: 'QT-2024-001',
      customer_name: 'شركة التقنية المتقدمة',
      status: 'pending',
      total_amount: 83000,
      created_at: '2024-01-25'
    },
    {
      id: '2',
      quotation_number: 'QT-2024-002',
      customer_name: 'مؤسسة الإلكترونيات الحديثة',
      status: 'approved',
      total_amount: 50300,
      created_at: '2024-01-24'
    },
    {
      id: '3',
      quotation_number: 'QT-2024-003',
      customer_name: 'شركة الأجهزة الذكية',
      status: 'expired',
      total_amount: 35980,
      created_at: '2024-01-23'
    }
  ]

  const recentInvoices = [
    {
      id: '1',
      invoice_number: 'INV-2024-001',
      customer_name: 'شركة التقنية المتقدمة',
      status: 'pending',
      total_amount: 83000,
      due_date: '2024-02-20'
    },
    {
      id: '2',
      invoice_number: 'INV-2024-002',
      customer_name: 'مؤسسة الإلكترونيات الحديثة',
      status: 'paid',
      total_amount: 50300,
      due_date: '2024-02-05'
    },
    {
      id: '3',
      invoice_number: 'INV-2024-003',
      customer_name: 'شركة الأجهزة الذكية',
      status: 'overdue',
      total_amount: 35980,
      due_date: '2024-01-25'
    }
  ]

  const topCustomers = [
    { name: 'شركة التقنية المتقدمة', orders: 45, value: 890000 },
    { name: 'مؤسسة الإلكترونيات الحديثة', orders: 38, value: 720000 },
    { name: 'شركة الأجهزة الذكية', orders: 32, value: 580000 },
    { name: 'متجر الكمبيوتر المتقدم', orders: 28, value: 450000 },
    { name: 'شركة الحلول التقنية', orders: 25, value: 380000 }
  ]

  // Helper functions
  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending': return 'في الانتظار'
      case 'approved': return 'معتمد'
      case 'confirmed': return 'مؤكد'
      case 'shipped': return 'تم الشحن'
      case 'paid': return 'مدفوعة'
      case 'overdue': return 'متأخرة'
      case 'expired': return 'منتهية الصلاحية'
      default: return 'غير محدد'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800'
      case 'approved': return 'bg-blue-100 text-blue-800'
      case 'confirmed': return 'bg-green-100 text-green-800'
      case 'shipped': return 'bg-green-100 text-green-800'
      case 'paid': return 'bg-green-100 text-green-800'
      case 'overdue': return 'bg-red-100 text-red-800'
      case 'expired': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  if (!hasPermission(user, PERMISSIONS.SALES_VIEW)) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold text-gray-900">غير مصرح</h2>
          <p className="text-gray-600">ليس لديك صلاحية لعرض وحدة المبيعات</p>
        </div>
      </Layout>
    )
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">المبيعات</h1>
            <p className="text-muted-foreground">
              لوحة تحكم شاملة لإدارة المبيعات والعملاء
            </p>
          </div>
          <div className="flex space-x-2 space-x-reverse">
            {hasPermission(user, PERMISSIONS.SALES_CREATE) && (
              <>
                <Link href="/sales/quotations">
                  <Button variant="outline">
                    <FileText className="h-4 w-4 mr-2" />
                    عرض سعر جديد
                  </Button>
                </Link>
                <Link href="/sales/invoices">
                  <Button>
                    <FileText className="h-4 w-4 mr-2" />
                    فاتورة مبيعات جديدة
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>

        {/* Quick Navigation */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/sales/quotations">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">عروض الأسعار</CardTitle>
                <FileText className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{salesData.stats.totalQuotations}</div>
                <p className="text-xs text-muted-foreground">
                  {salesData.stats.pendingQuotations} في الانتظار
                </p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/sales/orders">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">طلبات المبيعات</CardTitle>
                <ShoppingCart className="h-4 w-4 text-green-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{salesData.stats.totalOrders}</div>
                <p className="text-xs text-muted-foreground">
                  {salesData.stats.pendingOrders} في الانتظار
                </p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/sales/invoices">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">فواتير المبيعات</CardTitle>
                <FileText className="h-4 w-4 text-purple-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{salesData.stats.totalInvoices}</div>
                <p className="text-xs text-muted-foreground">
                  {salesData.stats.pendingInvoices} تحتاج دفع
                </p>
              </CardContent>
            </Card>
          </Link>

          <Link href="/sales/returns">
            <Card className="hover:shadow-md transition-shadow cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">مرتجعات المبيعات</CardTitle>
                <RotateCcw className="h-4 w-4 text-red-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{salesData.stats.totalReturns}</div>
                <p className="text-xs text-muted-foreground">
                  {salesData.stats.pendingReturns} في الانتظار
                </p>
              </CardContent>
            </Card>
          </Link>
        </div>

        {/* Financial Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">إجمالي قيمة المبيعات</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(salesData.stats.totalValue)}</div>
              <p className="text-xs text-muted-foreground">إجمالي المبيعات</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المبلغ المحصل</CardTitle>
              <DollarSign className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{formatCurrency(salesData.stats.paidValue)}</div>
              <p className="text-xs text-muted-foreground">
                {salesData.stats.totalValue > 0 ? Math.round((salesData.stats.paidValue / salesData.stats.totalValue) * 100) : 0}% من الإجمالي
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">المبلغ المستحق</CardTitle>
              <Clock className="h-4 w-4 text-yellow-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">{formatCurrency(salesData.stats.pendingValue)}</div>
              <p className="text-xs text-muted-foreground">يجب تحصيله</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">قيمة المرتجعات</CardTitle>
              <TrendingDown className="h-4 w-4 text-red-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-red-600">{formatCurrency(salesData.stats.returnValue)}</div>
              <p className="text-xs text-muted-foreground">
                {salesData.stats.totalValue > 0 ? Math.round((salesData.stats.returnValue / salesData.stats.totalValue) * 100) : 0}% من الإجمالي
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Quotations */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>أحدث عروض الأسعار</CardTitle>
              <Link href="/sales/quotations">
                <Button variant="ghost" size="sm">
                  عرض الكل
                  <ArrowRight className="h-4 w-4 mr-2" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentQuotations.map((quotation) => (
                  <div key={quotation.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{quotation.quotation_number}</div>
                      <div className="text-sm text-gray-500">{quotation.customer_name}</div>
                      <div className="text-xs text-gray-400">{formatDate(quotation.created_at)}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(quotation.total_amount)}</div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(quotation.status)}`}>
                        {getStatusText(quotation.status)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Invoices */}
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>أحدث فواتير المبيعات</CardTitle>
              <Link href="/sales/invoices">
                <Button variant="ghost" size="sm">
                  عرض الكل
                  <ArrowRight className="h-4 w-4 mr-2" />
                </Button>
              </Link>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentInvoices.map((invoice) => (
                  <div key={invoice.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="font-medium">{invoice.invoice_number}</div>
                      <div className="text-sm text-gray-500">{invoice.customer_name}</div>
                      <div className="text-xs text-gray-400">استحقاق: {formatDate(invoice.due_date)}</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{formatCurrency(invoice.total_amount)}</div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(invoice.status)}`}>
                        {getStatusText(invoice.status)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Customers */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 space-x-reverse">
              <Users className="h-5 w-5" />
              <span>أهم العملاء</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>اسم العميل</TableHead>
                    <TableHead>عدد الطلبات</TableHead>
                    <TableHead>إجمالي القيمة</TableHead>
                    <TableHead>متوسط قيمة الطلب</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {topCustomers.map((customer, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-blue-600 font-medium text-sm">{index + 1}</span>
                          </div>
                          <span className="font-medium">{customer.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>{customer.orders}</TableCell>
                      <TableCell className="font-medium">{formatCurrency(customer.value)}</TableCell>
                      <TableCell>{formatCurrency(customer.value / customer.orders)}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  )
}
